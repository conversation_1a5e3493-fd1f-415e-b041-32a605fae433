import React, { useState, useEffect } from 'react';
import {
  Users,
  Building2,
  Bar<PERSON><PERSON>3,
  T<PERSON>dingUp,
  UserCheck,
  Briefcase,
  Calendar,
  Activity
} from "lucide-react";
import { HiOutlineSparkles } from "react-icons/hi";
import { toast } from "react-hot-toast";

const AdminDashboard = () => {
  const [stats, setStats] = useState({
    totalEmployers: 0,
    totalCandidates: 2847,
    activeEmployers: 0,
    activeCandidates: 1923,
    totalCreditsUsed: 1247,
    totalVisumesCreated: 3891
  });
  const [loading, setLoading] = useState(true);

  // Fetch real employer and candidate stats
  useEffect(() => {
    const fetchAllStats = async () => {
      setLoading(true);
      try {
        // Fetch employer data
        const employerResponse = await fetch(`${import.meta.env.VITE_APP_HOST}/api/v1/admin/employers`);
        if (!employerResponse.ok) {
          throw new Error(`Employer API error! status: ${employerResponse.status}`);
        }
        const employerResult = await employerResponse.json();
        const employers = employerResult.data || [];

        // Fetch candidate data
        const candidateResponse = await fetch(`${import.meta.env.VITE_APP_HOST}/api/v1/admin/candidates`);
        if (!candidateResponse.ok) {
          throw new Error(`Candidate API error! status: ${candidateResponse.status}`);
        }
        const candidateResult = await candidateResponse.json();
        const candidates = candidateResult.data?.candidates || [];

        // Fetch visume data
        const visumeResponse = await fetch(`${import.meta.env.VITE_APP_HOST}/api/v1/admin/visumes`);
        if (!visumeResponse.ok) {
          throw new Error(`Visume API error! status: ${visumeResponse.status}`);
        }
        const visumeResult = await visumeResponse.json();
        const visumes = visumeResult.data?.visumes || [];

        // Calculate employer stats
        const activeEmployers = employers.filter(emp => emp.membershipStatus === 'active').length;

        // Calculate candidate stats
        const activeCandidates = candidates.filter(candidate =>
          candidate.membershipStatus === 'active' && candidate.currentVisumeCount > 0
        ).length;

        // Calculate visume stats (only active/non-deleted visumes)
        const totalVisumesCreated = visumeResult.data?.active || visumes.filter(v => !v.is_deleted).length;

        // Update all stats
        setStats({
          totalEmployers: employers.length,
          activeEmployers: activeEmployers,
          totalCandidates: candidates.length,
          activeCandidates: activeCandidates,
          totalCreditsUsed: 1247, // Keep this static for now as it requires complex calculation
          totalVisumesCreated: totalVisumesCreated
        });

      } catch (error) {
        console.error("Error fetching admin stats:", error);
        toast.error("Failed to load dashboard statistics");
      } finally {
        setLoading(false);
      }
    };

    fetchAllStats();
  }, []);

  // Enhanced Metric Card Component with Membership Management Styling
  const MetricCard = ({ title, value, icon: Icon, trend, color }) => (
    <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 p-6 transition-all duration-300 hover:shadow-2xl hover:scale-105 group overflow-hidden">
      <div className="flex flex-col items-center text-center">
        <div 
          className="p-4 rounded-xl shadow-lg mb-4 group-hover:scale-110 transition-transform duration-300"
          style={{
            background: `linear-gradient(135deg, ${color}, ${color}dd)`,
            boxShadow: `0 4px 20px ${color}40`
          }}
        >
          <Icon className="w-6 h-6 text-white" />
        </div>
        <div>
          <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2 font-manrope">
            {title}
          </p>
          <p className="text-3xl font-bold text-gray-900 dark:text-white mb-2 font-sora">
            {loading ? (
              <div className="animate-pulse h-8 w-16 bg-gray-200 dark:bg-gray-700 rounded mx-auto"></div>
            ) : (
              value.toLocaleString()
            )}
          </p>
          {trend && (
            <div className="flex items-center justify-center">
              <TrendingUp className="w-4 h-4 mr-1 text-green-500" />
              <span className="text-sm font-medium text-green-500 font-manrope">
                +{trend}% from last month
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );



  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6 mb-12">
          <MetricCard
            title="Total Employers"
            value={stats.totalEmployers}
            icon={Building2}
            color="#3B82F6"
          />
          <MetricCard
            title="Active Employers"
            value={stats.activeEmployers}
            icon={UserCheck}
            color="#10B981"
          />
          <MetricCard
            title="Total Candidates"
            value={stats.totalCandidates}
            icon={Users}
            color="#8B5CF6"
          />
          <MetricCard
            title="Active Candidates"
            value={stats.activeCandidates}
            icon={Briefcase}
            color="#F59E0B"
          />
          <MetricCard
            title="Visumes Created"
            value={stats.totalVisumesCreated}
            icon={HiOutlineSparkles}
            color="#06B6D4"
          />
        </div>

        {/* Overview Header */}
        <div className="mb-12">
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center gap-4">
              <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-3 rounded-xl">
                <BarChart3 className="w-6 h-6 text-white" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white font-sora">
                  Admin Dashboard Overview
                </h2>
                <p className="text-gray-600 dark:text-gray-400 font-manrope">
                  Real-time platform statistics and insights
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Platform Overview Content */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700">
          <div className="p-8">
            <div className="flex items-center justify-between mb-8">
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white font-sora">
                Platform Overview
              </h2>
              <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-3 rounded-xl">
                <Activity className="w-6 h-6 text-white" />
              </div>
            </div>

              {/* Recent Activity Section */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div className="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800 p-8 rounded-2xl border border-gray-200 dark:border-gray-600 shadow-lg">
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center gap-3 font-sora">
                    <div className="bg-gradient-to-r from-green-500 to-emerald-600 p-2 rounded-lg">
                      <Activity className="w-5 h-5 text-white" />
                    </div>
                    Recent Activity
                  </h3>
                  <div className="space-y-6">
                    <div className="flex items-center justify-between p-4 bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700">
                      <span className="text-sm text-gray-600 dark:text-gray-400 font-manrope">
                        New employer registrations
                      </span>
                      <span className="font-bold text-green-600 dark:text-green-400 font-manrope bg-green-50 dark:bg-green-900/30 px-3 py-1 rounded-full">
                        +12 today
                      </span>
                    </div>
                    <div className="flex items-center justify-between p-4 bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700">
                      <span className="text-sm text-gray-600 dark:text-gray-400 font-manrope">
                        New candidate profiles
                      </span>
                      <span className="font-bold text-blue-600 dark:text-blue-400 font-manrope bg-blue-50 dark:bg-blue-900/30 px-3 py-1 rounded-full">
                        +45 today
                      </span>
                    </div>
                    <div className="flex items-center justify-between p-4 bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700">
                      <span className="text-sm text-gray-600 dark:text-gray-400 font-manrope">
                        Credits consumed
                      </span>
                      <span className="font-bold text-purple-600 dark:text-purple-400 font-manrope bg-purple-50 dark:bg-purple-900/30 px-3 py-1 rounded-full">
                        89 today
                      </span>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800 p-8 rounded-2xl border border-gray-200 dark:border-gray-600 shadow-lg">
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center gap-3 font-sora">
                    <div className="bg-gradient-to-r from-orange-500 to-red-600 p-2 rounded-lg">
                      <Calendar className="w-5 h-5 text-white" />
                    </div>
                    Platform Summary
                  </h3>
                  <div className="space-y-4">
                    <div className="p-6 rounded-xl bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-sm">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-semibold text-gray-900 dark:text-white font-manrope mb-1">
                            Total Platform Users
                          </h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400 font-manrope">
                            {loading ? 'Loading...' : `${stats.totalEmployers + stats.totalCandidates} registered users`}
                          </p>
                        </div>
                        <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-2 rounded-lg">
                          <Users className="w-5 h-5 text-white" />
                        </div>
                      </div>
                    </div>
                    <div className="p-6 rounded-xl bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-sm">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-semibold text-gray-900 dark:text-white font-manrope mb-1">
                            Active Users
                          </h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400 font-manrope">
                            {loading ? 'Loading...' : `${stats.activeEmployers + stats.activeCandidates} active users`}
                          </p>
                        </div>
                        <div className="bg-green-600 p-2 rounded-lg">
                          <Activity className="w-5 h-5 text-white" />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;