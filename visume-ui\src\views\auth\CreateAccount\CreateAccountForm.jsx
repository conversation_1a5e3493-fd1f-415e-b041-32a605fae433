import React from "react";
import {
  Briefcase,
  ChartBar,
  Trophy,
  Upload,
  Users,
  X,
  Check,
  FileText,
  User,
  UserCheck,
} from "lucide-react";

const CreateAccountForm = ({
  isProcessing,
  navigate,
  LogoImage,
  videoRes,
  profilePicPreview,
  imageError,
  setImageError,
  profilePic,
  profilePicError,
  errors,
  handleProfilePicChange,
  profilePicInputRef,
  handleRemoveProfilePic,
  formData,
  setFormData,
  handleInputChange,
  emailValidation,
  selectedLanguages,
  isLanguageOpen,
  setIsLanguageOpen,
  languageOptions,
  languageDropdownRef,
  toggleLanguage,
  selectedLocations,
  isLocationOpen,
  setIsLocationOpen,
  locationOptions,
  locationDropdownRef,
  toggleLocation,
  handlePhoneNumberChange,
  showSendOTPButton,
  otpVerified,
  handleSendOTPClick,
  otp,
  otpError,
  handleOTPChange,
  handleVerifyOTP,
  handleFileUpload,
  uploadedFile,
  showOTPModal,
  setShowOTPModal,
  phoneAvailable,
  phoneCheckLoading,
  phoneCheckMsg,
  handleSubmit,
}) => (
  <div className="relative min-h-screen bg-blue-50/30 px-4 py-4 sm:px-6 sm:py-6" style={{ fontFamily: 'Sora, sans-serif' }}>
    {/* Font imports */}
    <style>{`
      @import url('https://fonts.googleapis.com/css2?family=Manrope:wght@400;500;600;700;800&family=Sora:wght@300;400;500;600;700&display=swap');
    `}</style>
    {/* Loading Overlay */}
    {isProcessing && (
      <div className="bg-black/40 fixed inset-0 z-50 flex items-center justify-center backdrop-blur-sm">
        <div className="w-full max-w-sm rounded-2xl bg-white p-8 text-center shadow-2xl border border-gray-100">
          <div className="mb-4 flex justify-center">
            <div className="h-12 w-12 animate-spin rounded-full border-4 border-blue-200 border-t-blue-600"></div>
          </div>
          <h3 className="mb-2 text-lg font-medium text-gray-900" style={{ fontFamily: 'Manrope, sans-serif' }}>
            Creating your profile
          </h3>
          <p className="text-sm text-gray-600" style={{ fontFamily: 'Sora, sans-serif' }}>
            Processing your information...
          </p>
        </div>
      </div>
    )}
    <div className="mx-auto max-w-7xl">
      {/* Header */}
      <div className="mb-6 sm:mb-8 flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0">
        <div className="flex items-center gap-3">
          <img src="/visume-logo-new.png" alt="Visume logo" className="h-8 w-8 sm:h-9 sm:w-9" />
          <span className="text-lg sm:text-xl font-semibold text-gray-900">
            Visume
          </span>
        </div>
        <div className="text-xs sm:text-sm text-gray-600" style={{ fontFamily: 'Sora, sans-serif' }}>
          Already have an account?{" "}
          <button
            onClick={() => navigate("/candidate/sign-in")}
            className="font-medium text-blue-600 hover:text-blue-700 transition-colors"
            style={{ fontFamily: 'Sora, sans-serif' }}
          >
            Sign in
          </button>
        </div>
      </div>

      <div className="grid gap-6 lg:gap-8 lg:grid-cols-[2fr,1fr]">
        {/* Main Form */}
        <div className="rounded-2xl border border-blue-200 bg-white p-4 sm:p-6 lg:p-8 shadow-sm">
          <div className="mb-4 sm:mb-6">
            <h1 className="text-xl sm:text-2xl font-semibold text-gray-900 mb-2" style={{ fontFamily: 'Manrope, sans-serif' }}>
              Create your profile
            </h1>
            <p className="text-gray-600 text-sm sm:text-base" style={{ fontFamily: 'Sora, sans-serif' }}>
              Join thousands of professionals creating impactful video resumes
            </p>
          </div>

          <form
            className="space-y-4 sm:space-y-6"
            onSubmit={handleSubmit}
            autoComplete="off"
          >
            {/* Personal Information Section */}
            <div className="space-y-4">
              <div className="flex items-center gap-2 pb-2 mb-3">
                <div className="w-6 h-6 bg-blue-50 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 font-medium text-xs">1</span>
                </div>
                <h3 className="text-sm font-medium text-gray-700 uppercase tracking-wide" style={{ fontFamily: 'Manrope, sans-serif' }}>Personal Information</h3>
              </div>
              {/* Profile Picture Upload */}
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-900" style={{ fontFamily: 'Manrope, sans-serif' }}>
                  Profile Picture{" "}
                  <span className="text-gray-500 font-normal" style={{ fontFamily: 'Sora, sans-serif' }}>(optional)</span>
                </label>
                <input
                  type="file"
                  accept="image/jpeg, image/png, image/jpg"
                  className="hidden"
                  id="profile-pic-upload-input"
                  onChange={handleProfilePicChange}
                  ref={profilePicInputRef}
                />
                <div
                  className="group cursor-pointer rounded-xl border-2 border-dashed border-gray-200 bg-gray-50 p-4 text-center transition-all hover:border-blue-300 hover:bg-blue-50"
                  onClick={() =>
                    document.getElementById("profile-pic-upload-input").click()
                  }
                >
                  {profilePicPreview && !imageError ? (
                    <div className="flex flex-col items-center">
                      <div className="relative">
                        <img
                          src={
                            typeof profilePicPreview === "string"
                              ? profilePicPreview
                              : URL.createObjectURL(profilePicPreview)
                          }
                          alt="Profile Preview"
                          className="mx-auto mb-3 h-20 w-20 rounded-full border-2 border-blue-200 object-cover shadow-md"
                          onError={() => setImageError(true)}
                        />
                        <button
                          type="button"
                          aria-label="Remove profile picture"
                          className="absolute -right-1 -top-1 rounded-full bg-red-100 p-1.5 shadow-sm transition-colors hover:bg-red-200"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRemoveProfilePic();
                            setImageError(false);
                          }}
                        >
                          <X className="h-3 w-3 text-red-600" />
                        </button>
                      </div>
                      <p className="text-xs text-gray-600 font-medium">
                        {profilePic?.name}
                      </p>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center">
                      <div className="mb-3 rounded-full bg-gray-100 p-4 group-hover:bg-blue-100 transition-colors">
                        <svg
                          className="h-8 w-8 text-gray-400 group-hover:text-blue-500 transition-colors"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                      </div>
                      <p className="text-sm font-medium text-gray-700 mb-1" style={{ fontFamily: 'Sora, sans-serif' }}>Add a profile photo</p>
                      <p className="text-xs text-gray-500" style={{ fontFamily: 'Sora, sans-serif' }}>
                        JPG, JPEG, PNG • Max 5MB
                      </p>
                    </div>
                  )}
              </div>
              {(profilePicError || errors.profilePic) && (
                <p className="mt-2 text-xs text-red-500">
                  {profilePicError || errors.profilePic}
                </p>
              )}
            </div>
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-900" style={{ fontFamily: 'Manrope, sans-serif' }}>
                  Full name <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  name="fullName"
                  placeholder="Enter your full name"
                  className="w-full rounded-xl border border-gray-300 bg-gray-50/50 px-3 py-2.5 sm:px-4 sm:py-3 text-gray-900 placeholder-gray-500 transition-all focus:border-blue-500 focus:bg-white focus:outline-none focus:ring-3 focus:ring-blue-500/20 focus:shadow-sm"
                  value={formData.fullName}
                  onChange={handleInputChange}
                  required
                />
                {errors.fullName && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.fullName}
                  </p>
                )}
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium text-gray-900" style={{ fontFamily: 'Manrope, sans-serif' }}>
                  Email address <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <input
                    type="email"
                    name="email"
                    placeholder="Enter your email address"
                    className={`w-full rounded-xl border bg-gray-50/50 px-3 py-2.5 sm:px-4 sm:py-3 text-gray-900 placeholder-gray-500 transition-all focus:bg-white focus:outline-none focus:ring-3 focus:shadow-sm ${
                      emailValidation.isChecking
                        ? "border-blue-300 focus:border-blue-500 focus:ring-blue-500/20"
                        : emailValidation.isValid === null
                        ? "border-gray-300 focus:border-blue-500 focus:ring-blue-500/20"
                        : emailValidation.isValid
                        ? "border-green-400 focus:border-green-500 focus:ring-green-500/20"
                        : "border-red-400 focus:border-red-500 focus:ring-red-500/20"
                    }`}
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                  />
                  {emailValidation.isValid && (
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                      <Check className="h-4 w-4 text-green-500" />
                    </div>
                  )}
                </div>
                {/* Email validation feedback */}
                {emailValidation.message && (
                  <div className={`mt-1 flex items-center gap-1 text-sm ${
                    emailValidation.isChecking
                      ? "text-blue-600"
                      : emailValidation.isValid
                      ? "text-green-600"
                      : "text-red-600"
                  }`}>
                    {emailValidation.isChecking ? (
                      <div className="h-3 w-3 animate-spin rounded-full border-2 border-blue-600 border-t-transparent"></div>
                    ) : emailValidation.isValid ? (
                      <Check className="h-3 w-3" />
                    ) : (
                      <X className="h-3 w-3" />
                    )}
                    {emailValidation.message}
                  </div>
                )}
                {errors.email && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.email}
                  </p>
                )}
              </div>
              
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-900" style={{ fontFamily: 'Manrope, sans-serif' }}>
                  Password <span className="text-red-500">*</span>
                </label>
                <input
                  type="password"
                  name="password"
                  placeholder="Create a strong password"
                  className="w-full rounded-xl border border-gray-300 bg-gray-50/50 px-3 py-2.5 sm:px-4 sm:py-3 text-gray-900 placeholder-gray-500 transition-all focus:border-blue-500 focus:bg-white focus:outline-none focus:ring-3 focus:ring-blue-500/20 focus:shadow-sm"
                  value={formData.password}
                  onChange={handleInputChange}
                  required
                  minLength={6}
                />
                {errors.password && (
                  <p className="mt-1 text-sm text-red-600" style={{ fontFamily: 'Sora, sans-serif' }}>
                    {errors.password}
                  </p>
                )}
              </div>
            </div>

            {/* Contact Information Section */}
            <div className="space-y-4">
              <div className="flex items-center gap-2 pb-2 mb-3">
                <div className="w-6 h-6 bg-blue-50 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 font-medium text-xs">2</span>
                </div>
                <h3 className="text-sm font-medium text-gray-700 uppercase tracking-wide" style={{ fontFamily: 'Manrope, sans-serif' }}>Contact Information</h3>
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium text-gray-900" style={{ fontFamily: 'Manrope, sans-serif' }}>
                  Mobile number <span className="text-red-500">*</span>
                </label>
                <div className="flex rounded-xl border border-gray-300 bg-gray-50/50 transition-all focus-within:border-blue-500 focus-within:bg-white focus-within:ring-3 focus-within:ring-blue-500/20 focus-within:shadow-sm">
                  <span className="inline-flex items-center px-3 sm:px-4 text-gray-600 text-sm font-medium border-r border-gray-300">
                    +91
                  </span>
                  <input
                    type="tel"
                    name="mobile"
                    maxLength="10"
                    inputMode="numeric"
                    pattern="[0-9]*"
                    placeholder="Enter your mobile number"
                    className="w-full rounded-r-xl px-3 py-2.5 sm:px-4 sm:py-3 text-gray-900 placeholder-gray-500 bg-transparent focus:outline-none"
                    value={formData.mobile}
                    onChange={handlePhoneNumberChange}
                    required
                  />
                </div>
                {errors.mobile && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.mobile}
                  </p>
                )}
                {/* Live phone availability feedback (mirrors email feedback style) */}
                {(phoneCheckLoading || phoneAvailable !== null || phoneCheckMsg) && (
                  <div
                    className={`mt-1 flex items-center gap-1 text-sm ${
                      phoneCheckLoading
                        ? "text-blue-600"
                        : phoneAvailable === false
                        ? "text-red-600"
                        : phoneAvailable === true
                        ? "text-green-600"
                        : "text-gray-600"
                    }`}
                  >
                    {phoneCheckLoading ? (
                      <div className="h-3 w-3 animate-spin rounded-full border-2 border-blue-600 border-t-transparent"></div>
                    ) : phoneAvailable === true ? (
                      <Check className="h-3 w-3" />
                    ) : phoneAvailable === false ? (
                      <X className="h-3 w-3" />
                    ) : null}
                    <span>
                      {phoneCheckLoading
                        ? "Checking phone availability..."
                        : phoneAvailable === false
                        ? phoneCheckMsg || "Phone number already exists. Please use a different number."
                        : phoneAvailable === true
                        ? "Phone number is available"
                        : phoneCheckMsg || ""}
                    </span>
                  </div>
                )}
                
                {showSendOTPButton && !otpVerified && (
                  <button
                    onClick={handleSendOTPClick}
                    className="mt-3 inline-flex items-center gap-2 rounded-xl bg-blue-600 px-4 py-2.5 text-sm font-medium text-white shadow-sm transition-all hover:bg-blue-700 hover:shadow-md focus:outline-none focus:ring-3 focus:ring-blue-500/30"
                    style={{ fontFamily: 'Sora, sans-serif' }}
                  >
                    Send verification code
                  </button>
                )}
                
                {otpVerified && (
                  <div className="mt-2 inline-flex items-center gap-2 text-sm text-green-600" style={{ fontFamily: 'Sora, sans-serif' }}>
                    <Check className="h-4 w-4" />
                    Phone number verified!
                  </div>
                )}
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium text-gray-900" style={{ fontFamily: 'Manrope, sans-serif' }}>
                  Gender <span className="text-red-500">*</span>
                </label>
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
                  {[
                    { value: "male", label: "Male", icon: "♂" },
                    { value: "female", label: "Female", icon: "♀" },
                    { value: "other", label: "Other", icon: UserCheck }
                  ].map((option) => (
                    <button
                      key={option.value}
                      type="button"
                      className={`flex items-center justify-center gap-2 rounded-xl border px-4 py-3 text-sm font-medium transition-all ${
                        formData.gender === option.value
                          ? "border-blue-500 bg-blue-50 text-blue-700 shadow-sm"
                          : "border-gray-200 bg-white text-gray-700 hover:bg-blue-50/50 hover:border-blue-300"
                      }`}
                      onClick={() => {
                        setFormData({ ...formData, gender: option.value });
                        if (typeof setErrors === "function")
                          setErrors({ ...errors, gender: "" });
                      }}
                    >
                      {typeof option.icon === 'string' ? (
                        <span className="text-xl font-black" style={{ fontWeight: 900 }}>{option.icon}</span>
                      ) : (
                        <option.icon className="h-4 w-4" />
                      )}
                      <span style={{ fontFamily: 'Sora, sans-serif' }}>{option.label}</span>
                    </button>
                  ))}
                </div>
                {errors.gender && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.gender}
                  </p>
                )}
              </div>
            </div>

            {/* Preferences Section */}
            <div className="space-y-4">
              <div className="flex items-center gap-2 pb-2 mb-3">
                <div className="w-6 h-6 bg-blue-50 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 font-medium text-xs">3</span>
                </div>
                <h3 className="text-sm font-medium text-gray-700 uppercase tracking-wide" style={{ fontFamily: 'Manrope, sans-serif' }}>Preferences</h3>
              </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <div className="w-full" ref={languageDropdownRef}>
                <label
                  htmlFor="language-select"
                  className="mb-2 block text-sm font-semibold text-gray-700" style={{ fontFamily: 'Manrope, sans-serif' }}
                >
                  Languages known <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <button
                    type="button"
                    id="language-select"
                    className="flex w-full items-center justify-between rounded-lg border-2 border-blue-200 bg-white px-4 py-2 text-left shadow-lg transition focus:outline-none focus:ring-2 focus:ring-blue-500"
                    onClick={() => setIsLanguageOpen(!isLanguageOpen)}
                    aria-haspopup="listbox"
                    aria-expanded={isLanguageOpen}
                  >
                    <span style={{ fontFamily: 'Sora, sans-serif' }}>
                      {selectedLanguages.length > 0
                        ? `${selectedLanguages.length} selected`
                        : "Select languages"}
                    </span>
                    <span className="pointer-events-none flex items-center">
                      <svg
                        className="h-5 w-5 text-blue-400"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                        aria-hidden="true"
                      >
                        <path
                          fillRule="evenodd"
                          d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </span>
                  </button>
                  {isLanguageOpen && (
                    <ul
                      className="absolute z-10 mt-2 max-h-60 w-full overflow-auto rounded-lg border border-blue-200 bg-white py-1 text-base shadow-lg ring-1 ring-blue-200 ring-opacity-10 focus:outline-none sm:text-sm"
                      role="listbox"
                      aria-labelledby="language-select"
                      tabIndex={-1}
                    >
                      {languageOptions.map((language) => (
                        <li
                          key={language}
                          className={`${
                            selectedLanguages.includes(language)
                              ? "bg-blue-50 text-blue-900"
                              : "text-gray-900"
                          } relative cursor-pointer select-none py-2 pl-3 pr-9 transition hover:bg-blue-50`}
                          role="option"
                          aria-selected={selectedLanguages.includes(language)}
                          onClick={() => toggleLanguage(language)}
                        >
                          <span
                            className={`block truncate ${
                              selectedLanguages.includes(language)
                                ? "font-semibold"
                                : "font-normal"
                            }`}
                          >
                            {language}
                          </span>
                          {selectedLanguages.includes(language) && (
                            <span className="absolute inset-y-0 right-0 flex items-center pr-4 text-blue-600">
                              <Check className="h-5 w-5" aria-hidden="true" />
                            </span>
                          )}
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
                {selectedLanguages.length > 0 && (
                  <div className="mt-2 flex flex-wrap gap-2">
                    {selectedLanguages.map((lang) => (
                      <span
                        key={lang}
                        className="inline-flex items-center rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-blue-800"
                      >
                        {lang}
                        <button
                          type="button"
                          className="ml-1 inline-flex h-4 w-4 flex-shrink-0 items-center justify-center rounded-full text-blue-400 hover:bg-blue-200 hover:text-blue-500 focus:bg-blue-500 focus:text-white focus:outline-none"
                          onClick={() => toggleLanguage(lang)}
                        >
                          <span className="sr-only">Remove {lang}</span>
                          <X className="h-3 w-3" />
                        </button>
                      </span>
                    ))}
                  </div>
                )}
                {errors.languages && (
                  <p className="mt-2 text-xs text-red-500">
                    {errors.languages}
                  </p>
                )}
              </div>

              <div className="w-full" ref={locationDropdownRef}>
                <label
                  htmlFor="location-select"
                  className="mb-2 block text-sm font-semibold text-gray-700" style={{ fontFamily: 'Manrope, sans-serif' }}
                >
                  Preferred Locations <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <button
                    type="button"
                    id="location-select"
                    className="flex w-full items-center justify-between rounded-lg border-2 border-blue-200 bg-white px-4 py-2 text-left shadow-lg transition focus:outline-none focus:ring-2 focus:ring-blue-500"
                    onClick={() => setIsLocationOpen(!isLocationOpen)}
                    aria-haspopup="listbox"
                    aria-expanded={isLocationOpen}
                  >
                    <span style={{ fontFamily: 'Sora, sans-serif' }}>
                      {selectedLocations.length > 0
                        ? `${selectedLocations.length} selected`
                        : "Select locations"}
                    </span>
                    <span className="pointer-events-none flex items-center">
                      <svg
                        className="h-5 w-5 text-blue-400"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                        aria-hidden="true"
                      >
                        <path
                          fillRule="evenodd"
                          d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </span>
                  </button>
                  {isLocationOpen && (
                    <ul
                      className="absolute z-10 mt-2 max-h-60 w-full overflow-auto rounded-lg border border-blue-200 bg-white py-1 text-base shadow-lg ring-1 ring-blue-200 ring-opacity-10 focus:outline-none sm:text-sm"
                      role="listbox"
                      aria-labelledby="location-select"
                      tabIndex={-1}
                    >
                      {locationOptions.map((location) => (
                        <li
                          key={location}
                          className={`${
                            selectedLocations.includes(location)
                              ? "bg-blue-50 text-blue-900"
                              : "text-gray-900"
                          } relative cursor-pointer select-none py-2 pl-3 pr-9 transition hover:bg-blue-50`}
                          role="option"
                          aria-selected={selectedLocations.includes(location)}
                          onClick={() => toggleLocation(location)}
                        >
                          <span
                            className={`block truncate ${
                              selectedLocations.includes(location)
                                ? "font-semibold"
                                : "font-normal"
                            }`}
                          >
                            {location}
                          </span>
                          {selectedLocations.includes(location) && (
                            <span className="absolute inset-y-0 right-0 flex items-center pr-4 text-blue-600">
                              <Check className="h-5 w-5" aria-hidden="true" />
                            </span>
                          )}
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
                {selectedLocations.length > 0 && (
                  <div className="mt-2 flex flex-wrap gap-2">
                    {selectedLocations.map((loc) => (
                      <span
                        key={loc}
                        className="inline-flex items-center rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-blue-800"
                      >
                        {loc}
                        <button
                          type="button"
                          className="ml-1 inline-flex h-4 w-4 flex-shrink-0 items-center justify-center rounded-full text-blue-400 hover:bg-blue-200 hover:text-blue-500 focus:bg-blue-500 focus:text-white focus:outline-none"
                          onClick={() => toggleLocation(loc)}
                        >
                          <span className="sr-only">Remove {loc}</span>
                          <X className="h-3 w-3" />
                        </button>
                      </span>
                    ))}
                  </div>
                )}
                {errors.locations && (
                  <p className="mt-2 text-xs text-red-500">
                    {errors.locations}
                  </p>
                )}
              </div>
            </div>
            </div>

            {/* Documents Section */}
            <div className="space-y-4">
              <div className="flex items-center gap-2 pb-2 mb-3">
                <div className="w-6 h-6 bg-blue-50 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 font-medium text-xs">4</span>
                </div>
                <h3 className="text-sm font-medium text-gray-700 uppercase tracking-wide" style={{ fontFamily: 'Manrope, sans-serif' }}>Documents</h3>
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium text-gray-900" style={{ fontFamily: 'Manrope, sans-serif' }}>
                  Resume Upload <span className="text-red-500">*</span>
                </label>
                <input
                  type="file"
                  accept=".pdf, .doc, .docx"
                  className="hidden"
                  onChange={handleFileUpload}
                  id="resume-upload-input"
                />
                <div
                  className="group cursor-pointer rounded-xl border-2 border-dashed border-gray-300 bg-gray-50 p-6 text-center transition-all hover:border-blue-400 hover:bg-blue-50"
                  onClick={() =>
                    document.getElementById("resume-upload-input").click()
                  }
                >
                  {uploadedFile ? (
                    <div className="flex flex-col items-center">
                      <div className="mb-4 rounded-full bg-green-100 p-3">
                        <FileText className="h-8 w-8 text-green-600" />
                      </div>
                      <p className="font-medium text-gray-800 mb-1" style={{ fontFamily: 'Sora, sans-serif' }}>
                        {uploadedFile.name}
                      </p>
                      <p className="text-xs text-gray-500" style={{ fontFamily: 'Sora, sans-serif' }}>
                        File uploaded successfully
                      </p>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center">
                      <div className="mb-4 rounded-full bg-gray-100 p-4 group-hover:bg-blue-100 transition-colors">
                        <Upload className="h-8 w-8 text-gray-400 group-hover:text-blue-500 transition-colors" />
                      </div>
                      <p className="text-sm font-medium text-gray-700 mb-1" style={{ fontFamily: 'Sora, sans-serif' }}>
                        Upload your resume
                      </p>
                      <p className="text-xs text-gray-500" style={{ fontFamily: 'Sora, sans-serif' }}>
                        PDF, DOC, DOCX • Max 5MB
                      </p>
                    </div>
                  )}
                </div>
                {errors.resume && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.resume}
                  </p>
                )}
              </div>
            </div>

            {/* Final Section */}
            <div className="space-y-4 pt-2">
              <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
                <input
                  type="checkbox"
                  className="mt-0.5 rounded border-gray-300 text-blue-600 focus:ring-blue-500 focus:ring-offset-0"
                  defaultChecked
                />
                <div>
                  <p className="text-sm text-gray-800 font-medium mb-1" style={{ fontFamily: 'Manrope, sans-serif' }}>
                    Stay updated with opportunities
                  </p>
                  <p className="text-xs text-gray-600" style={{ fontFamily: 'Sora, sans-serif' }}>
                    Get job recommendations and career insights via email and SMS
                  </p>
                </div>
              </div>

              <p className="text-xs text-gray-600 text-center" style={{ fontFamily: 'Sora, sans-serif' }}>
                By creating an account, you agree to our{" "}
                <a href="#" className="text-blue-600 hover:text-blue-700 underline">
                  Terms of Service
                </a>{" "}
                and{" "}
                <a href="#" className="text-blue-600 hover:text-blue-700 underline">
                  Privacy Policy
                </a>
              </p>

              <button
                type="submit"
                className="w-full rounded-xl bg-gradient-to-r from-blue-600 to-blue-700 py-3.5 px-6 text-base font-semibold text-white shadow-lg transition-all hover:from-blue-700 hover:to-blue-800 hover:shadow-xl hover:scale-[1.01] focus:outline-none focus:ring-3 focus:ring-blue-500/30"
                style={{ fontFamily: 'Manrope, sans-serif' }}
              >
                Create your Visume profile
              </button>
            </div>
          </form>
        </div>

        {/* Sidebar */}
        <div className="lg:sticky lg:top-8 h-fit">
          <div className="rounded-2xl border border-blue-200 bg-white p-4 sm:p-6 shadow-sm">
            <div className="mb-6 text-center">
              <img
                src={videoRes}
                alt="Video resume illustration"
                className="w-full max-w-xs mx-auto"
              />
            </div>
            
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2 text-center" style={{ fontFamily: 'Manrope, sans-serif' }}>
                Why choose Visume?
              </h3>
              <p className="text-gray-600 text-center text-sm" style={{ fontFamily: 'Sora, sans-serif' }}>
                Join thousands of professionals who've transformed their careers
              </p>
            </div>
            
            <div className="space-y-5">
              {[
                {
                  icon: ChartBar,
                  color: "bg-blue-100/80 text-blue-700",
                  title: "3x higher response rate",
                  description: "Get noticed faster than traditional resumes"
                },
                {
                  icon: Users,
                  color: "bg-green-100/80 text-green-700", 
                  title: "500+ partner recruiters",
                  description: "Direct access to top companies and startups"
                },
                {
                  icon: Trophy,
                  color: "bg-amber-100/80 text-amber-700",
                  title: "AI-powered optimization",
                  description: "Smart recommendations to improve your profile"
                },
                {
                  icon: Briefcase,
                  color: "bg-purple-100/80 text-purple-700",
                  title: "Exclusive opportunities",
                  description: "Access to premium job listings first"
                }
              ].map((feature, index) => (
                <div key={index} className="flex items-start gap-4">
                  <div className={`flex-shrink-0 rounded-xl p-3 shadow-sm ${feature.color}`}>
                    <feature.icon className="h-5 w-5" strokeWidth={2.5} />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1 text-sm" style={{ fontFamily: 'Manrope, sans-serif' }}>
                      {feature.title}
                    </h4>
                    <p className="text-sm text-gray-600 leading-relaxed" style={{ fontFamily: 'Sora, sans-serif' }}>
                      {feature.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>

    {/* OTP Modal */}
    {showOTPModal && (
      <div className="fixed inset-0 z-50 flex items-center justify-center backdrop-blur-sm px-4">
        <div className="absolute inset-0 bg-black/60"></div>
        <div className="relative z-50 w-full max-w-sm sm:max-w-md rounded-3xl border border-white/20 bg-white/95 backdrop-blur-md p-6 sm:p-8 shadow-2xl">
          <button
            onClick={() => setShowOTPModal(false)}
            className="absolute right-4 top-4 rounded-full p-2 text-gray-400 transition-colors hover:bg-gray-100 hover:text-gray-600"
          >
            <X className="h-5 w-5" />
          </button>
          
          <div className="text-center mb-8">
            <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100">
              <svg className="h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </div>
            <h3 className="text-xl font-bold text-gray-800 mb-2" style={{ fontFamily: 'Manrope, sans-serif' }}>
              Verify your number
            </h3>
            <p className="text-gray-600 text-sm" style={{ fontFamily: 'Sora, sans-serif' }}>
              Enter the 4-digit code sent to your mobile number
            </p>
          </div>
          
          <div className="mb-6 flex justify-center gap-3">
            {otp.map((digit, index) => (
              <input
                key={index}
                id={`otp-${index}`}
                type="text"
                inputMode="numeric"
                maxLength={1}
                value={digit}
                onChange={(e) => handleOTPChange(index, e.target.value)}
                className="h-14 w-14 rounded-xl border-2 border-gray-200 text-center text-xl font-semibold text-gray-800 transition-all focus:border-blue-500 focus:outline-none focus:ring-4 focus:ring-blue-500/10"
                aria-label={`OTP digit ${index + 1}`}
              />
            ))}
          </div>
          
          {otpError && (
            <div className="mb-6 rounded-xl bg-red-50 p-3 text-center">
              <p className="text-sm text-red-600 flex items-center justify-center gap-1">
                <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                {otpError}
              </p>
            </div>
          )}
          
          <div className="space-y-3">
            <button
              onClick={handleVerifyOTP}
              className="w-full rounded-xl bg-blue-600 py-3.5 text-lg font-semibold text-white shadow-sm transition-colors hover:bg-blue-700 focus:outline-none focus:ring-4 focus:ring-blue-500/20"
              style={{ fontFamily: 'Manrope, sans-serif' }}
            >
              Verify OTP
            </button>
            <button
              onClick={handleSendOTPClick}
              className="w-full text-sm text-blue-600 font-medium hover:text-blue-700 transition-colors"
              style={{ fontFamily: 'Sora, sans-serif' }}
            >
              Didn't receive the code? Resend OTP
            </button>
          </div>
        </div>
      </div>
    )}
  </div>
);

export default CreateAccountForm;
