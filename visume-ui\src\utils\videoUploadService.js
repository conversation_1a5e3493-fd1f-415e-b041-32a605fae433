/**
 * VideoUploadService - Simple R2 direct upload service
 * 
 * Uses Cloudflare R2 for direct video uploads via presigned URLs
 * Completely replaces the old S3 multipart upload system
 */

import { uploadVideoToR2, validateVideoFile, testR2Connection } from './r2UploadService.js';

/**
 * Upload video file to Cloudflare R2
 * 
 * @param {File} file - The video file to upload
 * @param {string} vpid - Video profile ID (used as filename)
 * @param {Object} options - Upload options
 * @param {Function} options.onProgress - Progress callback
 * @param {Function} options.onError - Error callback
 * @param {Function} options.setLoadingText - Loading text setter
 * @returns {Promise<string>} - Final video URL
 */
export async function uploadVideo(file, vpid, options = {}) {
  const {
    onProgress = () => {},
    onError = () => {},
    setLoadingText = () => {}
  } = options;

  console.log('VideoUploadService: Starting R2 upload', {
    fileSize: file.size,
    fileName: vpid,
    contentType: file.type,
    fileSizeMB: Math.round(file.size / (1024 * 1024) * 100) / 100
  });

  // Validate the video file first
  const validation = validateVideoFile(file);
  if (!validation.valid) {
    const errorMessage = `Invalid video file: ${validation.errors.join(', ')}`;
    console.error(errorMessage);
    onError(new Error(errorMessage));
    throw new Error(errorMessage);
  }

  // Ensure filename has .webm extension
  const filename = vpid.includes('.') ? vpid : `${vpid}.webm`;
  
  // Use R2 direct upload
  return uploadVideoToR2(file, filename, { onProgress, onError, setLoadingText });
}

/**
 * Get upload method info
 */
export function getUploadMethodInfo(fileSize) {
  return {
    method: 'R2 direct upload',
    service: 'Cloudflare R2',
    fileSize,
    fileSizeMB: Math.round(fileSize / (1024 * 1024) * 100) / 100,
    maxSizeGB: 2, // 2GB max file size
    advantages: [
      'Direct upload - no chunking needed',
      'Better reliability than multipart',
      'Faster upload speeds',
      'Cloudflare global network'
    ]
  };
}

// Re-export functions from R2 service
export { validateVideoFile, testR2Connection } from './r2UploadService.js';

export default {
  uploadVideo,
  getUploadMethodInfo,
  validateVideoFile,
  testR2Connection
};
