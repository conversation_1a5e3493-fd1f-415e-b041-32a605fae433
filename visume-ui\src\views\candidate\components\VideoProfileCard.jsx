import React, { useState } from "react";
import toast from "react-hot-toast";
import {
  <PERSON>,
  Eye,
  Clipboard<PERSON>he<PERSON>,
  Users,
  Trash2,
  ArrowRight,
  VideoIcon,
  Star,
  Clock,
  TrendingUp
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { HiArrowRight } from "react-icons/hi";

const VideoProfileCard = ({ profile, toggleVideoProfilePopup }) => {
  const [loader, setLoader] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [resumeIdToDelete, setResumeIdToDelete] = useState(null);

  const deleteVisume = async (id) => {
    try {
      setLoader(true);
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/video-resume/${id}`,
        {
          method: "DELETE",
        }
      );

      if (response.ok) {
        toast.success("Video profile deleted successfully");
        window.location.reload(); // Ensure the page is reloaded to reflect changes
      } else {
        console.error("Failed to delete video profile");
      }
    } catch (error) {
      console.error("Error:", error);
    } finally {
      setLoader(false);
      setIsModalOpen(false); // Close modal after delete attempt
    }
  };

  const handleDeleteClick = (id) => {
    setResumeIdToDelete(id);
    setIsModalOpen(true);
  };

  const navigate = useNavigate();

  return (
    <div className="group relative bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-4 hover:shadow-lg hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-300 transform hover:-translate-y-1">
      {/* Compact Layout */}
      <div className="flex items-center justify-between gap-3">
        {/* Left: Icon + Role + Skills */}
        <div className="flex items-center gap-4 flex-1 min-w-0">
          <div className="flex-shrink-0">
            <VideoIcon className="w-5 h-5 text-gray-600 dark:text-gray-400" />
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-3 mb-2">
              <h3 className="text-base font-semibold text-gray-900 dark:text-white truncate">
                {profile.role}
              </h3>
              {/* Status Badge */}
              {(profile.status?.toLowerCase() === "notsubmitted" ||
               profile.status?.toLowerCase() === "started" ||
               !profile.status ||
               profile.status.trim() === "") ? (
                <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300">
                  Draft
                </span>
              ) : (
                <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300">
                  Active
                </span>
              )}
            </div>
            
            {/* Skills */}
            <div className="flex items-center gap-2 mb-2">
              {profile.skills.slice(0, 3).map((skill, index) => (
                <span
                  key={index}
                  className="px-2 py-0.5 rounded text-gray-600 dark:text-gray-300 text-xs bg-gray-100 dark:bg-gray-700"
                >
                  {skill}
                </span>
              ))}
              {profile.skills.length > 3 && (
                <span className="px-2 py-0.5 rounded text-gray-500 dark:text-gray-400 text-xs bg-gray-200 dark:bg-gray-600">
                  +{profile.skills.length - 3}
                </span>
              )}
            </div>
            
            {/* Stats */}
            <div className="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
              <div className="flex items-center gap-1">
                <Eye className="w-3.5 h-3.5" />
                <span>10 views</span>
              </div>
              <div className="flex items-center gap-1">
                <Star className="w-3.5 h-3.5" />
                <span>3 shortlists</span>
              </div>
            </div>
          </div>
        </div>

        {/* Right: Action Buttons */}
        <div className="flex items-center gap-2 flex-shrink-0">
          {(profile.status?.toLowerCase() === "notsubmitted" ||
           profile.status?.toLowerCase() === "started" ||
           !profile.status ||
           profile.status.trim() === "") ? (
            <>
              <button
                onClick={() => navigate(`/candidate/interview/${profile.vpid}`)}
                className="px-3 py-1.5 bg-gray-900 dark:bg-gray-100 hover:bg-gray-800 dark:hover:bg-gray-200 text-white dark:text-gray-900 text-xs font-medium rounded transition-all duration-200"
              >
                Continue
              </button>
              <button
                onClick={() => handleDeleteClick(profile.vpid)}
                className="p-1.5 text-gray-400 hover:text-red-500 transition-colors"
              >
                <Trash2 className="w-3.5 h-3.5" />
              </button>
            </>
          ) : (
            <button
              onClick={() => navigate(`/candidate/videoResume/${profile.vpid}`)}
              className="px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 text-xs font-medium rounded transition-all duration-200"
            >
              View
            </button>
          )}
        </div>
      </div>

      {/* Enhanced Confirmation Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 flex items-center justify-center bg-black/50 backdrop-blur-sm z-50">
          <div className="bg-white dark:bg-gray-900 rounded-xl shadow-2xl max-w-md w-full mx-4 overflow-hidden">
            {/* Modal Header */}
            <div className="p-6 border-b border-gray-200 dark:border-gray-800">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-red-100 dark:bg-red-900/50 rounded-lg">
                  <Trash2 className="w-5 h-5 text-red-600 dark:text-red-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Delete Video Resume
                </h3>
              </div>
            </div>
            
            {/* Modal Content */}
            <div className="p-6">
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                Are you sure you want to delete this video resume? The video file will be permanently removed, but your profile data will be retained for administrative purposes.
              </p>
              
              {/* Action Buttons */}
              <div className="flex items-center justify-end gap-3">
                <button
                  onClick={() => setIsModalOpen(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-lg transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={() => deleteVisume(resumeIdToDelete)}
                  disabled={loader}
                  className="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg transition-colors flex items-center gap-2"
                >
                  {loader ? (
                    <>
                      <svg
                        className="h-4 w-4 animate-spin text-white"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                          fill="none"
                        />
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        />
                      </svg>
                      Deleting...
                    </>
                  ) : (
                    <>
                      <Trash2 className="w-4 h-4" />
                      Delete
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default VideoProfileCard;