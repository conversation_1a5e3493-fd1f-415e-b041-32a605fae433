import React, {
  useEffect,
  useState,
  useImperative<PERSON><PERSON><PERSON>,
  forwardRef,
} from "react";
import { useNavigate } from "react-router-dom";
import { Modal } from "react-responsive-modal";
import "react-responsive-modal/styles.css";

import Card from "../../../components/card";
import { HiOutlineSparkles, HiOutlineShare, HiOutlineTrash } from "react-icons/hi";
import { Users } from "lucide-react";
const JobDescriptionList = forwardRef(({ emp_id, onUploadClick }, ref) => {
  const [jobDescriptions, setJobDescriptions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [skillsOpenIdx, setSkillsOpenIdx] = useState(null);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [jobToDelete, setJobToDelete] = useState(null);
  const [inviteModalOpen, setInviteModalOpen] = useState(false);
  const [selectedJobForInvite, setSelectedJobForInvite] = useState(null);
  const [inviteLink, setInviteLink] = useState("");
  const [copied, setCopied] = useState(false);
  const navigate = useNavigate();

  const handleDelete = async (id) => {
    try {
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/job-description/${id}`,
        { method: "DELETE" }
      );
      if (response.ok) {
        setJobDescriptions((prev) => prev.filter((jd) => jd._id !== id));
      } else {
        alert("Failed to delete job description.");
      }
    } catch {
      alert("Error deleting job description.");
    }
    setDeleteModalOpen(false);
    setJobToDelete(null);
  };

  const openDeleteModal = (id) => {
    setJobToDelete(id);
    setDeleteModalOpen(true);
  };

  const closeDeleteModal = () => {
    setDeleteModalOpen(false);
    setJobToDelete(null);
  };

  const fetchJobDescriptions = async () => {
    setLoading(true);
    try {
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/job-description/${emp_id}`
      );
      if (response.ok) {
        const data = await response.json();
        setJobDescriptions(data.jobDescriptions || []);
      }
    } catch (err) {
      setJobDescriptions([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (emp_id) fetchJobDescriptions();
  }, [emp_id]);

  useImperativeHandle(ref, () => ({
    refresh: fetchJobDescriptions,
  }));

  // Secure invite link generation using inviteId only
  const generateInviteLink = (jobDescription) => {
    const baseUrl = window.location.origin;
    // Assume jobDescription._id is used as inviteId
    return `${baseUrl}/candidate/create-visume?inviteId=${jobDescription._id}`;
  };

  const handleInviteCandidate = (jobDescription) => {
    const link = generateInviteLink(jobDescription);
    setInviteLink(link);
    setSelectedJobForInvite(jobDescription);
    setInviteModalOpen(true);
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(inviteLink);
      setCopied(true);
    } catch (err) {
      console.error("Failed to copy: ", err);
      // Fallback for older browsers
      const textArea = document.createElement("textarea");
      textArea.value = inviteLink;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand("copy");
      document.body.removeChild(textArea);
      alert("Invite link copied to clipboard!");
    }
  };

  const closeInviteModal = () => {
    setInviteModalOpen(false);
    setSelectedJobForInvite(null);
    setInviteLink("");
    setCopied(false);
  };

  if (loading) return <div>Loading job descriptions...</div>;
  if (!jobDescriptions.length)
    return (
      <Card extra="rounded-2xl border border-gray-200 bg-white shadow-sm p-8 flex flex-col items-center justify-center">
        <div className="w-12 h-12 rounded-xl bg-blue-50 border border-blue-100 flex items-center justify-center mb-4">
          <HiOutlineSparkles className="w-6 h-6 text-blue-600" />
        </div>
        <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
          No Job Descriptions Yet
        </h3>
        <p className="mb-6 text-center text-gray-500 text-sm max-w-xs">
          Upload a job description to find matching candidates using AI.
        </p>
        <button
          onClick={onUploadClick}
          className="inline-flex items-center rounded-xl bg-blue-600 px-6 py-3 text-sm font-semibold text-white transition-all hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          <HiOutlineSparkles className="mr-2 h-4 w-4" />
          Upload Job Description
        </button>
      </Card>
    );

  return (
    <div className="mb-6">
      <Modal
        open={deleteModalOpen}
        onClose={closeDeleteModal}
        center
        classNames={{ modal: "rounded-2xl p-8 max-w-md w-full" }}
        animationDuration={200}
      >
        <div className="flex flex-col items-center">
          <div className="mb-4 text-2xl font-bold text-red-600">
            Confirm Delete
          </div>
          <div className="mb-6 text-center text-gray-700">
            Are you sure you want to delete this job description? This action
            cannot be undone.
          </div>
          <div className="flex gap-4">
            <button
              className="rounded-lg bg-red-600 px-6 py-2 font-semibold text-white shadow transition hover:bg-red-700"
              onClick={() => handleDelete(jobToDelete)}
            >
              Yes, Delete
            </button>
            <button
              className="rounded-lg bg-gray-200 px-6 py-2 font-semibold text-gray-800 shadow transition hover:bg-gray-300"
              onClick={closeDeleteModal}
            >
              Cancel
            </button>
          </div>
        </div>
      </Modal>
      <div className="mb-6 flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
          All Job Descriptions
        </h3>
        <button
          onClick={onUploadClick}
          className="inline-flex items-center rounded-xl bg-blue-600 px-6 py-3 text-sm font-semibold text-white transition-all hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          <HiOutlineSparkles className="mr-2 h-4 w-4" />
          Upload Job Description
        </button>
      </div>
      <div className="max-h-96 overflow-auto">
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {jobDescriptions.map((jd, idx) => (
            <div
              key={jd._id || idx}
              className="mb-6 flex w-auto min-w-fit max-w-[350px] flex-col justify-between rounded-2xl border border-blue-200 bg-white p-6 shadow-sm transition-all duration-200 hover:shadow-md hover:border-blue-300 dark:bg-navy-900 dark:border-blue-400"
              title="View matching candidates"
            >
              {/* Header Row */}
              <div className="mb-4 flex items-center gap-3">
                <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-blue-50 border border-blue-100 text-lg font-bold text-blue-600">
                  {jd.role ? jd.role[0].toUpperCase() : "J"}
                </div>
                <div className="flex min-w-0 flex-col">
                  <span className="truncate text-lg font-semibold text-gray-900 dark:text-white mb-1">
                    {jd.role || "No Role"}
                  </span>
                  <span className="text-sm text-gray-500 font-medium">
                    {Array.isArray(jd.location)
                      ? jd.location.join(", ")
                      : jd.location}
                  </span>
                </div>
              </div>
              {/* Info Section */}
              <div className="mb-4 space-y-3">
                <div className="flex flex-wrap gap-2">
                  <span className="inline-flex items-center rounded-lg bg-blue-50 border border-blue-100 px-3 py-1.5 text-xs font-medium text-blue-700">
                    Experience: {jd.experience}
                  </span>
                  {jd.companyType && (
                    <span className="inline-flex items-center rounded-lg bg-green-50 border border-green-100 px-3 py-1.5 text-xs font-medium text-green-700">
                      {jd.companyType.charAt(0).toUpperCase() + jd.companyType.slice(1).replace("_", " ")}
                    </span>
                  )}
                </div>
                <div className="space-y-2">
                  <button
                    className="flex items-center gap-2 text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors"
                    onClick={(e) => {
                      e.stopPropagation();
                      setSkillsOpenIdx(skillsOpenIdx === idx ? null : idx);
                    }}
                  >
                    <span>Required Skills</span>
                    <svg
                      className={`h-4 w-4 transition-transform ${
                        skillsOpenIdx === idx ? "rotate-90" : "rotate-0"
                      }`}
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M9 5l7 7-7 7"
                      />
                    </svg>
                  </button>
                  {skillsOpenIdx === idx && (
                    <div className="rounded-lg border border-gray-200 bg-gray-50 p-3">
                      <div className="flex flex-wrap gap-1">
                        {(Array.isArray(jd.skills) ? jd.skills : [jd.skills]).map(
                          (skill, i) => (
                            <span
                              key={i}
                              className="inline-block rounded-md bg-white border border-gray-200 px-2 py-1 text-xs font-medium text-gray-700"
                            >
                              {skill}
                            </span>
                          )
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
              {/* Action Buttons */}
              <div className="mt-auto border-t border-gray-100 pt-4">
                <div className="flex gap-2">
                  {/* View Details - Primary Action */}
                  <button
                    className="flex flex-1 items-center justify-center gap-2 rounded-xl bg-gradient-to-r from-blue-600 to-blue-700 px-4 py-2 text-sm font-bold text-white shadow-sm transition-all duration-200 hover:from-blue-700 hover:to-blue-800 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transform hover:scale-[1.02]"
                    onClick={() =>
                      navigate(`/employer/job-description/${jd._id}`, {
                        state: { job: jd },
                      })
                    }
                    title="View Job Candidates"
                  >
                    <Users className="h-4 w-4" />
                    Candidates
                  </button>
                  
                  {/* Share Button */}
                  <button
                    className="flex items-center justify-center gap-1.5 rounded-lg border border-emerald-200 bg-emerald-50 px-4 py-2 text-sm font-bold text-emerald-700 transition-all duration-200 hover:bg-emerald-100 hover:border-emerald-300 hover:shadow-sm focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-1 min-w-[80px]"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleInviteCandidate(jd);
                    }}
                    title="Share job with candidates"
                  >
                    <HiOutlineShare className="h-4 w-4" />
                    Share
                  </button>
                  
                  {/* Delete Button - Icon Only */}
                  <button
                    className="flex items-center justify-center rounded-lg border border-red-200 bg-red-50 px-3 py-2 text-red-700 transition-all duration-200 hover:bg-red-100 hover:border-red-300 hover:shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-1"
                    onClick={(e) => {
                      e.stopPropagation();
                      openDeleteModal(jd._id);
                    }}
                    title="Delete job description"
                  >
                    <HiOutlineTrash className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Invite Modal */}
      <Modal
        open={inviteModalOpen}
        onClose={closeInviteModal}
        center
        classNames={{ modal: "rounded-2xl p-8 max-w-2xl w-full" }}
        animationDuration={200}
      >
        <div className="flex flex-col">
          <div className="mb-4 flex items-center gap-3">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
              <HiOutlineShare className="h-6 w-6 text-green-600" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">
                Invite Candidate
              </h2>
              <p className="text-sm text-gray-600">
                Share this link with candidates to invite them for this role
              </p>
            </div>
          </div>

          {selectedJobForInvite && (
            <div className="mb-4 rounded-lg bg-gray-50 p-4">
              <h3 className="font-semibold text-gray-900">
                {selectedJobForInvite.role}
              </h3>
              <div className="mt-2 flex flex-wrap gap-2">
                {(Array.isArray(selectedJobForInvite.skills)
                  ? selectedJobForInvite.skills
                  : [selectedJobForInvite.skills]
                ).map((skill, idx) => (
                  <span
                    key={idx}
                    className="inline-flex items-center rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-blue-700"
                  >
                    {skill}
                  </span>
                ))}
                {selectedJobForInvite.companyType && (
                  <span className="inline-flex items-center rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-700">
                    Company: {selectedJobForInvite.companyType.charAt(0).toUpperCase() + selectedJobForInvite.companyType.slice(1).replace("_", " ")}
                  </span>
                )}
              </div>
            </div>
          )}

          <div className="mb-4">
            <label className="mb-2 block text-sm font-medium text-gray-700">
              Candidate Invite Link
            </label>
            <div className="flex gap-2">
              <input
                type="text"
                value={inviteLink}
                readOnly
                className="flex-1 rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
              />
              <button
                onClick={copyToClipboard}
                className="rounded-lg bg-blue-600 px-4 py-2 text-sm font-semibold text-white shadow-sm transition-colors hover:bg-blue-700"
              >
                {copied ? "Copied!" : "Copy Link"}
              </button>
            </div>
          </div>

          <div className="mb-4 rounded-lg bg-blue-50 p-4">
            <h4 className="mb-2 font-medium text-blue-900">How it works:</h4>
            <ul className="space-y-1 text-sm text-blue-800">
              <li>
                • Candidate clicks the link and lands on the Visume creation
                page
              </li>
              <li>
                • Job details (role, skills, experience) are automatically
                filled
              </li>
              <li>
                • Candidate completes their profile and creates their Visume
              </li>
              <li>• You can view their application in your dashboard</li>
            </ul>
          </div>

          <div className="flex gap-3">
            <button
              className="rounded-lg bg-gray-200 px-6 py-2 font-semibold text-gray-800 shadow transition hover:bg-gray-300"
              onClick={closeInviteModal}
            >
              Close
            </button>
          </div>
        </div>
      </Modal>
    </div>
  );
});

export default JobDescriptionList;
