import React from 'react'
import { MapPin, Building2, Users, ExternalLink } from 'lucide-react'

const JobCard = ({job, iconUrl}) => {
  return (
    <div
      className="group relative bg-white dark:bg-gray-800/50 border border-blue-100 dark:border-gray-700/50 rounded-lg p-3 hover:bg-blue-50/30 dark:hover:bg-gray-800 hover:border-blue-200 dark:hover:border-gray-600 hover:shadow-sm transition-all duration-200 cursor-pointer"
      onClick={() => window.open(job.url, '_blank', 'noopener,noreferrer')}
    >
      {/* Header */}
      <div className="flex items-center gap-3 mb-2">
        <div className="relative flex-shrink-0">
          <img
            src={iconUrl}
            alt={job.company}
            className="w-7 h-7 rounded object-contain bg-blue-50 dark:bg-gray-700 p-1 border border-blue-200 dark:border-gray-600"
            onError={(e) => {
              e.target.style.display = 'none';
              e.target.nextSibling.style.display = 'flex';
            }}
          />
          <div className="hidden w-7 h-7 rounded bg-blue-600 dark:bg-blue-500 items-center justify-center text-white font-bold text-xs">
            {job.company?.charAt(0)?.toUpperCase()}
          </div>
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="text-sm font-medium text-gray-900 dark:text-white truncate">
            {job.title}
          </h3>
          <p className="text-xs text-gray-500 dark:text-gray-400 truncate">{job.company}</p>
        </div>
        <ExternalLink className="w-3 h-3 text-blue-400 flex-shrink-0" />
      </div>

      {/* Details */}
      <div className="flex items-center justify-between text-xs">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-1 text-gray-500 dark:text-gray-400">
            <MapPin className="w-3 h-3" />
            <span>{job.location || 'Remote'}</span>
          </div>
          <div className="flex items-center gap-1 text-gray-500 dark:text-gray-400">
            <Users className="w-3 h-3" />
            <span>{job.openings} openings</span>
          </div>
        </div>
        {job.type && (
          <span className="px-2 py-0.5 rounded-full text-xs font-medium bg-green-50 text-green-600 dark:bg-green-900/20 dark:text-green-400">
            {job.type}
          </span>
        )}
      </div>
    </div>
  )
}

export default JobCard