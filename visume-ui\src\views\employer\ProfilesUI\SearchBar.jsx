// SearchBar.jsx
import React, { useEffect, useState } from "react";
import { Search, Filter } from "lucide-react";
import { HiBriefcase, HiLocationMarker } from "react-icons/hi";
import AdvancedSearchModal from "../components/AdvancedSearchModal";
import { useLocation, useNavigate } from "react-router-dom";

const SearchBar = ({
  setProfiles,
  loading,
  setLoading,
  setSetShowLoadMore,
  getAllProfiles,
  setOldProfiles,
  oldProfiles,
  setViewInteraction,
}) => {
  const [location, setLocation] = useState("");
  const [role, setRole] = useState("");
  const [skills, setSkills] = useState("");
  const [focusedInput, setFocusedInput] = useState(null);
  const [selectedSkills, setSelectedSkills] = useState([]);
  const [showAdvancedModal, setShowAdvancedModal] = useState(false);
  const [advancedFilters, setAdvancedFilters] = useState({
    experience: "",
    expectedSalary: "",
    currentSalary: "",
    score: "",
  });
  const [countAdvanceFilters, setCountAdvanceFilters] = useState(0);

  const [roleSuggestion, setRoleSuggestion] = useState([]);
  const [skillSuggestion, setSkillSuggestion] = useState([]);
  const [locationSuggestion, setLocationSuggestion] = useState([]);

  const jobRoles = [
    "Software Developer/Engineer",
    "Java Developer",
    "Frontend Developer",
    "Backend Developer",
    "Full Stack Developer",
    "DevOps Engineer",
  ];

  const skillsList = [
    "JavaScript",
    "Python",
    "React",
    "Node.js",
    "CSS",
    "HTML",
    "Tailwind CSS",
    "Django",
    "Java",
    "Spring MVC",
  ];

  const locations = ["Bangalore", "Delhi", "Mumbai", "Hyderabad"];

  const query = new URLSearchParams(useLocation().search);
  const navigate = useNavigate();

  const handleAddSkill = (skill) => {
    if (skill && !selectedSkills.includes(skill)) {
      setSelectedSkills([...selectedSkills, skill]);
      setSkills("");
    }
  };

  const handleRemoveSkill = (skill) => {
    setSelectedSkills(selectedSkills.filter((s) => s !== skill));
  };

  const handleSearch = async () => {
    try {
      if (
        !location.trim() &&
        !role.trim() &&
        !selectedSkills.length &&
        !Object.values(advancedFilters).some((value) => value)
      ) {
        navigate(`filterCandidate`);
        setProfiles([]);
        getAllProfiles(1);
        return;
      }

      setLoading(true);
      const queryParams = new URLSearchParams({
        ...(location && { preferred_location: location }),
        ...(role && { role }),
        ...(selectedSkills.length && {
          selectedSkills: JSON.stringify(selectedSkills),
        }),
        shortlisted: "false",
        ...(advancedFilters.experience && {
          experience: advancedFilters.experience,
        }),
        ...(advancedFilters.expectedSalary && {
          expected_salary: advancedFilters.expectedSalary,
        }),
        ...(advancedFilters.currentSalary && {
          current_salary: advancedFilters.currentSalary,
        }),
        ...(advancedFilters.score && { score: advancedFilters.score }),
      });

      const data = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/filterCandidate?${queryParams.toString()}`
      );

      const res = await data.json();
      setProfiles(res.candidateProfiles);
      setOldProfiles((prev) => {
        const updatedProfiles = [...prev, ...res.candidateProfiles];
        return updatedProfiles.slice(-10);
      });
      navigate(`filterCandidate?${queryParams.toString()}`);
      setViewInteraction((prev) => prev + 1);
      setSetShowLoadMore(false);
    } catch (err) {
      console.error("Error fetching candidate profiles:", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    handleSearch();
    // eslint-disable-next-line
  }, [advancedFilters, selectedSkills]);

  useEffect(() => {
    let count = 0;
    for (const key in advancedFilters) {
      if (advancedFilters[key].trim()) {
        count += 1;
      }
    }
    setCountAdvanceFilters(count);
  }, [advancedFilters]);

  useEffect(() => {
    const preferredLocation = query.get("preferred_location") || "";
    const roleParam = query.get("role") || "";
    const experience = query.get("experience") || "";
    const expectedSalary = query.get("expected_salary") || "";
    const currentSalary = query.get("current_salary") || "";
    const score = query.get("score") || "";
    const skills = query.get("selectedSkills") || "[]";
    const decodedSkills = JSON.parse(decodeURIComponent(skills));

    setLocation(preferredLocation);
    setRole(roleParam);
    setAdvancedFilters({
      experience,
      expectedSalary,
      currentSalary,
      score,
    });
    setSelectedSkills(decodedSkills);
    // eslint-disable-next-line
  }, []);

  useEffect(() => {
    const advFilter = Object.values(advancedFilters).some((value) => value);
    if (
      !selectedSkills.length &&
      !role &&
      !location &&
      !advFilter &&
      oldProfiles.length
    ) {
      navigate(`filterCandidate`);
      setProfiles((prev) => {
        const updatedProfiles = [...prev, ...oldProfiles].slice(-10);
        const uniqueProfiles = [
          ...new Map(updatedProfiles.map((item) => [item.id, item])).values(),
        ];
        return uniqueProfiles.reverse();
      });
    }
    // eslint-disable-next-line
  }, [selectedSkills, role, location, advancedFilters, oldProfiles]);

  return (
    <div className="relative mx-auto mb-8 w-full max-w-5xl">
      <div className="relative overflow-visible rounded-2xl bg-white dark:bg-gray-800 border-2 border-blue-300 dark:border-blue-700">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-indigo-50/20 to-purple-50/30 dark:from-blue-900/20 dark:via-indigo-900/10 dark:to-purple-900/20 rounded-2xl"></div>
        
        <div className="relative z-10 p-6">
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
            <div className="flex-1 grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div className={`group relative flex items-center rounded-xl px-4 py-3.5 border-2 transition-all duration-300 ${
                focusedInput === "location"
                  ? "border-blue-500 bg-blue-50/50 dark:bg-blue-900/50"
                  : "border-blue-200 dark:border-gray-600 bg-white dark:bg-gray-700 hover:border-blue-300 dark:hover:border-blue-500"
              }`}>
                <HiLocationMarker
                  size={20}
                  className={`transition-colors duration-300 ${
                    focusedInput === "location" ? "text-blue-600 dark:text-blue-400" : "text-gray-500 dark:text-gray-400 group-hover:text-blue-500 dark:group-hover:text-blue-400"
                  }`}
                />
                <input
                  type="text"
                  placeholder="Enter location"
                  value={location}
                  onChange={(e) => {
                    setLocation(e.target.value);
                    if (e.target.value) {
                      const locationSuggest = locations.filter((val) =>
                        val.toLowerCase().includes(e.target.value.toLowerCase())
                      );
                      setLocationSuggestion(locationSuggest);
                    } else {
                      setLocationSuggestion([]);
                    }
                  }}
                  className="bg-transparent w-full py-1 pl-3 text-base focus:outline-none text-gray-800 dark:text-gray-200 placeholder-gray-400 dark:placeholder-gray-500 font-medium font-sora"
                  onFocus={() => setFocusedInput("location")}
                  onBlur={() => setFocusedInput(null)}
                />
              </div>
              {locationSuggestion && locationSuggestion.length ? (
                <div className="absolute top-20 left-0 z-50 flex max-h-60 w-full flex-col overflow-y-auto rounded-xl border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 shadow-xl backdrop-blur-sm">
                  {locationSuggestion.slice(0, 10).map((e, index) => (
                    <span
                      onClick={() => {
                        setLocation(e);
                        setLocationSuggestion([]);
                      }}
                      key={index}
                      className="cursor-pointer px-4 py-3 text-gray-700 dark:text-gray-300 transition-all duration-200 hover:bg-blue-50 dark:hover:bg-blue-900/50 hover:text-blue-700 dark:hover:text-blue-400 first:rounded-t-xl last:rounded-b-xl font-medium font-sora border-b border-gray-100 dark:border-gray-600 last:border-0"
                    >
                      {e}
                    </span>
                  ))}
                </div>
              ) : undefined}
              
              <div className={`group relative flex items-center rounded-xl px-4 py-3.5 border-2 transition-all duration-300 ${
                focusedInput === "role"
                  ? "border-blue-500 bg-blue-50/50 dark:bg-blue-900/50"
                  : "border-blue-200 dark:border-gray-600 bg-white dark:bg-gray-700 hover:border-blue-300 dark:hover:border-blue-500"
              }`}>
                <HiBriefcase
                  size={20}
                  className={`transition-colors duration-300 ${
                    focusedInput === "role" ? "text-blue-600 dark:text-blue-400" : "text-gray-500 dark:text-gray-400 group-hover:text-blue-500 dark:group-hover:text-blue-400"
                  }`}
                />
                <input
                  type="text"
                  placeholder="Job role"
                  value={role}
                  onChange={(e) => {
                    setRole(e.target.value);
                    if (e.target.value) {
                      const roleSuggest = jobRoles.filter((val) =>
                        val.toLowerCase().includes(e.target.value.toLowerCase())
                      );
                      setRoleSuggestion(roleSuggest);
                    } else {
                      setRoleSuggestion([]);
                    }
                  }}
                  className="bg-transparent w-full py-1 pl-3 text-base focus:outline-none text-gray-800 dark:text-gray-200 placeholder-gray-400 dark:placeholder-gray-500 font-medium font-sora"
                  onFocus={() => setFocusedInput("role")}
                  onBlur={() => setFocusedInput(null)}
                />
              </div>
              {roleSuggestion && roleSuggestion.length ? (
                <div className="absolute top-20 left-0 z-50 flex max-h-60 w-full flex-col overflow-y-auto rounded-xl border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 shadow-xl backdrop-blur-sm">
                  {roleSuggestion.slice(0, 10).map((e, index) => (
                    <span
                      onClick={() => {
                        setRole(e);
                        setRoleSuggestion([]);
                      }}
                      key={index}
                      className="cursor-pointer px-4 py-3 text-gray-700 dark:text-gray-300 transition-all duration-200 hover:bg-blue-50 dark:hover:bg-blue-900/50 hover:text-blue-700 dark:hover:text-blue-400 first:rounded-t-xl last:rounded-b-xl font-medium font-sora border-b border-gray-100 dark:border-gray-600 last:border-0"
                    >
                      {e}
                    </span>
                  ))}
                </div>
              ) : undefined}
              
              <div className={`group relative flex items-center rounded-xl px-4 py-3.5 border-2 transition-all duration-300 ${
                focusedInput === "skills"
                  ? "border-blue-500 bg-blue-50/50 dark:bg-blue-900/50"
                  : "border-blue-200 dark:border-gray-600 bg-white dark:bg-gray-700 hover:border-blue-300 dark:hover:border-blue-500"
              }`}>
                <Filter
                  size={20}
                  className={`transition-colors duration-300 ${
                    focusedInput === "skills" ? "text-blue-600 dark:text-blue-400" : "text-gray-500 dark:text-gray-400 group-hover:text-blue-500 dark:group-hover:text-blue-400"
                  }`}
                />
                <input
                  type="text"
                  placeholder="Add skills"
                  value={skills}
                  onChange={(e) => {
                    setSkills(e.target.value);
                    if (e.target.value) {
                      const skillSuggest = skillsList.filter((val) =>
                        val.toLowerCase().includes(e.target.value.toLowerCase())
                      );
                      setSkillSuggestion(skillSuggest);
                    } else {
                      setSkillSuggestion([]);
                    }
                  }}
                  className="bg-transparent w-full py-1 pl-3 text-base focus:outline-none text-gray-800 dark:text-gray-200 placeholder-gray-400 dark:placeholder-gray-500 font-medium font-sora"
                  onFocus={() => setFocusedInput("skills")}
                  onBlur={() => setFocusedInput(null)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      handleAddSkill(skills);
                    }
                  }}
                />
              </div>
              {skillSuggestion && skillSuggestion.length ? (
                <div className="absolute top-20 left-0 z-50 flex max-h-60 w-full flex-col overflow-y-auto rounded-xl border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 shadow-xl backdrop-blur-sm">
                  {skillSuggestion.slice(0, 10).map((e, index) => (
                    <span
                      onClick={() => {
                        setSkills(e);
                        setSkillSuggestion([]);
                      }}
                      key={index}
                      className="cursor-pointer px-4 py-3 text-gray-700 dark:text-gray-300 transition-all duration-200 hover:bg-blue-50 dark:hover:bg-blue-900/50 hover:text-blue-700 dark:hover:text-blue-400 first:rounded-t-xl last:rounded-b-xl font-medium font-sora border-b border-gray-100 dark:border-gray-600 last:border-0"
                    >
                      {e}
                    </span>
                  ))}
                </div>
              ) : undefined}
            </div>
            
            <div className="flex gap-3 sm:ml-4">
              <button
                onClick={() => setShowAdvancedModal(true)}
                className="relative flex items-center justify-center gap-2 rounded-xl border-2 border-blue-200 dark:border-blue-700 px-5 py-3.5 text-base font-medium text-gray-700 dark:text-gray-300 transition-all duration-300 hover:border-blue-300 dark:hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/50 hover:text-blue-700 dark:hover:text-blue-400 font-sora"
              >
                <Filter size={18} />
                Filters
                {countAdvanceFilters > 0 && (
                  <span className="absolute -right-2 -top-2 flex h-5 w-5 items-center justify-center rounded-full bg-red-500 text-xs font-bold text-white ring-2 ring-white dark:ring-gray-800">
                    {countAdvanceFilters}
                  </span>
                )}
              </button>
              <button
                onClick={loading ? undefined : handleSearch}
                className="flex items-center justify-center gap-2 rounded-xl bg-gradient-to-r from-blue-600 to-blue-700 dark:from-blue-700 dark:to-blue-800 px-6 py-3.5 text-base font-semibold text-white transition-all duration-300 hover:from-blue-700 hover:to-blue-800 dark:hover:from-blue-800 dark:hover:to-blue-900 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed font-sora"
                disabled={loading}
              >
                {loading ? (
                  <svg
                    className="h-5 w-5 animate-spin text-white"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                      fill="none"
                    />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    />
                  </svg>
                ) : (
                  <>
                    <Search size={18} />
                    Search
                  </>
                )}
              </button>
            </div>
          </div>
          
          {selectedSkills.length > 0 && (
            <div className="flex flex-wrap gap-2.5 pt-4 border-t border-gray-100 dark:border-gray-600">
              {selectedSkills.map((skill) => (
                <span
                  key={skill}
                  className="flex items-center rounded-lg bg-blue-50 dark:bg-blue-900/50 px-3 py-2 text-sm font-medium text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-700 transition-all duration-200 hover:bg-blue-100 dark:hover:bg-blue-900/70 font-sora"
                >
                  {skill}
                  <button
                    className="ml-2 text-blue-500 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-200 transition-colors duration-200 font-bold"
                    onClick={() => handleRemoveSkill(skill)}
                  >
                    ×
                  </button>
                </span>
              ))}
            </div>
          )}
        </div>
      </div>
      
      {Object.entries(advancedFilters).filter(([key, value]) => value).length > 0 && (
        <div className="mt-4 flex items-center justify-start gap-3">
          {Object.entries(advancedFilters)
            .filter(([key, value]) => value)
            .map(([key, value], index) => (
              <div
                key={index}
                className="relative flex items-center gap-2 rounded-lg bg-gray-800 dark:bg-gray-700 px-4 py-2 text-white shadow-md"
              >
                <span className="text-sm font-medium font-sora">
                  {`${key}: ${value}`}
                </span>
                <button
                  type="button"
                  className="absolute -right-2 -top-2 flex h-5 w-5 items-center justify-center rounded-full bg-red-500 text-xs font-bold text-white hover:bg-red-600 transition-all duration-200 ring-2 ring-white dark:ring-gray-800"
                  onClick={() => {
                    const newFilters = { ...advancedFilters };
                    newFilters[key] = "";
                    setAdvancedFilters(newFilters);
                  }}
                >
                  ×
                </button>
              </div>
            ))}
        </div>
      )}
      
      <AdvancedSearchModal
        isOpen={showAdvancedModal}
        onClose={() => setShowAdvancedModal(false)}
        advancedFilters={advancedFilters}
        setAdvancedFilters={setAdvancedFilters}
        onApplyFilters={(filters) => setAdvancedFilters(filters)}
      />
    </div>
  );
};

export default SearchBar;