import { useEffect, useState } from "react";
import {
  EyeIcon,
  EyeOffIcon,
  User,
  Lock,
  Bell,
  Shield,
  Upload,
  FileText,
  Check,
  Camera,
  Settings,
} from "lucide-react";
import Cookies from "js-cookie";
import avatar from "assets/img/avatars/avatar4.png";
import toast from "react-hot-toast";
import { useNavigate } from "react-router-dom";

const CandidateSettings = () => {
  const navigate = useNavigate();
  const cand_id = Cookies.get("candId");

  // Active tab state
  const [activeTab, setActiveTab] = useState("profile");

  // Profile state
  const [profileData, setProfileData] = useState({
    name: "",
    email: "",
    mobile: "",
    gender: "",
    language: "",
    location: "",
    profileImage: null,
  });

  // Password state
  const [passwordData, setPasswordData] = useState({
    oldPassword: "",
    newPassword: "",
    confirmPassword: "",
  });

  // Notification state
  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    jobAlerts: true,
    interviewReminders: true,
    marketingEmails: false,
  });

  // Privacy state
  const [privacySettings, setPrivacySettings] = useState({
    profileVisibility: "public",
    showContactInfo: true,
    allowDirectMessages: true,
  });

  // Loading states
  const [loading, setLoading] = useState({
    profile: false,
    password: false,
    notifications: false,
    privacy: false,
  });

  // Other states
  const [showPasswords, setShowPasswords] = useState({
    old: false,
    new: false,
    confirm: false,
  });
  const [uploadedFile, setUploadedFile] = useState(null);
  const [errors, setErrors] = useState({});
  const [isEditing, setIsEditing] = useState(false);

  // Fetch profile data
  useEffect(() => {
    const fetchProfileData = async () => {
      if (!cand_id) {
        toast.error("No Token Found, Please Login Again");
        navigate("/candidate/sign-in");
        return;
      }

      try {
        const response = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/candidate/${cand_id}`
        );
        const data = await response.json();

        if (data.candidateProfile?.[0]) {
          const candidate = data.candidateProfile[0];
          console.log("Candidate profile API response:", candidate);
          setProfileData({
            name: candidate.cand_name || "",
            email: candidate.cand_email || "",
            mobile: candidate.cand_mobile || "",
            language:
              candidate.languages_known?.replace(/^\["|"\]$/g, "") || "",
            location:
              candidate.preferred_location?.replace(/^\["|"\]$/g, "") || "",
            profileImage: candidate.profile_picture || null,
          });
        }
      } catch (error) {
        console.error("Failed to fetch profile data:", error);
        toast.error("Failed to load profile data");
      }
    };

    fetchProfileData();
  }, [cand_id, navigate]);

  // Handle password change
  const handlePasswordChange = async () => {
    if (
      !passwordData.oldPassword ||
      !passwordData.newPassword ||
      !passwordData.confirmPassword
    ) {
      toast.error("Please fill in all password fields.");
      return;
    }

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      toast.error("New passwords do not match.");
      return;
    }

    if (passwordData.newPassword.length < 8) {
      toast.error("Password must be at least 8 characters long.");
      return;
    }

    setLoading((prev) => ({ ...prev, password: true }));

    try {
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/changePassword`,
        {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            cand_id,
            password: passwordData.oldPassword,
            newPassword: passwordData.newPassword,
          }),
        }
      );

      const responseData = await response.json();

      if (!response.ok) {
        toast.error(responseData.message);
      } else {
        setPasswordData({
          oldPassword: "",
          newPassword: "",
          confirmPassword: "",
        });
        toast.success("Password updated successfully!");
      }
    } catch (error) {
      toast.error("An unexpected error occurred.");
      console.error("Error:", error);
    } finally {
      setLoading((prev) => ({ ...prev, password: false }));
    }
  };

  // Handle profile update
  const handleProfileUpdate = async () => {
    setLoading((prev) => ({ ...prev, profile: true }));

    try {
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/candidate/${cand_id}`,
        {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            cand_name: profileData.name,
            cand_email: profileData.email,
            cand_mobile: profileData.mobile,
            languages_known: profileData.language,
            preferred_location: profileData.location,
            profile_picture: profileData.profileImage,
          }),
        }
      );
      const data = await response.json();
      if (!response.ok) {
        toast.error(data.message || "Failed to update profile");
      } else {
        toast.success("Profile updated successfully!");
        setIsEditing(false);
      }
    } catch (error) {
      toast.error("Failed to update profile");
    } finally {
      setLoading((prev) => ({ ...prev, profile: false }));
    }
  };

  // Handle file upload
  const handleFileUpload = async (e) => {
    const file = e.target.files[0];
    if (file) {
      const isPdf = file.type === "application/pdf";
      if (isPdf && file.size <= 5 * 1024 * 1024) {
        // 5MB limit
        setErrors((prev) => ({ ...prev, resume: "" }));

        // Upload resume to server
        const formData = new FormData();
        formData.append("resume", file);
        formData.append("cand_id", cand_id);

        setLoading((prev) => ({ ...prev, profile: true }));
        try {
          const response = await fetch(
            `${import.meta.env.VITE_APP_HOST}/api/v1/candidate/uploadResume`,
            {
              method: "POST",
              body: formData,
            }
          );
          const data = await response.json();
          if (!response.ok) {
            setErrors((prev) => ({
              ...prev,
              resume: data.message || "Resume upload failed.",
            }));
          } else {
            setUploadedFile(file);
            toast.success("Resume uploaded successfully!");
          }
        } catch (error) {
          setErrors((prev) => ({
            ...prev,
            resume: "Resume upload failed.",
          }));
        } finally {
          setLoading((prev) => ({ ...prev, profile: false }));
        }
      } else {
        setErrors((prev) => ({
          ...prev,
          resume: "Please upload a valid PDF file under 5MB.",
        }));
        setUploadedFile(null);
      }
    }
  };

  // Handle profile image upload
  const handleProfileImageUpload = async (e) => {
    const file = e.target.files[0];
    if (file) {
      if (file.type.startsWith("image/") && file.size <= 2 * 1024 * 1024) {
        // 2MB limit
        const formData = new FormData();
        formData.append("profile_picture", file);
        formData.append("cand_id", cand_id);

        setLoading((prev) => ({ ...prev, profile: true }));
        try {
          const response = await fetch(
            `${import.meta.env.VITE_APP_HOST}/api/v1/candidate/uploadProfileImage`,
            {
              method: "POST",
              body: formData,
            }
          );
          const data = await response.json();
          if (!response.ok) {
            toast.error(data.message || "Profile image upload failed.");
          } else {
            setProfileData((prev) => ({
              ...prev,
              profileImage: data.profile_picture_url || prev.profileImage,
            }));
            toast.success("Profile image updated!");
          }
        } catch (error) {
          toast.error("Profile image upload failed.");
        } finally {
          setLoading((prev) => ({ ...prev, profile: false }));
        }
      } else {
        toast.error("Please upload a valid image file under 2MB.");
      }
    }
  };

  // Tab configuration
  const tabs = [
    { id: "profile", label: "Profile", icon: User },
    { id: "security", label: "Security", icon: Lock },
    { id: "notifications", label: "Notifications", icon: Bell },
    { id: "privacy", label: "Privacy", icon: Shield },
  ];

  return (
    <div className="p-3 max-w-6xl mx-auto">
      {/* Compact Header */}
      <div className="bg-white dark:bg-slate-800 rounded-2xl border border-slate-200 dark:border-slate-700 mb-6">
        <div className="flex items-center justify-between p-5">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-600 rounded-xl">
              <Settings className="h-5 w-5 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-slate-900 dark:text-white font-sora">
                Settings
              </h1>
              <p className="text-sm text-slate-600 dark:text-slate-400 font-manrope">
                Manage your account
              </p>
            </div>
          </div>
          <div className="hidden sm:flex items-center gap-2 bg-slate-50 dark:bg-slate-700 rounded-lg px-3 py-2">
            <User className="w-4 h-4 text-slate-500" />
            <div className="text-sm font-medium text-slate-900 dark:text-white">
              {profileData.name || 'Loading...'}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="bg-white dark:bg-slate-800 rounded-2xl border border-slate-200 dark:border-slate-700">
        {/* Compact Tab Navigation */}
        <div className="border-b border-slate-200 dark:border-slate-700 p-4">
          <nav className="flex flex-wrap gap-1">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              const isActive = activeTab === tab.id;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-all font-manrope ${
                    isActive
                      ? 'bg-blue-600 text-white shadow-md'
                      : 'text-slate-600 dark:text-slate-400 hover:bg-slate-100 dark:hover:bg-slate-700'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span className="hidden sm:block">{tab.label}</span>
                </button>
              );
            })}
          </nav>
        </div>
        <div className="p-4">

          {/* Content Area */}
          <div>
            {/* Profile Tab */}
            {activeTab === "profile" && (
              <div>
                {/* Compact Section Header */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <User className="w-4 h-4 text-blue-600" />
                    <h2 className="text-lg font-semibold text-slate-900 dark:text-white font-sora">
                      Profile Information
                    </h2>
                  </div>
                  <button
                    onClick={() =>
                      isEditing ? handleProfileUpdate() : setIsEditing(true)
                    }
                    disabled={loading.profile}
                    className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed font-manrope"
                  >
                    {loading.profile ? (
                      <>
                        <div className="border-t-transparent h-3 w-3 animate-spin rounded-full border-2 border-white"></div>
                        Saving...
                      </>
                    ) : isEditing ? (
                      <>
                        <Check className="h-3 w-3" />
                        Save
                      </>
                    ) : (
                      "Edit"
                    )}
                  </button>
                </div>

                {/* Compact Profile Image Section */}
                <div className="bg-slate-50 dark:bg-slate-700/50 rounded-xl p-4 mb-4">
                  <div className="flex items-center gap-4">
                    <div className="relative">
                      <img
                        src={
                          profileData.profileImage
                            ? profileData.profileImage.startsWith("http")
                              ? profileData.profileImage
                              : `${import.meta.env.VITE_APP_HOST}/${profileData.profileImage.replace(/^\/+/, "")}`
                            : avatar
                        }
                        alt="Profile"
                        className="h-16 w-16 rounded-full border-2 border-white object-cover shadow-sm dark:border-slate-600"
                      />
                      {isEditing && (
                        <label className="absolute inset-0 flex cursor-pointer items-center justify-center rounded-full bg-black bg-opacity-50 opacity-0 transition-opacity duration-200 hover:opacity-100">
                          <Camera className="h-4 w-4 text-white" />
                          <input
                            type="file"
                            accept="image/*"
                            onChange={handleProfileImageUpload}
                            className="hidden"
                          />
                        </label>
                      )}
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-slate-900 dark:text-white font-sora">
                        {profileData.name || 'Your Name'}
                      </h3>
                      <p className="text-sm text-slate-600 dark:text-slate-400 font-manrope">
                        {profileData.email || '<EMAIL>'}
                      </p>
                      {isEditing && (
                        <p className="text-xs text-blue-600 dark:text-blue-400 mt-1 font-manrope">
                          Click image to change photo
                        </p>
                      )}
                    </div>
                  </div>
                </div>

                {/* Compact Profile Form */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="md:col-span-2">
                    <label className="mb-1 block text-sm font-medium text-slate-700 dark:text-slate-300 font-manrope">
                      Full Name
                    </label>
                    <input
                      type="text"
                      value={profileData.name}
                      onChange={(e) =>
                        setProfileData((prev) => ({
                          ...prev,
                          name: e.target.value,
                        }))
                      }
                      disabled={!isEditing}
                      className="w-full rounded-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-800 px-3 py-2 text-slate-900 dark:text-white transition-colors focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 disabled:bg-slate-50 disabled:text-slate-500 dark:disabled:bg-slate-700 disabled:cursor-not-allowed font-manrope"
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="mb-1 block text-sm font-medium text-slate-700 dark:text-slate-300 font-manrope">
                      Email Address
                    </label>
                    <div className="w-full rounded-lg border border-slate-300 dark:border-slate-600 bg-slate-50 dark:bg-slate-700 px-3 py-2 text-slate-900 dark:text-white flex items-center gap-2">
                      <User className="h-4 w-4 text-slate-500 dark:text-slate-400" />
                      <span className="font-manrope">{profileData.email}</span>
                      <span className="ml-auto text-xs text-slate-500 dark:text-slate-400 bg-slate-200 dark:bg-slate-600 px-2 py-1 rounded font-manrope">Read-only</span>
                    </div>
                  </div>

                  <div>
                    <label className="mb-1 block text-sm font-medium text-slate-700 dark:text-slate-300 font-manrope">
                      Preferred Location
                    </label>
                    <input
                      type="text"
                      value={profileData.location}
                      onChange={(e) =>
                        setProfileData((prev) => ({
                          ...prev,
                          location: e.target.value,
                        }))
                      }
                      disabled={!isEditing}
                      placeholder="e.g., New York, Remote, Bangalore"
                      className="w-full rounded-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-800 px-3 py-2 text-slate-900 dark:text-white transition-colors focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 disabled:bg-slate-50 disabled:text-slate-500 dark:disabled:bg-slate-700 disabled:cursor-not-allowed font-manrope"
                    />
                  </div>

                  <div>
                    <label className="mb-1 block text-sm font-medium text-slate-700 dark:text-slate-300 font-manrope">
                      Mobile Number
                    </label>
                    <input
                      type="tel"
                      value={profileData.mobile}
                      onChange={(e) =>
                        setProfileData((prev) => ({
                          ...prev,
                          mobile: e.target.value,
                        }))
                      }
                      disabled={!isEditing}
                      className="w-full rounded-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-800 px-3 py-2 text-slate-900 dark:text-white transition-colors focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 disabled:bg-slate-50 disabled:text-slate-500 dark:disabled:bg-slate-700 disabled:cursor-not-allowed font-manrope"
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="mb-1 block text-sm font-medium text-slate-700 dark:text-slate-300 font-manrope">
                      Languages Known
                    </label>
                    <input
                      type="text"
                      value={profileData.language}
                      onChange={(e) =>
                        setProfileData((prev) => ({
                          ...prev,
                          language: e.target.value,
                        }))
                      }
                      disabled={!isEditing}
                      placeholder="e.g., English, Hindi, Spanish"
                      className="w-full rounded-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-800 px-3 py-2 text-slate-900 dark:text-white transition-colors focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 disabled:bg-slate-50 disabled:text-slate-500 dark:disabled:bg-slate-700 disabled:cursor-not-allowed font-manrope"
                    />
                  </div>
                </div>

                {/* Compact Resume Section */}
                <div className="mt-4 bg-slate-50 dark:bg-slate-700/50 rounded-xl p-4 border border-slate-200 dark:border-slate-600">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <FileText className="w-4 h-4 text-green-600" />
                      <h3 className="font-semibold text-slate-900 dark:text-white font-sora">
                        Resume
                      </h3>
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
                    <a
                      href="/resume"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center gap-2 rounded-lg bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 px-3 py-2 text-sm font-medium text-slate-700 dark:text-slate-300 transition-colors hover:bg-slate-50 dark:hover:bg-slate-600 font-manrope"
                    >
                      <FileText className="h-3 w-3" />
                      View Current Resume
                    </a>

                    {isEditing && (
                      <div className="flex-1">
                        <label className={`inline-flex cursor-pointer items-center gap-2 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors font-manrope ${
                          loading.profile
                            ? 'bg-gray-600 cursor-not-allowed'
                            : 'bg-green-600 hover:bg-green-700'
                        }`}>
                          {loading.profile ? (
                            <>
                              <div className="border-t-transparent h-3 w-3 animate-spin rounded-full border-2 border-white"></div>
                              Uploading...
                            </>
                          ) : (
                            <>
                              <Upload className="h-3 w-3" />
                              Upload New Resume
                            </>
                          )}
                          <input
                            type="file"
                            accept=".pdf,.doc,.docx"
                            onChange={handleFileUpload}
                            className="hidden"
                            disabled={loading.profile}
                          />
                        </label>
                        {uploadedFile && !loading.profile && (
                          <div className="mt-2 flex items-center gap-2 text-sm text-green-600 dark:text-green-400 font-manrope">
                            <Check className="h-3 w-3" />
                            <span>{uploadedFile.name} uploaded</span>
                          </div>
                        )}
                        {errors.resume && (
                          <p className="mt-2 text-sm text-red-600 dark:text-red-400 font-manrope">
                            {errors.resume}
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Security Tab */}
            {activeTab === "security" && (
              <div>
                {/* Compact Section Header */}
                <div className="flex items-center gap-2 mb-4">
                  <Lock className="w-4 h-4 text-red-600" />
                  <h2 className="text-lg font-semibold text-slate-900 dark:text-white font-sora">
                    Security Settings
                  </h2>
                </div>

                <div className="max-w-2xl">
                  {/* Compact Password Change Card */}
                  <div className="bg-slate-50 dark:bg-slate-700/50 border border-slate-200 dark:border-slate-600 rounded-xl p-4">
                    <div className="flex items-center gap-2 mb-4">
                      <Lock className="w-4 h-4 text-blue-600" />
                      <h3 className="font-semibold text-slate-900 dark:text-white font-sora">
                        Change Password
                      </h3>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <label className="mb-1 block text-sm font-medium text-slate-700 dark:text-slate-300 font-manrope">
                          Current Password
                        </label>
                        <div className="relative">
                          <input
                            type={showPasswords.old ? "text" : "password"}
                            value={passwordData.oldPassword}
                            onChange={(e) =>
                              setPasswordData((prev) => ({
                                ...prev,
                                oldPassword: e.target.value,
                              }))
                            }
                            className="w-full rounded-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-800 px-3 py-2 pr-10 text-slate-900 dark:text-white transition-colors focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 font-manrope"
                            placeholder="Enter your current password"
                          />
                          <button
                            type="button"
                            onClick={() =>
                              setShowPasswords((prev) => ({
                                ...prev,
                                old: !prev.old,
                              }))
                            }
                            className="absolute right-3 top-1/2 -translate-y-1/2 text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200 transition-colors"
                          >
                            {showPasswords.old ? (
                              <EyeOffIcon className="h-4 w-4" />
                            ) : (
                              <EyeIcon className="h-4 w-4" />
                            )}
                          </button>
                        </div>
                      </div>

                      <div>
                        <label className="mb-1 block text-sm font-medium text-slate-700 dark:text-slate-300 font-manrope">
                          New Password
                        </label>
                        <div className="relative">
                          <input
                            type={showPasswords.new ? "text" : "password"}
                            value={passwordData.newPassword}
                            onChange={(e) =>
                              setPasswordData((prev) => ({
                                ...prev,
                                newPassword: e.target.value,
                              }))
                            }
                            className="w-full rounded-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-800 px-3 py-2 pr-10 text-slate-900 dark:text-white transition-colors focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 font-manrope"
                            placeholder="Enter a new password"
                          />
                          <button
                            type="button"
                            onClick={() =>
                              setShowPasswords((prev) => ({
                                ...prev,
                                new: !prev.new,
                              }))
                            }
                            className="absolute right-3 top-1/2 -translate-y-1/2 text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200 transition-colors"
                          >
                            {showPasswords.new ? (
                              <EyeOffIcon className="h-4 w-4" />
                            ) : (
                              <EyeIcon className="h-4 w-4" />
                            )}
                          </button>
                        </div>
                      </div>

                      <div>
                        <label className="mb-1 block text-sm font-medium text-slate-700 dark:text-slate-300 font-manrope">
                          Confirm New Password
                        </label>
                        <div className="relative">
                          <input
                            type={showPasswords.confirm ? "text" : "password"}
                            value={passwordData.confirmPassword}
                            onChange={(e) =>
                              setPasswordData((prev) => ({
                                ...prev,
                                confirmPassword: e.target.value,
                              }))
                            }
                            className="w-full rounded-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-800 px-3 py-2 pr-10 text-slate-900 dark:text-white transition-colors focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 font-manrope"
                            placeholder="Confirm your new password"
                          />
                          <button
                            type="button"
                            onClick={() =>
                              setShowPasswords((prev) => ({
                                ...prev,
                                confirm: !prev.confirm,
                              }))
                            }
                            className="absolute right-3 top-1/2 -translate-y-1/2 text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200 transition-colors"
                          >
                            {showPasswords.confirm ? (
                              <EyeOffIcon className="h-4 w-4" />
                            ) : (
                              <EyeIcon className="h-4 w-4" />
                            )}
                          </button>
                        </div>
                      </div>

                      <button
                        onClick={handlePasswordChange}
                        disabled={loading.password}
                        className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed font-manrope"
                      >
                        {loading.password ? (
                          <>
                            <div className="border-t-transparent h-3 w-3 animate-spin rounded-full border-2 border-white"></div>
                            Updating...
                          </>
                        ) : (
                          "Update Password"
                        )}
                      </button>
                    </div>
                  </div>

                  {/* Compact Security Tips */}
                  <div className="mt-4 bg-blue-50 dark:bg-blue-900/20 rounded-xl p-4 border border-blue-200 dark:border-blue-800/50">
                    <div className="flex items-center gap-2 mb-3">
                      <Shield className="w-4 h-4 text-blue-600" />
                      <h4 className="text-sm font-semibold text-blue-900 dark:text-blue-300 font-sora">
                        Security Tips
                      </h4>
                    </div>
                    <ul className="space-y-2 text-sm text-blue-800 dark:text-blue-400 font-manrope">
                      <li className="flex items-start gap-2">
                        <Check className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
                        <span>Use 8+ characters with mixed case, numbers, symbols</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <Check className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
                        <span>Avoid personal information</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <Check className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
                        <span>Don't reuse passwords from other accounts</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            )}

            {/* Notifications Tab */}
            {activeTab === "notifications" && (
              <div>
                {/* Compact Section Header */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <Bell className="w-4 h-4 text-yellow-600" />
                    <h2 className="text-lg font-semibold text-slate-900 dark:text-white font-sora">
                      Notifications
                    </h2>
                  </div>
                </div>

                <div className="space-y-3">
                  {[
                    {
                      key: "emailNotifications",
                      label: "Email Notifications",
                      description: "Receive notifications via email",
                      icon: "📧",
                    },
                    {
                      key: "jobAlerts",
                      label: "Job Alerts",
                      description: "Get notified about new job opportunities",
                      icon: "💼",
                    },
                    {
                      key: "interviewReminders",
                      label: "Interview Reminders",
                      description: "Receive reminders about upcoming interviews",
                      icon: "⏰",
                    },
                    {
                      key: "marketingEmails",
                      label: "Marketing Emails",
                      description: "Receive promotional emails and newsletters",
                      icon: "📢",
                    },
                  ].map((setting) => (
                    <div
                      key={setting.key}
                      className="bg-slate-50 dark:bg-slate-700/50 border border-slate-200 dark:border-slate-600 rounded-lg p-3 hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="text-lg">{setting.icon}</div>
                          <div>
                            <h3 className="text-sm font-semibold text-slate-900 dark:text-white font-sora">
                              {setting.label}
                            </h3>
                            <p className="text-xs text-slate-600 dark:text-slate-400 font-manrope">
                              {setting.description}
                            </p>
                          </div>
                        </div>
                        <label className="relative inline-flex cursor-pointer items-center">
                          <input
                            type="checkbox"
                            checked={notificationSettings[setting.key]}
                            onChange={(e) =>
                              setNotificationSettings((prev) => ({
                                ...prev,
                                [setting.key]: e.target.checked,
                              }))
                            }
                            className="peer sr-only"
                          />
                          <div className="peer h-5 w-9 rounded-full bg-slate-300 after:absolute after:left-[2px] after:top-[2px] after:h-4 after:w-4 after:rounded-full after:border after:border-slate-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-blue-600 peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 dark:border-slate-600 dark:bg-slate-600"></div>
                        </label>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="mt-6 flex justify-end">
                  <button
                    onClick={async () => {
                      setLoading((prev) => ({ ...prev, notifications: true }));
                      try {
                        const response = await fetch(
                          `${import.meta.env.VITE_APP_HOST}/api/v1/candidate/${cand_id}/notifications`,
                          {
                            method: "PUT",
                            headers: { "Content-Type": "application/json" },
                            body: JSON.stringify(notificationSettings),
                          }
                        );
                        const data = await response.json();
                        if (!response.ok) {
                          toast.error(data.message || "Failed to update notifications");
                        } else {
                          toast.success("Notification preferences updated!");
                        }
                      } catch (error) {
                        toast.error("Failed to update notifications");
                      } finally {
                        setLoading((prev) => ({ ...prev, notifications: false }));
                      }
                    }}
                    disabled={loading.notifications}
                    className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed font-manrope"
                  >
                    {loading.notifications ? (
                      <>
                        <div className="border-t-transparent h-3 w-3 animate-spin rounded-full border-2 border-white"></div>
                        Saving...
                      </>
                    ) : (
                      "Save"
                    )}
                  </button>
                </div>
              </div>
            )}

            {/* Privacy Tab */}
            {activeTab === "privacy" && (
              <div>
                {/* Compact Section Header */}
                <div className="flex items-center gap-2 mb-4">
                  <Shield className="w-4 h-4 text-green-600" />
                  <h2 className="text-lg font-semibold text-slate-900 dark:text-white font-sora">
                    Privacy Settings
                  </h2>
                </div>

                <div className="space-y-4">
                  {/* Profile Visibility Card */}
                  <div className="bg-slate-50 dark:bg-slate-700/50 border border-slate-200 dark:border-slate-600 rounded-xl p-4">
                    <div className="flex items-center gap-2 mb-3">
                      <User className="w-4 h-4 text-blue-600" />
                      <h3 className="font-semibold text-slate-900 dark:text-white font-sora">
                        Profile Visibility
                      </h3>
                    </div>
                    <div className="space-y-2">
                      {[
                        {
                          value: "public",
                          label: "Public",
                          description: "Anyone can view your profile",
                          icon: "🌍",
                        },
                        {
                          value: "employers",
                          label: "Employers Only",
                          description: "Only verified employers can view your profile",
                          icon: "🏢",
                        },
                        {
                          value: "private",
                          label: "Private",
                          description: "Only you can view your profile",
                          icon: "🔒",
                        },
                      ].map((option) => (
                        <label
                          key={option.value}
                          className={`flex cursor-pointer items-center rounded-lg border p-3 transition-colors hover:bg-slate-100 dark:hover:bg-slate-600/50 ${
                            privacySettings.profileVisibility === option.value
                              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                              : 'border-slate-300 dark:border-slate-600'
                          }`}
                        >
                          <input
                            type="radio"
                            name="profileVisibility"
                            value={option.value}
                            checked={privacySettings.profileVisibility === option.value}
                            onChange={(e) =>
                              setPrivacySettings((prev) => ({
                                ...prev,
                                profileVisibility: e.target.value,
                              }))
                            }
                            className="h-4 w-4 border-slate-300 text-blue-600 focus:ring-blue-500 dark:border-slate-600"
                          />
                          <div className="ml-3 flex items-center gap-2">
                            <div className="text-lg">{option.icon}</div>
                            <div>
                              <div className="text-sm font-semibold text-slate-900 dark:text-white font-sora">
                                {option.label}
                              </div>
                              <div className="text-xs text-slate-600 dark:text-slate-400 font-manrope">
                                {option.description}
                              </div>
                            </div>
                          </div>
                        </label>
                      ))}
                    </div>
                  </div>

                  {/* Contact Settings Card */}
                  <div className="bg-slate-50 dark:bg-slate-700/50 border border-slate-200 dark:border-slate-600 rounded-xl p-4">
                    <div className="flex items-center gap-2 mb-3">
                      <Bell className="w-4 h-4 text-purple-600" />
                      <h3 className="font-semibold text-slate-900 dark:text-white font-sora">
                        Contact Preferences
                      </h3>
                    </div>
                    <div className="space-y-3">
                      {[
                        {
                          key: "showContactInfo",
                          label: "Show Contact Information",
                          description: "Allow others to see your email and phone number",
                          icon: "📞",
                        },
                        {
                          key: "allowDirectMessages",
                          label: "Allow Direct Messages",
                          description: "Let employers send you messages directly",
                          icon: "💬",
                        },
                      ].map((setting) => (
                        <div
                          key={setting.key}
                          className="bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-600 rounded-lg p-3"
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className="text-lg">{setting.icon}</div>
                              <div>
                                <h3 className="text-sm font-semibold text-slate-900 dark:text-white font-sora">
                                  {setting.label}
                                </h3>
                                <p className="text-xs text-slate-600 dark:text-slate-400 font-manrope">
                                  {setting.description}
                                </p>
                              </div>
                            </div>
                            <label className="relative inline-flex cursor-pointer items-center">
                              <input
                                type="checkbox"
                                checked={privacySettings[setting.key]}
                                onChange={(e) =>
                                  setPrivacySettings((prev) => ({
                                    ...prev,
                                    [setting.key]: e.target.checked,
                                  }))
                                }
                                className="peer sr-only"
                              />
                              <div className="peer h-5 w-9 rounded-full bg-slate-300 after:absolute after:left-[2px] after:top-[2px] after:h-4 after:w-4 after:rounded-full after:border after:border-slate-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-blue-600 peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 dark:border-slate-600 dark:bg-slate-600"></div>
                            </label>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <button
                      onClick={async () => {
                        setLoading((prev) => ({ ...prev, privacy: true }));
                        try {
                          const response = await fetch(
                            `${import.meta.env.VITE_APP_HOST}/api/v1/candidate/${cand_id}/privacy`,
                            {
                              method: "PUT",
                              headers: { "Content-Type": "application/json" },
                              body: JSON.stringify(privacySettings),
                            }
                          );
                          const data = await response.json();
                          if (!response.ok) {
                            toast.error(data.message || "Failed to update privacy settings");
                          } else {
                            toast.success("Privacy settings updated!");
                          }
                        } catch (error) {
                          toast.error("Failed to update privacy settings");
                        } finally {
                          setLoading((prev) => ({ ...prev, privacy: false }));
                        }
                      }}
                      disabled={loading.privacy}
                      className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed font-manrope"
                    >
                      {loading.privacy ? (
                        <>
                          <div className="border-t-transparent h-3 w-3 animate-spin rounded-full border-2 border-white"></div>
                          Saving...
                        </>
                      ) : (
                        "Save"
                      )}
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CandidateSettings;
