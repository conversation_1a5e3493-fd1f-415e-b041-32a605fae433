import React from "react";
import { Routes, Route, Navigate, useLocation } from "react-router-dom";
import Navbar from "components/navbar";
import Sidebar from "components/sidebar";
import Footer from "components/footer/Footer";
import HeaderNav from "components/header/HeaderNav";
import routes from "routes";
import Cookies from "js-cookie";
import { FiAlignJustify } from "react-icons/fi";

export default function Employer(props) {
  const { ...rest } = props;
  const location = useLocation();
  
  // Check if user is authenticated
  const isAuthenticated = () => {
    const token = Cookies.get("jstoken");
    const role = Cookies.get("role");
    return token && role === "employer";
  };

  // Redirect to signin if not authenticated
  if (!isAuthenticated() && location.pathname !== "/employer/sign-in") {
    return <Navigate to="/employer/sign-in" replace />;
  }
  const [open, setOpen] = React.useState(true);
  const [currentRoute, setCurrentRoute] = React.useState("Home");

  React.useEffect(() => {
    window.addEventListener("resize", () =>
      window.innerWidth < 1200 ? setOpen(false) : setOpen(true)
    );
  }, []);
  React.useEffect(() => {
    getActiveRoute(routes);
  }, [location.pathname]);

  const getActiveRoute = (routes) => {

    let activeRoute = "Home";

    for (let i = 0; i < routes.length; i++) {

      if (window.location.href.indexOf(routes[i].layout + "/" + routes[i].path) !== -1){
        setCurrentRoute(routes[i].name);
      }
    }
    return activeRoute;
  };
  const getActiveNavbar = (routes) => {
    let activeNavbar = false;
    for (let i = 0; i < routes.length; i++) {
      if (
        window.location.href.indexOf(routes[i].layout + routes[i].path) !== -1
      ) {
        return routes[i].secondary;
      }
    }
    return activeNavbar;
  };
  const getRoutes = (routes) => {
    return routes.map((prop, key) => {
      if (prop.layout === "/employer") {
        return (
          <Route path={`/${prop.path}`} element={prop.component} key={key} />
        );
      } else {
        return null;
      }
    });
  };

  document.documentElement.dir = "ltr";

  return (
    <div className="flex h-full w-full">
      
      <Sidebar open={open} onClose={() => setOpen(false)} />
      {/* Navbar & Main Content */}
      <div className={`h-full w-full bg-lightPrimary dark:!bg-navy-900 transition-all duration-300 ${
        open ? 'ml-64' : 'ml-0'
      }`}>
        {/* Main Content */}
        <main className="flex-1 overflow-auto bg-lightPrimary dark:bg-navy-900">
          {/* Header Navigation */}
          <HeaderNav />
          
          {/* Mobile Sidebar Toggle - Only visible on mobile when sidebar is closed */}
          {!open && (
            <div className="xl:hidden fixed top-4 left-4 z-40">
              <button
                className="p-2 rounded-lg bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50 shadow-lg hover:bg-white dark:hover:bg-gray-800 transition-colors"
                onClick={() => setOpen(true)}
              >
                <FiAlignJustify className="h-4 w-4 text-gray-600 dark:text-gray-400" />
              </button>
            </div>
          )}
          
          {/* Routes */}
          <div className="h-full">
            <div className="container mx-auto px-4 py-6 max-w-7xl xl:pt-6 pt-2">
              <Routes>
                {getRoutes(routes)}

                <Route
                  path="/"
                  element={<Navigate to="/employer/dashboard" replace />}
                />
              </Routes>
            </div>

            <Footer />

          </div>
        </main>
      </div>
    </div>
  );
}
