import React, { useState } from "react";
import Cookies from "js-cookie";
import { useNavigate } from "react-router-dom";
import { X } from "lucide-react";
import toast from "react-hot-toast";

export default function JSSignup() {
  const navigate = useNavigate();

  const [fullName, setFullName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [showSendOTPButton, setShowSendOTPButton] = useState(false);
  const [showOTPModal, setShowOTPModal] = useState(false);
  const [otp, setOTP] = useState(["", "", "", ""]);
  const [generatedOTP, setGeneratedOTP] = useState("");
  const [otpVerified, setOtpVerified] = useState(false);
  const [otpError, setOtpError] = useState("");

  const [fullNameError, setFullNameError] = useState("");
  const [emailError, setEmailError] = useState("");
  const [passwordError, setPasswordError] = useState("");
  const [phoneNumberError, setPhoneNumberError] = useState("");

  const handlePhoneNumberChange = (e) => {
    const value = e.target.value.replace(/\D/g, "").slice(0, 10);
    setPhoneNumber(value);
    setShowSendOTPButton(value.length === 10);
    if (value.length !== 10) {
      setOtpVerified(false);
      setOtpError("");
    }
  };

  const handleSendOTPClick = (e) => {
    e.preventDefault();
    const newOTP = Math.floor(1000 + Math.random() * 9000).toString();
    setGeneratedOTP(newOTP);
    alert(newOTP);
    setShowOTPModal(true);
    toast.success(newOTP);
    setShowOTP(true);
    setOTP(["", "", "", ""]);
    setOtpVerified(false);
    setOtpError("");
  };

  const handleResendOTP = () => {
    handleSendOTPClick({ preventDefault: () => {} });
  };

  const handleOTPChange = (index, value) => {
    const newOTP = [...otp];
    newOTP[index] = value.replace(/\D/g, "").slice(0, 1);
    setOTP(newOTP);

    if (value && index < 3) {
      const nextInput = document.getElementById(`otp-${index + 1}`);
      if (nextInput) nextInput.focus();
    }
  };

  const handleVerifyOTP = () => {
    const enteredOTP = otp.join("");
    if (enteredOTP === generatedOTP) {
      setOtpVerified(true);
      setPhoneNumberError("");
      setOtpError("");
      setShowOTPModal(false);
    } else {
      setOtpVerified(false);
      setOtpError("Wrong OTP entered. Please try again.");
    }
  };

  const handleNextClick = (e) => {
    e.preventDefault();

    let isValid = true;

    // Reset all error messages
    setFullNameError("");
    setEmailError("");
    setPasswordError("");
    setPhoneNumberError("");

    // Email regex pattern for validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    // Validate fields
    if (!fullName) {
      setFullNameError("Full name is required");
      isValid = false;
    }
    if (!email) {
      setEmailError("Email is required");
      isValid = false;
    } else if (!emailRegex.test(email)) {
      setEmailError("Invalid email format");
      isValid = false;
    }
    if (!password) {
      setPasswordError("Password is required");
      isValid = false;
    }
    if (!phoneNumber) {
      setPhoneNumberError("Phone number is required");
      isValid = false;
    } else if (!otpVerified) {
      setPhoneNumberError("Phone number is not verified");
      isValid = false;
    }

    // If valid, log the form data
    if (isValid) {
      const formDataCookie = Cookies.get("formData");

      if (formDataCookie) {
        Cookies.remove("formData");
        console.log("deleted previous cookie");
      }
      const formData = {
        fullName,
        email,
        password,
        phoneNumber,
      };
      Cookies.set("formData", JSON.stringify(formData), { expires: 7 });
      console.log("Form Data:", JSON.stringify(formData, null, 2));
      console.log("formdata saved in a cookie");
      navigate("/candidate/onboarding");
    }
  };

  return (
    <div className="z-50 w-full max-w-lg overflow-hidden rounded-xl bg-white px-4 sm:px-6 md:px-8">
      <div className="w-full">
        <h4 className="mb-2.5 mt-2 text-2xl sm:text-3xl md:text-4xl font-bold text-navy-700 dark:text-white">
          Sign Up as a Job Seeker
        </h4>
        <p className="mb-4 text-sm sm:text-base text-gray-500">
          Let's get started by filling out information below
        </p>

        <form className="space-y-4 px-1 sm:px-2 md:px-0">
          <div>
            <label
              htmlFor="full-name"
              className="block text-sm font-medium text-gray-700"
            >
              Full Name
            </label>
            <input
              id="full-name"
              type="text"
              value={fullName}
              onChange={(e) => setFullName(e.target.value)}
              className="mt-2 block w-full rounded-md border border-gray-300 px-3 py-2 sm:px-4 sm:py-2.5 text-sm outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
            />
            {fullNameError && (
              <p className="text-red-600">{fullNameError}</p>
            )}
          </div>
          <div>
            <label
              htmlFor="email"
              className="block text-sm font-medium text-gray-700"
            >
              Email
            </label>
            <input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="mt-2 block w-full rounded-md border border-gray-300 px-3 py-2 sm:px-4 sm:py-2.5 text-sm outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
            />
            {emailError && <p className="text-red-600">{emailError}</p>}
          </div>
          <div>
            <label
              htmlFor="password"
              className="block text-sm font-medium text-gray-700"
            >
              Password
            </label>
            <input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="mt-2 block w-full rounded-md border border-gray-300 px-3 py-2 sm:px-4 sm:py-2.5 text-sm outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
            />
            {passwordError && <p className="text-red-600">{passwordError}</p>}
          </div>
          <div>
            <label
              htmlFor="phone-number"
              className="block text-sm font-medium text-gray-700"
            >
              Phone Number
            </label>
            <div className="w-full max-w-full space-y-4">
              <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                <input
                  id="phone-number"
                  type="tel"
                  value={phoneNumber}
                  onChange={handlePhoneNumberChange}
                  className="flex-grow block w-full rounded-md border border-gray-300 px-3 py-2 sm:px-4 sm:py-2.5 text-sm outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
                  maxLength={10}
                  placeholder="Enter 10 digit number"
                />
                {showSendOTPButton && !otpVerified && (
                  <button
                    onClick={handleSendOTPClick}
                    className="rounded-md bg-blue-600 px-4 py-2 text-sm sm:text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 whitespace-nowrap"
                  >
                    Send OTP
                  </button>
                )}
              </div>
              {phoneNumber.length > 0 && phoneNumber.length < 10 && (
                <p className="text-sm text-red-500">
                  Please enter a 10-digit phone number.
                </p>
              )}
            </div>

            {otpVerified && (
              <p className="mt-2 text-green-600">
                Phone number verified successfully!
              </p>
            )}
            {phoneNumberError && (
              <p className="text-red-600">{phoneNumberError}</p>
            )}
          </div>
          <div>
            <button
              type="submit"
              onClick={handleNextClick}
              className="mt-6 w-full rounded-md bg-blue-600 px-4 py-2.5 sm:py-3 text-sm sm:text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              Next
            </button>
          </div>
        </form>
        <p className="mt-4 text-center text-sm sm:text-base text-gray-600">
          Already have an account?{" "}
          <a href="/candidate/sign-in" className="text-blue-600 hover:underline">
            Sign in →
          </a>
        </p>
      </div>

      {showOTPModal && (
        <div className="fixed inset-0 z-50 backdrop-blur-md flex items-center justify-center px-4">
          <div className="absolute inset-0 bg-black opacity-50"></div>
          <div className="relative z-50 w-full max-w-sm sm:max-w-md rounded-lg bg-white p-4 sm:p-6 shadow-xl">
            <button
              onClick={() => setShowOTPModal(false)}
              className="absolute right-3 top-3 sm:right-4 sm:top-4 text-gray-500 hover:text-gray-700"
            >
              <X className="h-5 w-5 sm:h-6 sm:w-6" />
            </button>
            <h3 className="mb-4 text-lg sm:text-xl font-semibold">Enter OTP</h3>
            <div className="mb-4 flex justify-center space-x-1.5 sm:space-x-2">
              {otp.map((digit, index) => (
                <input
                  key={index}
                  id={`otp-${index}`}
                  type="text"
                  inputMode="numeric"
                  maxLength={1}
                  value={digit}
                  onChange={(e) => handleOTPChange(index, e.target.value)}
                  className="h-10 w-10 sm:h-12 sm:w-12 rounded-md border border-gray-300 text-center text-lg shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
                  aria-label={`OTP digit ${index + 1}`}
                />
              ))}
            </div>
            {otpError && <p className="mb-4 text-center text-red-600 text-sm">{otpError}</p>}
            <div className="flex flex-col sm:flex-row justify-between space-y-2 sm:space-y-0">
              <button
                onClick={handleVerifyOTP}
                className="rounded-md bg-blue-600 px-4 py-2 text-sm sm:text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                Verify OTP
              </button>
              <button
                onClick={handleResendOTP}
                className="text-blue-600 hover:text-blue-800 focus:underline focus:outline-none text-sm sm:text-base"
              >
                Resend OTP
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}