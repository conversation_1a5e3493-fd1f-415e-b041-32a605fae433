import React, { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import Cookies from "js-cookie";
import {
  HiOutlineSparkles,
  HiBriefcase,
  HiOutlineUserGroup,
  HiPlay,
  HiMicrophone,
  HiLightBulb,
} from "react-icons/hi";
import CreateVR from "./components/CreateVR/CreateVR";
import api from "../../api/axios";
import VisumeLogoIcon from "/visume-logo-new.png";

const companyTypeLabels = {
  mid_range: "Mid-range",
  startup: "Startup",
  enterprise: "Enterprise",
  small_business: "Small Business",
  // Add more mappings as needed
};

const CandidateInvite = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [inviteData, setInviteData] = useState(null);
  const [showCreateVR, setShowCreateVR] = useState(false);
  const [isSignedIn, setIsSignedIn] = useState(false);

  const jstoken = Cookies.get("jstoken");
  const candId = Cookies.get("candId");
  const role = Cookies.get("role");

  useEffect(() => {
    // Extract inviteId from URL query params
    const searchParams = new URLSearchParams(location.search);
    const inviteId = searchParams.get("inviteId");

    if (inviteId) {
      // Fetch invitation context from backend API
      // Use production API base URL if on visume.co.in, else use relative path
      api
        .get(`/api/v1/invite/${inviteId}`)
        .then(({ data }) => {
          setInviteData({
            ...data,
            skills: Array.isArray(data.skills)
              ? data.skills
              : typeof data.skills === "string"
              ? data.skills.split(",")
              : [],
          });
        })
        .catch(() => {
          if (inviteData) {
            localStorage.setItem(
              "pendingInviteData",
              JSON.stringify(inviteData)
            );
            console.log(
              "Set pendingInviteData in sessionStorage before dashboard redirect:",
              inviteData
            );
          }
          navigate("/candidate/dashboard");
        });
    } else {
      // If no inviteId, redirect to candidate dashboard
      if (inviteData) {
        localStorage.setItem("pendingInviteData", JSON.stringify(inviteData));
        console.log(
          "Set pendingInviteData in sessionStorage before dashboard redirect:",
          inviteData
        );
      }
      navigate("/candidate/dashboard");
    }

    // Check if user is signed in as a candidate
    if (jstoken && candId && role === "jobseeker") {
      setIsSignedIn(true);
    }
  }, [location.search, jstoken, candId, role, navigate]);

  const handleCreateVisume = () => {
    if (isSignedIn) {
      setShowCreateVR(true);
    } else {
      // Store invite data in localStorage before redirecting to sign in
      if (inviteData) {
        localStorage.setItem("pendingInviteData", JSON.stringify(inviteData));
        console.log(
          "Set pendingInviteData in sessionStorage before signIn redirect:",
          inviteData
        );
      }
      // Pass only inviteId as query param to signIn
      const searchParams = new URLSearchParams(location.search);
      const inviteId = searchParams.get("inviteId");
      if (inviteId) {
        navigate(`/candidate/sign-in?inviteId=${inviteId}`);
      } else {
        navigate(`/candidate/sign-in`);
      }
    }
  };

  const handleSignUp = () => {
    // Store invite data in localStorage before redirecting to sign up
    if (inviteData) {
      localStorage.setItem("pendingInviteData", JSON.stringify(inviteData));
      console.log(
        "Set pendingInviteData in sessionStorage before signUp redirect:",
        inviteData
      );
    }
    // Pass only inviteId as query param to account creation
    const searchParams = new URLSearchParams(location.search);
    const inviteId = searchParams.get("inviteId");
    if (inviteId) {
      navigate(`/create-account/candidate?inviteId=${inviteId}`);
    } else {
      navigate(`/create-account/candidate`);
    }
  };

  const toggleCreateVR = () => {
    setShowCreateVR(!showCreateVR);
  };

  if (!inviteData) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="mx-auto h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600"></div>
          <p className="mt-4 text-gray-600">Loading job details...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-blue-50 flex items-center justify-center py-4">
      <div className="container mx-auto max-w-lg px-4">
        {/* Visume Branding Header */}
        <div className="mb-4 text-center">
          <div className="mb-3 flex items-center justify-center gap-2">
            <img 
              src={VisumeLogoIcon} 
              alt="Visume Logo" 
              className="h-10 w-10" 
            />
            <div className="text-left">
              <h1 className="text-xl font-bold bg-gradient-to-r from-indigo-600 to-blue-600 bg-clip-text text-transparent">
                Visume
              </h1>
              <p className="text-xs text-gray-500 font-medium">AI-Powered Video Interviews</p>
            </div>
          </div>
          <h2 className="mb-1 text-lg font-bold text-gray-900">
            Ready for Your AI Interview?
          </h2>
          <p className="text-sm text-gray-600">
            Attend your AI video interview for {inviteData.organization}
          </p>
        </div>

        {/* AI Interview Setup Card */}
        <div className="mb-4 overflow-hidden rounded-xl border border-indigo-100 bg-white shadow-lg">
          <div className="bg-gradient-to-r from-indigo-500 to-blue-600 p-3 text-white">
            <div className="text-center">
              <h3 className="text-lg font-bold">{inviteData.jobRole}</h3>
              <p className="text-sm font-medium text-indigo-100 mb-1">
                {inviteData.organization}
              </p>
              <p className="text-xs text-indigo-200">
                {inviteData.experience} • {inviteData.location}
              </p>
            </div>
          </div>

          <div className="p-3">
            <div className="mb-3">
              <h4 className="mb-2 flex items-center justify-center gap-2 text-sm font-semibold text-gray-900">
                <HiLightBulb className="h-4 w-4 text-indigo-600" />
                Skills to be assessed
              </h4>
              <div className="flex flex-wrap gap-1 justify-center">
                {inviteData.skills.slice(0, 5).map((skill, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center rounded-md bg-indigo-50 px-2 py-1 text-xs font-medium text-indigo-700"
                  >
                    {skill.trim()}
                  </span>
                ))}
                {inviteData.skills.length > 5 && (
                  <span className="inline-flex items-center rounded-md bg-gray-100 px-2 py-1 text-xs text-gray-600">
                    +{inviteData.skills.length - 5} more
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* AI Interview Action Section */}
        <div className="rounded-xl border border-indigo-100 bg-white p-4 text-center shadow-lg">
          {isSignedIn ? (
            <div>
              <div className="mb-3 flex items-center justify-center">
                <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-br from-indigo-100 to-blue-100">
                  <HiMicrophone className="h-5 w-5 text-indigo-600" />
                </div>
              </div>
              <h3 className="mb-2 text-base font-bold text-gray-900">
                Start Your AI Interview
              </h3>
              <p className="mb-3 text-xs text-gray-600">
                AI will generate personalized questions. Record your responses to create your profile.
              </p>
              <button
                onClick={handleCreateVisume}
                className="inline-flex transform items-center gap-2 rounded-lg bg-gradient-to-r from-indigo-600 to-blue-600 px-6 py-2.5 text-sm font-semibold text-white shadow-md transition-all duration-200 hover:-translate-y-0.5 hover:from-indigo-700 hover:to-blue-700 hover:shadow-lg"
              >
                <HiPlay className="h-4 w-4" />
                Begin AI Interview
              </button>
            </div>
          ) : (
            <div>
              <div className="mb-3 flex items-center justify-center">
                <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-br from-indigo-100 to-blue-100">
                  <HiOutlineSparkles className="h-5 w-5 text-indigo-600" />
                </div>
              </div>
              <h3 className="mb-2 text-base font-bold text-gray-900">
                Join Visume to Start
              </h3>
              <p className="mb-3 text-xs text-gray-600">
                Sign in or create account to begin AI interview and showcase your skills.
              </p>
              <button
                onClick={() => {
                  const searchParams = new URLSearchParams(location.search);
                  const inviteId = searchParams.get("inviteId");
                  if (inviteId) {
                    navigate(`/candidate/sign-in?inviteId=${inviteId}`);
                  } else {
                    navigate(`/candidate/sign-in`);
                  }
                }}
                className="inline-flex items-center justify-center gap-2 rounded-lg bg-gradient-to-r from-indigo-600 to-blue-600 px-6 py-2.5 text-sm font-semibold text-white shadow-md transition-all duration-200 hover:from-indigo-700 hover:to-blue-700 hover:shadow-lg"
              >
                <HiPlay className="h-4 w-4" />
                Apply Now
              </button>
            </div>
          )}
        </div>

        {/* AI Interview Process */}
        <div className="mt-4 rounded-xl bg-gradient-to-r from-indigo-50 to-blue-50 p-3">
          <div className="grid gap-2 text-xs grid-cols-3">
            <div className="text-center">
              <div className="mx-auto mb-1 flex h-5 w-5 items-center justify-center rounded-full bg-indigo-200 text-xs font-bold text-indigo-700">
                1
              </div>
              <p className="text-gray-600 text-xs">
                AI questions
              </p>
            </div>
            <div className="text-center">
              <div className="mx-auto mb-1 flex h-5 w-5 items-center justify-center rounded-full bg-indigo-200 text-xs font-bold text-indigo-700">
                2
              </div>
              <p className="text-gray-600 text-xs">
                Interview
              </p>
            </div>
            <div className="text-center">
              <div className="mx-auto mb-1 flex h-5 w-5 items-center justify-center rounded-full bg-indigo-200 text-xs font-bold text-indigo-700">
                3
              </div>
              <p className="text-gray-600 text-xs">
                Visume created
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* CreateVR Modal */}
      {showCreateVR && (
        <CreateVR togglePopupVR={toggleCreateVR} inviteData={inviteData} />
      )}
    </div>
  );
};

export default CandidateInvite;
