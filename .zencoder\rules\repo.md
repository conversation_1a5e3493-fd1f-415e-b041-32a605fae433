---
description: Repository Information Overview
alwaysApply: true
---

# Repository Information Overview

## Repository Summary
Visume is a platform connecting employers and candidates through AI-powered video resumes and intelligent job matching. The repository consists of two main components: a React-based frontend (visume-ui) and a Node.js backend API (visume-api).

## Repository Structure
- **visume-ui/**: Frontend React application with Vite build system
- **visume-api/**: Backend Node.js/Express API with Prisma ORM
- **src/**: Additional source files with employer views

### Main Repository Components
- **Frontend (visume-ui)**: React application for user interface
- **Backend (visume-api)**: Express API for data processing and business logic
- **Database**: MySQL database managed through Prisma ORM

## Projects

### Visume UI (Frontend)
**Configuration File**: package.json

#### Language & Runtime
**Language**: JavaScript/React
**Version**: React 18.3.1
**Build System**: Vite 4.5.5
**Package Manager**: npm

#### Dependencies
**Main Dependencies**:
- React 18.3.1
- React Router DOM 6.4.0
- Axios for API requests
- Tailwind CSS for styling
- Framer Motion for animations
- AWS SDK for Polly integration
- Groq SDK for AI integration

#### Build & Installation
```bash
cd visume-ui
npm install
npm run dev    # Development server
npm run build  # Production build
```

#### Testing
**Framework**: Jest with React Testing Library
**Test Location**: Tests are located alongside components
**Run Command**:
```bash
npm test
```

### Visume API (Backend)
**Configuration File**: package.json

#### Language & Runtime
**Language**: JavaScript/Node.js
**Version**: Node.js (CommonJS)
**Package Manager**: npm
**Database**: MySQL with Prisma ORM

#### Dependencies
**Main Dependencies**:
- Express 4.19.2
- Prisma Client 6.8.2
- JWT for authentication
- AWS SDK for S3 integration
- Google Generative AI
- Groq SDK for AI integration
- Multer for file uploads

**Development Dependencies**:
- Nodemon for development
- Prisma CLI for database management

#### Build & Installation
```bash
cd visume-api
npm install
npx prisma migrate deploy  # Set up database schema
npm run start:dev          # Development with auto-reload
npm run start:app          # Production
```

#### Database
**Type**: MySQL
**ORM**: Prisma
**Schema**: Comprehensive data model for users, employers, job seekers, video profiles, and job descriptions
**Migration**: Managed through Prisma migrations

#### Key Features
- RESTful API endpoints for candidate and employer operations
- JWT-based authentication and authorization
- File uploads for resumes and profile pictures
- AI-powered interview evaluation and scoring
- Job description creation and candidate-job matching

## Environment Configuration

### Frontend (.env)
- `VITE_APP_HOST`: Backend API URL
- `VITE_AWS_POOL_ID`: AWS Cognito Pool ID
- `VITE_AWS_REGION`: AWS Region
- `VITE_GROQ_API_KEY`: Groq API key for AI integration
- `VITE_GOOGLE_CLIENT_ID`: Google OAuth client ID

### Backend (.env)
- `DATABASE_URL`: MySQL connection string
- `AWS_ACCESS_KEY_ID`: AWS access key
- `AWS_SECRET_ACCESS_KEY`: AWS secret key
- `AWS_REGION`: AWS region
- `AWS_BUCKET_NAME`: S3 bucket for file storage
- `GROQ_API_KEY`: Groq API key for AI integration
- `GOOGLE_API_KEY`: Google API key
- `PORT`: Server port (default: 8000)