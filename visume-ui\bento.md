# Employer Dashboard Bento Grid Layout

## Overview
This document outlines the design and content for a 5-card bento grid section for the hiring manager landing page, showcasing the key features of the employer dashboard.

## Bento Grid Cards

### Card 1: AI-Powered Candidate Matching (Large - 2x2)
**Title:** Smart Candidate Matching
**Text:** Discover the perfect candidates in seconds with our AI-powered matching system that analyzes skills, experience, and cultural fit.
**Description of Supporting Visual:** 
- Interactive dashboard mockup showing candidate profiles with match percentage scores
- Animated matching algorithm visualization with connecting lines between job requirements and candidate skills
- Gradient background from blue to indigo with floating skill tags
**Placement:** Top-left corner, spanning 2 columns and 2 rows
**Dimensions:** Large card (50% width, 60% height of grid)

### Card 2: Video Resume Access (Medium - 1x2)
**Title:** Pre-Screened Video Interviews
**Text:** Browse high-quality video interviews from candidates who have been vetted and assessed by our AI.
**Description of Supporting Visual:**
- Video player interface showing a professional candidate interview
- Multiple thumbnail previews of different candidates
- Play button overlay with view count and duration indicators
**Placement:** Top-right, spanning 1 column and 2 rows
**Dimensions:** Medium vertical card (25% width, 60% height of grid)

### Card 3: Real-Time Analytics (Medium - 2x1)
**Title:** Advanced Hiring Analytics
**Text:** Get detailed insights on candidate performance, hiring trends, and team fit predictions to make data-driven decisions.
**Description of Supporting Visual:**
- Dashboard with colorful charts and graphs showing hiring metrics
- Key performance indicators (KPIs) with trending arrows
- Heatmap visualization of candidate scoring across different criteria
**Placement:** Bottom-left, spanning 2 columns and 1 row
**Dimensions:** Medium horizontal card (50% width, 40% height of grid)

### Card 4: Credit Management (Small - 1x1)
**Title:** Plan & Credits
**Text:** Track your usage and manage billing with transparent credit system and flexible plans.
**Description of Supporting Visual:**
- Credit usage meter with remaining credits display
- Simple billing card interface with plan information
- Progress bar showing credit consumption
**Placement:** Bottom-right top, spanning 1 column and 1 row
**Dimensions:** Small square card (25% width, 20% height of grid)

### Card 5: Candidate Tracking (Small - 1x1)
**Title:** Track Progress
**Text:** Monitor candidates through shortlisted, unlocked, interview, and offer stages seamlessly.
**Description of Supporting Visual:**
- Kanban-style board with candidate cards moving through pipeline stages
- Status indicators with different colors for each stage
- Progress completion badges and action buttons
**Placement:** Bottom-right bottom, spanning 1 column and 1 row
**Dimensions:** Small square card (25% width, 20% height of grid)

## Grid Layout Structure
```
[Card 1: AI Matching - 2x2]    [Card 2: Video - 1x2]
[Card 1: AI Matching - 2x2]    [Card 2: Video - 1x2]
[Card 3: Analytics - 2x1]      [Card 4: Credits - 1x1]
                               [Card 5: Tracking - 1x1]
```

## Design Guidelines
- **Color Scheme:** Blue to indigo gradients with white cards and subtle shadows
- **Typography:** Manrope for headings, Sora for body text
- **Spacing:** Consistent 1.5rem gaps between cards
- **Animation:** Subtle hover effects with scale transforms and shadow increases
- **Border:** 2px border with blue/indigo accent colors
- **Shadow:** Layered shadows for depth (blue-tinted)