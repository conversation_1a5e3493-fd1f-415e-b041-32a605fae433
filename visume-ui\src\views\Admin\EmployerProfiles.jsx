import React, { useState, useEffect } from 'react';
import {
  Users,
  Building2,
  CreditCard,
  Crown,
  Mail,
  MapPin,
  Edit3,
  Search,
  Filter,
  Calendar,
  Activity,
  Phone,
  Briefcase,
  Star,
  ChevronRight
} from "lucide-react";
import { HiOutlineSparkles } from "react-icons/hi";
import { toast } from "react-hot-toast";
import CreditManagementModal from "./components/CreditManagementModal";
import avatar from "assets/img/avatars/avatar4.png";

const EmployerProfiles = () => {
  const [employers, setEmployers] = useState([]);
  const [filteredEmployers, setFilteredEmployers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [selectedEmployer, setSelectedEmployer] = useState(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editCredits, setEditCredits] = useState("");
  const [isUpdatingCredits, setIsUpdatingCredits] = useState(false);

  // Fetch employers from API
  useEffect(() => {
    const fetchEmployers = async () => {
      setLoading(true);
      try {
        const response = await fetch(`${import.meta.env.VITE_APP_HOST}/api/v1/admin/employers`);
        const result = await response.json();

        if (response.ok) {
          setEmployers(result.data || []);
          setFilteredEmployers(result.data || []);
        } else {
          console.error("Error fetching employers:", result.message);
          toast.error("Failed to load employers");
          // Fallback to empty array
          setEmployers([]);
          setFilteredEmployers([]);
        }
      } catch (error) {
        console.error("Error fetching employers:", error);
        toast.error("Failed to load employers");
        // Fallback to empty array
        setEmployers([]);
        setFilteredEmployers([]);
      } finally {
        setLoading(false);
      }
    };

    fetchEmployers();
  }, []);

  // Filter employers based on search term and status
  useEffect(() => {
    let filtered = employers;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(employer =>
        employer.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        employer.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        employer.company?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        employer.designation?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter(employer => employer.membershipStatus === statusFilter);
    }

    setFilteredEmployers(filtered);
  }, [employers, searchTerm, statusFilter]);

  const handleEditCredits = (employer) => {
    setSelectedEmployer(employer);
    // Ensure initial value meets minimum requirement of 10 credits
    const currentLimit = employer.totalCredits || 10;
    setEditCredits(Math.max(currentLimit, 10).toString());
    setShowEditModal(true);
  };

  const handleSaveCredits = async () => {
    if (!selectedEmployer) return;

    try {
      const newCredits = parseInt(editCredits);
      if (isNaN(newCredits) || newCredits < 0) {
        toast.error("Please enter a valid credit limit");
        return;
      }

      // 🎯 CREDIT VALIDATION: Enforce minimum credit limit of 10
      if (newCredits < 10) {
        toast.error("Credit limit must be at least 10 credits");
        return;
      }

      setIsUpdatingCredits(true);

      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/admin/employer/${selectedEmployer.id}/credits`,
        {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ credits: newCredits, mode: 'replace' }),
        }
      );

      const result = await response.json();

      if (response.ok) {
        // Update local state with new credit limit and remaining credits
        const updatedEmployers = employers.map(emp =>
          emp.id === selectedEmployer.id
            ? {
                ...emp,
                creditsLeft: result.data.creditsLeft,
                totalCredits: result.data.creditLimit,
                planName: result.data.planName,
                membershipStatus: result.data.creditsLeft > 5 ? 'active' : result.data.creditsLeft > 0 ? 'warning' : 'expired'
              }
            : emp
        );

        setEmployers(updatedEmployers);
        setFilteredEmployers(updatedEmployers.filter(emp => {
          // Apply current filters
          let matches = true;
          if (searchTerm) {
            matches = matches && (
              emp.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
              emp.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
              emp.company?.toLowerCase().includes(searchTerm.toLowerCase()) ||
              emp.designation?.toLowerCase().includes(searchTerm.toLowerCase())
            );
          }
          if (statusFilter !== "all") {
            matches = matches && emp.membershipStatus === statusFilter;
          }
          return matches;
        }));

        setShowEditModal(false);
        setSelectedEmployer(null);
        setEditCredits("");

        toast.success(`Credit limit updated and usage history reset for ${selectedEmployer.name}`);
      } else {
        console.error("Error updating credits:", result.message);
        toast.error(result.message || "Failed to update credit limit");
      }
    } catch (error) {
      console.error("Error updating credits:", error);
      toast.error("Failed to update credit limit");
    } finally {
      setIsUpdatingCredits(false);
    }
  };

  const handleTopUpCredits = async () => {
    if (!selectedEmployer) return;

    try {
      const topUpAmount = parseInt(editCredits);
      if (isNaN(topUpAmount) || topUpAmount < 0) {
        toast.error("Please enter a valid top-up amount");
        return;
      }

      // 🎯 TOP-UP VALIDATION: Enforce minimum top-up of 1 credit
      if (topUpAmount < 1) {
        toast.error("Top-up amount must be at least 1 credit");
        return;
      }

      setIsUpdatingCredits(true);

      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/admin/employer/${selectedEmployer.id}/credits`,
        {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ credits: topUpAmount, mode: 'topup' }),
        }
      );

      const result = await response.json();

      if (response.ok) {
        // Update local state with new credit limit and remaining credits
        const updatedEmployers = employers.map(emp =>
          emp.id === selectedEmployer.id
            ? {
                ...emp,
                creditsLeft: result.data.creditsLeft,
                totalCredits: result.data.creditLimit,
                planName: result.data.planName,
                membershipStatus: result.data.creditsLeft > 5 ? 'active' : result.data.creditsLeft > 0 ? 'warning' : 'expired'
              }
            : emp
        );

        setEmployers(updatedEmployers);
        setFilteredEmployers(updatedEmployers.filter(emp => {
          // Apply current filters
          let matches = true;
          if (searchTerm) {
            matches = matches && (
              emp.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
              emp.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
              emp.company?.toLowerCase().includes(searchTerm.toLowerCase()) ||
              emp.designation?.toLowerCase().includes(searchTerm.toLowerCase())
            );
          }
          if (statusFilter !== "all") {
            matches = matches && emp.membershipStatus === statusFilter;
          }
          return matches;
        }));

        setShowEditModal(false);
        setSelectedEmployer(null);
        setEditCredits("");

        toast.success(`${topUpAmount} credits added successfully for ${selectedEmployer.name}`);
      } else {
        console.error("Error topping up credits:", result.message);
        toast.error(result.message || "Failed to top up credits");
      }
    } catch (error) {
      console.error("Error topping up credits:", error);
      toast.error("Failed to top up credits");
    } finally {
      setIsUpdatingCredits(false);
    }
  };

  const handleCancelEdit = () => {
    setShowEditModal(false);
    setSelectedEmployer(null);
    setEditCredits("");
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatMobile = (mobile) => {
    if (!mobile) return 'N/A';
    const mobileStr = mobile.toString();
    if (mobileStr.length === 10) {
      return `+91 ${mobileStr.slice(0, 5)} ${mobileStr.slice(5)}`;
    }
    return mobileStr;
  };

  // Status Badge Component
  const StatusBadge = ({ status, type = "membership" }) => {
    const getStatusConfig = () => {
      if (type === "membership") {
        switch (status) {
          case 'active':
            return { bg: 'bg-green-100 dark:bg-green-900/30', text: 'text-green-800 dark:text-green-300', label: 'Active' };
          case 'warning':
            return { bg: 'bg-yellow-100 dark:bg-yellow-900/30', text: 'text-yellow-800 dark:text-yellow-300', label: 'Low Credits' };
          case 'expired':
            return { bg: 'bg-red-100 dark:bg-red-900/30', text: 'text-red-800 dark:text-red-300', label: 'Expired' };
          default:
            return { bg: 'bg-gray-100 dark:bg-gray-700', text: 'text-gray-800 dark:text-gray-300', label: 'Unknown' };
        }
      }
      return { bg: 'bg-gray-100 dark:bg-gray-700', text: 'text-gray-800 dark:text-gray-300', label: status };
    };

    const config = getStatusConfig();
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
        {config.label}
      </span>
    );
  };

  // Enhanced Employer Card Component
  const EmployerCard = ({ employer }) => {
    const creditsUsed = employer.totalCredits - employer.creditsLeft;
    const creditsUsagePercentage = employer.totalCredits > 0 ? 
      (creditsUsed / employer.totalCredits) * 100 : 0;

    return (
      <div className="group bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-xl hover:shadow-blue-500/10 transition-all duration-300 transform overflow-hidden">
        {/* Top gradient accent */}
        <div className={`h-1 bg-gradient-to-r ${
          employer.membershipStatus === 'active' ? 'from-green-400 to-emerald-500' :
          employer.membershipStatus === 'warning' ? 'from-yellow-400 to-amber-500' : 
          'from-red-400 to-rose-500'
        }`}></div>

        <div className="p-6">
          {/* Header with profile info and status */}
          <div className="flex items-start justify-between mb-6">
            <div className="flex items-center gap-4 flex-1 min-w-0">
              <div className="relative">
                <div className="w-16 h-16 rounded-2xl overflow-hidden ring-4 ring-gray-100 dark:ring-gray-700 group-hover:ring-blue-100 dark:group-hover:ring-blue-900/50 transition-all duration-300">
                  <img
                    src={
                      employer.profile_picture
                        ? employer.profile_picture.startsWith("http")
                          ? employer.profile_picture
                          : `${import.meta.env.VITE_APP_HOST}/${employer.profile_picture}`
                        : avatar
                    }
                    alt={employer.name}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                    onError={(e) => {
                      if (e.target.src !== avatar) {
                        e.target.onerror = null;
                        e.target.src = avatar;
                      }
                    }}
                  />
                </div>
                {/* Enhanced status indicator */}
                <div className={`absolute -bottom-1 -right-1 w-6 h-6 rounded-full border-3 border-white dark:border-gray-800 shadow-md ${
                  employer.membershipStatus === 'active' ? 'bg-gradient-to-br from-green-400 to-green-600' :
                  employer.membershipStatus === 'warning' ? 'bg-gradient-to-br from-yellow-400 to-yellow-600' : 
                  'bg-gradient-to-br from-red-400 to-red-600'
                } flex items-center justify-center`}>
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                </div>
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <h3 className="text-lg font-bold text-gray-900 dark:text-white truncate group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                    {employer.name}
                  </h3>
                  {employer.membershipStatus === 'active' && (
                    <div className="flex-shrink-0">
                      <Star className="w-4 h-4 text-yellow-500 fill-current" />
                    </div>
                  )}
                </div>
                
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                  {employer.designation} {employer.company && `at ${employer.company}`}
                </p>
                
                <div className="space-y-1">
                  <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                    <Mail className="w-4 h-4 flex-shrink-0 text-blue-500" />
                    <span className="truncate font-medium">{employer.email}</span>
                  </div>
                  
                  {employer.mobile && (
                    <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                      <Phone className="w-4 h-4 flex-shrink-0 text-green-500" />
                      <span className="font-medium">{formatMobile(employer.mobile)}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Enhanced status badge */}
            <div className="flex-shrink-0">
              <div className={`px-4 py-2 rounded-full text-xs font-bold border-2 backdrop-blur-sm ${
                employer.membershipStatus === 'active' 
                  ? 'bg-green-50 dark:bg-green-900/30 text-green-700 dark:text-green-300 border-green-200 dark:border-green-700 shadow-green-200/50' 
                  : employer.membershipStatus === 'warning' 
                  ? 'bg-yellow-50 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 border-yellow-200 dark:border-yellow-700 shadow-yellow-200/50' 
                  : 'bg-red-50 dark:bg-red-900/30 text-red-700 dark:text-red-300 border-red-200 dark:border-red-700 shadow-red-200/50'
              } shadow-lg`}>
                {employer.membershipStatus === 'active' ? 'Active' : 
                 employer.membershipStatus === 'warning' ? 'Warning' : 'Expired'}
              </div>
            </div>
          </div>

          {/* Enhanced Action button */}
          <div className="mb-6">
            <button
              onClick={() => handleEditCredits(employer)}
              className="group/btn relative overflow-hidden bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 hover:shadow-lg hover:shadow-purple-500/25 w-full"
            >
              <div className="relative z-10 flex items-center justify-center gap-2">
                <CreditCard className="w-4 h-4" />
                <span className="text-sm">Manage Credits</span>
                <ChevronRight className="w-4 h-4 group-hover/btn:translate-x-1 transition-transform" />
              </div>
              <div className="absolute inset-0 bg-white opacity-0 group-hover/btn:opacity-10 transition-opacity"></div>
            </button>
          </div>

          {/* Enhanced stats grid with better visual hierarchy */}
          <div className="grid grid-cols-2 gap-4 mb-6">
            <div className="relative overflow-hidden bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-xl p-4 border border-blue-200/50 dark:border-blue-700/50">
              <div className="relative z-10">
                <div className="flex items-center justify-between mb-2">
                  <div className="text-2xl font-bold text-blue-700 dark:text-blue-300">
                    {employer.creditsLeft}
                  </div>
                  <div className="bg-blue-200 dark:bg-blue-800 p-2 rounded-lg">
                    <HiOutlineSparkles className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                  </div>
                </div>
                <div className="text-xs font-semibold text-blue-600 dark:text-blue-400 uppercase tracking-wide">
                  Credits Left
                </div>
              </div>
              <div className="absolute top-0 right-0 w-20 h-20 bg-blue-200/20 dark:bg-blue-400/10 rounded-full -translate-y-10 translate-x-10"></div>
            </div>
            
            <div className="relative overflow-hidden bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-xl p-4 border border-green-200/50 dark:border-green-700/50">
              <div className="relative z-10">
                <div className="flex items-center justify-between mb-2">
                  <div className="text-2xl font-bold text-green-700 dark:text-green-300">
                    {employer.profilesUnlocked || 0}
                  </div>
                  <div className="bg-green-200 dark:bg-green-800 p-2 rounded-lg">
                    <Users className="w-4 h-4 text-green-600 dark:text-green-400" />
                  </div>
                </div>
                <div className="text-xs font-semibold text-green-600 dark:text-green-400 uppercase tracking-wide">
                  Profiles Unlocked
                </div>
              </div>
              <div className="absolute top-0 right-0 w-20 h-20 bg-green-200/20 dark:bg-green-400/10 rounded-full -translate-y-10 translate-x-10"></div>
            </div>
            
            <div className="relative overflow-hidden bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-xl p-4 border border-purple-200/50 dark:border-purple-700/50">
              <div className="relative z-10">
                <div className="flex items-center justify-between mb-2">
                  <div className="text-sm font-bold text-purple-700 dark:text-purple-300">
                    {employer.planName}
                  </div>
                  <div className="bg-purple-200 dark:bg-purple-800 p-2 rounded-lg">
                    <Crown className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                  </div>
                </div>
                <div className="text-xs font-semibold text-purple-600 dark:text-purple-400 uppercase tracking-wide">
                  Current Plan
                </div>
              </div>
              <div className="absolute top-0 right-0 w-20 h-20 bg-purple-200/20 dark:bg-purple-400/10 rounded-full -translate-y-10 translate-x-10"></div>
            </div>
            
            <div className="relative overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900/20 dark:to-gray-800/20 rounded-xl p-4 border border-gray-200/50 dark:border-gray-700/50">
              <div className="relative z-10">
                <div className="flex items-center justify-between mb-2">
                  <div className="text-sm font-bold text-gray-700 dark:text-gray-300">
                    {formatDate(employer.lastActive)}
                  </div>
                  <div className="bg-gray-200 dark:bg-gray-700 p-2 rounded-lg">
                    <Activity className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                  </div>
                </div>
                <div className="text-xs font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wide">
                  Last Active
                </div>
              </div>
              <div className="absolute top-0 right-0 w-20 h-20 bg-gray-200/20 dark:bg-gray-400/10 rounded-full -translate-y-10 translate-x-10"></div>
            </div>
          </div>

          {/* Enhanced credits usage progress bar */}
          <div className="mb-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-xs font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wide">Credit Usage</span>
              <span className="text-xs font-bold text-gray-700 dark:text-gray-300">
                {employer.creditsLeft} / {employer.totalCredits}
              </span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 overflow-hidden">
              <div 
                className={`h-2 rounded-full transition-all duration-500 ease-out ${
                  employer.membershipStatus === 'active' ? 'bg-gradient-to-r from-green-500 to-green-600' :
                  employer.membershipStatus === 'warning' ? 'bg-gradient-to-r from-yellow-500 to-yellow-600' : 
                  'bg-gradient-to-r from-red-500 to-red-600'
                }`}
                style={{
                  width: `${Math.max(0, Math.min(100, creditsUsagePercentage))}%`
                }}
              ></div>
            </div>
          </div>
          
          {/* Enhanced footer with better typography and spacing */}
          <div className="flex items-center justify-between pt-4 border-t border-gray-100 dark:border-gray-700">
            <div className="flex items-center gap-3">
              <div className="bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/30 dark:to-blue-800/30 p-2 rounded-lg">
                <Calendar className="w-4 h-4 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <div className="text-sm font-bold text-gray-900 dark:text-white">
                  {formatDate(employer.joinDate)}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  Joined Date
                </div>
              </div>
            </div>
            
            {employer.company && (
              <div className="text-right">
                <div className="text-sm font-bold text-gray-900 dark:text-white flex items-center gap-1">
                  <Building2 className="w-4 h-4 text-gray-500" />
                  {employer.company}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  Company
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="container mx-auto px-4 py-4 mb-4 max-w-7xl">
        {/* Search with Results Summary */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-l border border-gray-200 dark:border-gray-700 p-4 sm:p-6 mb-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex-1 min-w-0">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search employers..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4 items-center">
              <div className="relative">
                <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="pl-10 pr-8 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white appearance-none"
                >
                  <option value="all">All Status</option>
                  <option value="active">Active</option>
                  <option value="warning">Low Credits</option>
                  <option value="expired">Expired</option>
                </select>
              </div>
              
              <div className="sm:ml-4 lg:ml-6 flex-shrink-0">
                <p className="text-sm text-gray-600 dark:text-gray-400 sm:whitespace-nowrap text-center sm:text-left">
                  Showing {filteredEmployers.length} of {employers.length} employers
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Employers</p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">{employers.length}</p>
              </div>
              <div className="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-lg">
                <Users className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Active Employers</p>
                <p className="text-3xl font-bold text-green-600 dark:text-green-400">
                  {employers.filter(emp => emp.membershipStatus === 'active').length}
                </p>
              </div>
              <div className="bg-green-100 dark:bg-green-900/30 p-3 rounded-lg">
                <Activity className="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Low Credits</p>
                <p className="text-3xl font-bold text-yellow-600 dark:text-yellow-400">
                  {employers.filter(emp => emp.membershipStatus === 'warning').length}
                </p>
              </div>
              <div className="bg-yellow-100 dark:bg-yellow-900/30 p-3 rounded-lg">
                <CreditCard className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Expired</p>
                <p className="text-3xl font-bold text-red-600 dark:text-red-400">
                  {employers.filter(emp => emp.membershipStatus === 'expired').length}
                </p>
              </div>
              <div className="bg-red-100 dark:bg-red-900/30 p-3 rounded-lg">
                <Crown className="w-6 h-6 text-red-600 dark:text-red-400" />
              </div>
            </div>
          </div>
        </div>
        
        {/* Employers Grid */}
        {loading ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {[...Array(6)].map((_, index) => (
              <div key={index} className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 animate-pulse">
                <div className="flex items-center gap-4 mb-4">
                  <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
                    <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3"></div>
                  </div>
                </div>
                <div className="grid grid-cols-4 gap-4">
                  {[...Array(4)].map((_, i) => (
                    <div key={i} className="h-16 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        ) : filteredEmployers.length > 0 ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredEmployers.map((employer) => (
              <EmployerCard key={employer.id} employer={employer} />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <Building2 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No employers found
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              {searchTerm || statusFilter !== "all"
                ? "Try adjusting your search criteria or filters"
                : "No employers have registered yet"
              }
            </p>
          </div>
        )}

        {/* Credit Management Modal */}
        <CreditManagementModal
          isOpen={showEditModal}
          onClose={handleCancelEdit}
          onSave={handleSaveCredits}
          onTopUp={handleTopUpCredits}
          selectedEntity={selectedEmployer}
          entityType="employer"
          value={editCredits}
          setValue={setEditCredits}
          isUpdating={isUpdatingCredits}
          StatusBadge={StatusBadge}
        />
      </div>
    </div>
  );
};

export default EmployerProfiles;