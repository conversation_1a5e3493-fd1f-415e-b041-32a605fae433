import React from "react";
import { HiOutlineHome, HiOutlineBriefcase, HiOutlineAcademicCap } from "react-icons/hi";
import { CheckCircle } from "lucide-react";

const JobPreference = ({
  inviteDataToUse,
  formData,
  handleInputChange,
}) => (
  <div className="space-y-6">
    <div className="text-center">
      <div className="mx-auto mb-4 flex h-14 w-14 items-center justify-center rounded-lg bg-blue-50 border border-blue-100">
        <HiOutlineBriefcase className="h-7 w-7 text-blue-600" />
      </div>
      <h1 className="mb-2 text-2xl font-semibold text-gray-900">
        Job Preference
      </h1>
      <p className="text-gray-600">
        {inviteDataToUse
          ? "Job preference from employer invitation"
          : "Select your preferred job types (you can choose multiple)"}
      </p>
      {inviteDataToUse && inviteDataToUse.jobPreference && (
        <div className="mt-4 rounded-lg border border-blue-200 bg-blue-50 p-4">
          <div className="flex items-center justify-center gap-3">
            <HiOutlineBriefcase className="h-5 w-5 text-blue-600" />
            <span className="font-medium text-blue-800">
              Pre-selected: {inviteDataToUse.jobPreference.join(", ")}
            </span>
          </div>
        </div>
      )}
    </div>

    <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
      {/* Remote Option */}
      <label
        className={`relative cursor-pointer rounded-lg border-2 p-4 transition-all duration-200 ${
          (formData.jobPreference || []).includes("Remote")
            ? "border-blue-500 bg-blue-50 shadow-sm"
            : "border-gray-200 bg-white hover:border-blue-300 hover:shadow-sm"
        }`}
      >
        <input
          type="checkbox"
          name="jobPreference"
          value="Remote"
          checked={(formData.jobPreference || []).includes("Remote")}
          onChange={handleInputChange}
          className="sr-only"
        />
        <div className="text-center">
          <div
            className={`mx-auto mb-3 flex h-10 w-10 items-center justify-center rounded-lg ${
              (formData.jobPreference || []).includes("Remote")
                ? "bg-blue-100"
                : "bg-gray-100"
            }`}
          >
            <HiOutlineHome
              className={`h-6 w-6 ${
                (formData.jobPreference || []).includes("Remote")
                  ? "text-blue-600"
                  : "text-gray-500"
              }`}
            />
          </div>
          <h3 className="mb-2 font-semibold text-gray-900">
            Remote
          </h3>
          <p className="text-sm text-gray-600">
            Work from anywhere
          </p>
        </div>
        {(formData.jobPreference || []).includes("Remote") && (
          <div className="absolute right-3 top-3">
            <CheckCircle className="h-5 w-5 text-blue-600" />
          </div>
        )}
      </label>

      {/* Full Time Option */}
      <label
        className={`relative cursor-pointer rounded-lg border-2 p-4 transition-all duration-200 ${
          (formData.jobPreference || []).includes("Full Time")
            ? "border-blue-500 bg-blue-50 shadow-sm"
            : "border-gray-200 bg-white hover:border-blue-300 hover:shadow-sm"
        }`}
      >
        <input
          type="checkbox"
          name="jobPreference"
          value="Full Time"
          checked={(formData.jobPreference || []).includes("Full Time")}
          onChange={handleInputChange}
          className="sr-only"
        />
        <div className="text-center">
          <div
            className={`mx-auto mb-3 flex h-10 w-10 items-center justify-center rounded-lg ${
              (formData.jobPreference || []).includes("Full Time")
                ? "bg-blue-100"
                : "bg-gray-100"
            }`}
          >
            <HiOutlineBriefcase
              className={`h-6 w-6 ${
                (formData.jobPreference || []).includes("Full Time")
                  ? "text-blue-600"
                  : "text-gray-500"
              }`}
            />
          </div>
          <h3 className="mb-2 font-semibold text-gray-900">
            Full Time
          </h3>
          <p className="text-sm text-gray-600">
            Permanent employment
          </p>
        </div>
        {(formData.jobPreference || []).includes("Full Time") && (
          <div className="absolute right-3 top-3">
            <CheckCircle className="h-5 w-5 text-blue-600" />
          </div>
        )}
      </label>

      {/* Internship Option */}
      <label
        className={`relative cursor-pointer rounded-lg border-2 p-4 transition-all duration-200 ${
          (formData.jobPreference || []).includes("Internship")
            ? "border-blue-500 bg-blue-50 shadow-sm"
            : "border-gray-200 bg-white hover:border-blue-300 hover:shadow-sm"
        }`}
      >
        <input
          type="checkbox"
          name="jobPreference"
          value="Internship"
          checked={(formData.jobPreference || []).includes("Internship")}
          onChange={handleInputChange}
          className="sr-only"
        />
        <div className="text-center">
          <div
            className={`mx-auto mb-3 flex h-10 w-10 items-center justify-center rounded-lg ${
              (formData.jobPreference || []).includes("Internship")
                ? "bg-blue-100"
                : "bg-gray-100"
            }`}
          >
            <HiOutlineAcademicCap
              className={`h-6 w-6 ${
                (formData.jobPreference || []).includes("Internship")
                  ? "text-blue-600"
                  : "text-gray-500"
              }`}
            />
          </div>
          <h3 className="mb-2 font-semibold text-gray-900">
            Internship
          </h3>
          <p className="text-sm text-gray-600">
            Training or short-term role
          </p>
        </div>
        {(formData.jobPreference || []).includes("Internship") && (
          <div className="absolute right-3 top-3">
            <CheckCircle className="h-5 w-5 text-blue-600" />
          </div>
        )}
      </label>
    </div>
  </div>
);

export default JobPreference;