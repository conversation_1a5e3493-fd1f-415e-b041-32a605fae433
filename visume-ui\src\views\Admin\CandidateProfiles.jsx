import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  Users,
  Search,
  Filter,
  Crown,
  Edit3,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Video,
  FileText,
  Star,
  Eye,
  Building2,
  ChevronRight,
  Activity
} from "lucide-react";
import { HiOutlineSparkles } from "react-icons/hi";
import { AdminSearchBar, StatusBadge, FilterDropdown } from "./components/AdminComponents";
import CreditManagementModal from "./components/CreditManagementModal";
import avatar from "assets/img/avatars/avatar4.png";
import toast from "react-hot-toast";

const CandidateProfiles = () => {
  const navigate = useNavigate();
  const [candidates, setCandidates] = useState([]);
  const [filteredCandidates, setFilteredCandidates] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [planFilter, setPlanFilter] = useState("");

  // Credit management state
  const [showCreditModal, setShowCreditModal] = useState(false);
  const [selectedCandidateForCredits, setSelectedCandidateForCredits] = useState(null);
  const [editCredits, setEditCredits] = useState("");
  const [isUpdatingCredits, setIsUpdatingCredits] = useState(false);

  // API base URL
  const API_BASE_URL = import.meta.env.VITE_APP_HOST;

  useEffect(() => {
    const fetchCandidates = async () => {
      setLoading(true);
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/admin/candidates`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.message || `HTTP error! status: ${response.status}`);
        }

        // Transform the API data to match the component's expected format
        const transformedCandidates = data.data.candidates.map(candidate => ({
          id: candidate.id,
          cand_id: candidate.cand_id,
          name: candidate.name,
          email: candidate.email,
          phone: candidate.phone || 'N/A',
          profile_picture: candidate.profile_picture,
          // Credit-related information (renamed for clarity)
          // Initialize with backend list values; will be enriched with lifetime usage below
          creditsUsed: candidate.currentVisumeCount,
          creditsAssigned: candidate.allowedVisumes,
          planName: candidate.planName,
          membershipStatus: candidate.membershipStatus,
          joinDate: candidate.joinDate,
          skills: candidate.skills || 'No skills listed',
          location: candidate.location || 'Location not specified',
          // Add some default values for display
          experience: 'N/A', // This could be calculated or added to API later
          profileViews: 0, // This could be added to API later
          shortlisted: 0 // This could be added to API later
        }));

        // Enrich with lifetime credit usage from membership endpoint (visumesUsed)
        const enrichedCandidates = await Promise.all(
          transformedCandidates.map(async (c) => {
            try {
              const res = await fetch(`${API_BASE_URL}/api/v1/candidate-membership/${c.cand_id}`);
              if (!res.ok) return c;
              const membership = await res.json();
              const data = membership?.data || {};
              return {
                ...c,
                creditsUsed: typeof data.visumesUsed === 'number' ? data.visumesUsed : c.creditsUsed,
                creditsAssigned: typeof data.allowedVisumes === 'number' ? data.allowedVisumes : c.creditsAssigned,
              };
            } catch {
              return c;
            }
          })
        );

        setCandidates(enrichedCandidates);
        setFilteredCandidates(enrichedCandidates);
      } catch (error) {
        console.error("Error fetching candidates:", error);
        toast.error("Failed to load candidates: " + error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchCandidates();
  }, []);

  // Filter candidates based on search and filters
  useEffect(() => {
    let filtered = candidates;

    if (searchTerm) {
      filtered = filtered.filter(candidate => 
        candidate.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        candidate.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        candidate.skills.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter) {
      filtered = filtered.filter(candidate => candidate.membershipStatus === statusFilter);
    }

    if (planFilter) {
      filtered = filtered.filter(candidate => candidate.planName === planFilter);
    }

    setFilteredCandidates(filtered);
  }, [searchTerm, statusFilter, planFilter, candidates]);

  const handleViewAllVisumes = (candidate) => {
    // Navigate to the new candidate visumes page
    navigate(`/admin/candidate/${candidate.cand_id}/visumes`);
  };

  const handleManageCredits = (candidate) => {
    setSelectedCandidateForCredits(candidate);
    // Set initial value to current credits assigned, minimum 1
    const currentCredits = candidate.creditsAssigned || 1;
    setEditCredits(Math.max(currentCredits, 1).toString());
    setShowCreditModal(true);
  };

  const handleSaveCredits = async () => {
    if (!selectedCandidateForCredits) return;

    setIsUpdatingCredits(true);
    try {
      const newCredits = parseInt(editCredits);
      if (isNaN(newCredits) || newCredits < 1) {
        toast.error("Please enter a valid number of credits (minimum 1)");
        return;
      }

      const response = await fetch(`${API_BASE_URL}/api/v1/admin/candidate/${selectedCandidateForCredits.cand_id}/limit`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          visume_limit: newCredits,
          mode: 'replace'
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      // Update local state
      const updatedCandidates = candidates.map(candidate =>
        candidate.cand_id === selectedCandidateForCredits.cand_id
          ? {
              ...candidate,
              creditsAssigned: newCredits,
              creditsUsed: 0, // Reset usage as per API behavior
              membershipStatus: newCredits > 0 ? 'active' : 'expired'
            }
          : candidate
      );

      setCandidates(updatedCandidates);
      setFilteredCandidates(updatedCandidates.filter(candidate => {
        let filtered = [candidate];

        if (searchTerm) {
          filtered = filtered.filter(c =>
            c.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            c.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
            c.skills.toLowerCase().includes(searchTerm.toLowerCase())
          );
        }

        if (statusFilter) {
          filtered = filtered.filter(c => c.membershipStatus === statusFilter);
        }

        if (planFilter) {
          filtered = filtered.filter(c => c.planName === planFilter);
        }

        return filtered.length > 0;
      }));

      setShowCreditModal(false);
      setSelectedCandidateForCredits(null);
      setEditCredits("");

      toast.success(`Credits updated successfully for ${selectedCandidateForCredits.name}. Usage history has been reset.`);
    } catch (error) {
      console.error("Error updating credits:", error);
      toast.error("Failed to update credits: " + error.message);
    } finally {
      setIsUpdatingCredits(false);
    }
  };

  const handleTopUpCredits = async () => {
    if (!selectedCandidateForCredits) return;

    setIsUpdatingCredits(true);
    try {
      const topUpAmount = parseInt(editCredits);
      if (isNaN(topUpAmount) || topUpAmount < 1) {
        toast.error("Please enter a valid top-up amount (minimum 1)");
        return;
      }

      const response = await fetch(`${API_BASE_URL}/api/v1/admin/candidate/${selectedCandidateForCredits.cand_id}/limit`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          visume_limit: topUpAmount,
          mode: 'topup'
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      // Update local state
      const updatedCandidates = candidates.map(candidate =>
        candidate.cand_id === selectedCandidateForCredits.cand_id
          ? {
              ...candidate,
              creditsAssigned: data.data.visumeLimit,
              creditsUsed: data.data.visumesUsed,
              allowedVisumes: data.data.allowedVisumes,
              membershipStatus: data.data.allowedVisumes > 0 ? 'active' : 'expired'
            }
          : candidate
      );

      setCandidates(updatedCandidates);
      setFilteredCandidates(updatedCandidates.filter(candidate => {
        let filtered = [candidate];

        if (searchTerm) {
          filtered = filtered.filter(c =>
            c.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            c.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
            c.skills.toLowerCase().includes(searchTerm.toLowerCase())
          );
        }

        if (statusFilter !== "all") {
          filtered = filtered.filter(c => c.membershipStatus === statusFilter);
        }

        return filtered.length > 0;
      }));

      setShowCreditModal(false);
      setSelectedCandidateForCredits(null);
      setEditCredits("");

      toast.success(`${topUpAmount} visumes added successfully for ${selectedCandidateForCredits.name}`);
    } catch (error) {
      console.error("Error topping up visumes:", error);
      toast.error("Failed to top up visumes: " + error.message);
    } finally {
      setIsUpdatingCredits(false);
    }
  };

  const handleCancelCreditEdit = () => {
    setShowCreditModal(false);
    setSelectedCandidateForCredits(null);
    setEditCredits("");
  };

  const formatMobile = (mobile) => {
    if (!mobile || mobile === 'N/A') return 'N/A';
    return mobile.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Enhanced Candidate Card Component
  const CandidateCard = ({ candidate }) => {
    const creditsRemaining = candidate.creditsAssigned - candidate.creditsUsed;
    const creditsUsagePercentage = candidate.creditsAssigned > 0 ? 
      (candidate.creditsUsed / candidate.creditsAssigned) * 100 : 0;

    return (
      <div className="group bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
        {/* Top gradient accent */}
        <div className={`h-1 bg-gradient-to-r ${
          candidate.membershipStatus === 'active' ? 'from-green-400 to-emerald-500' :
          candidate.membershipStatus === 'warning' ? 'from-yellow-400 to-amber-500' : 
          'from-red-400 to-rose-500'
        }`}></div>

        <div className="p-6">
          {/* Header with profile info and status */}
          <div className="flex items-start justify-between mb-6">
            <div className="flex items-center gap-4 flex-1 min-w-0">
              <div className="relative">
                <div className="w-16 h-16 rounded-2xl overflow-hidden ring-4 ring-gray-100 dark:ring-gray-700">
                  <img
                    src={
                      candidate.profile_picture
                        ? candidate.profile_picture.startsWith("http")
                          ? candidate.profile_picture
                          : `${API_BASE_URL}/${candidate.profile_picture}`
                        : avatar
                    }
                    alt={candidate.name}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      if (e.target.src !== avatar) {
                        e.target.onerror = null;
                        e.target.src = avatar;
                      }
                    }}
                  />
                </div>
                {/* Enhanced status indicator */}
                <div className={`absolute -bottom-1 -right-1 w-6 h-6 rounded-full border-3 border-white dark:border-gray-800 shadow-md ${
                  candidate.membershipStatus === 'active' ? 'bg-gradient-to-br from-green-400 to-green-600' :
                  candidate.membershipStatus === 'warning' ? 'bg-gradient-to-br from-yellow-400 to-yellow-600' : 
                  'bg-gradient-to-br from-red-400 to-red-600'
                } flex items-center justify-center`}>
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                </div>
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <h3 className="text-lg font-bold text-gray-900 dark:text-white truncate">
                    {candidate.name}
                  </h3>
                  {candidate.membershipStatus === 'active' && (
                    <div className="flex-shrink-0">
                      <Star className="w-4 h-4 text-yellow-500 fill-current" />
                    </div>
                  )}
                </div>
                
                <div className="space-y-1">
                  <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                    <Mail className="w-4 h-4 flex-shrink-0 text-blue-500" />
                    <span className="truncate font-medium">{candidate.email}</span>
                  </div>
                  
                  <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                    <Phone className="w-4 h-4 flex-shrink-0 text-green-500" />
                    <span className="font-medium">{formatMobile(candidate.phone)}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Enhanced status badge */}
            <div className="flex-shrink-0">
              <div className={`px-4 py-2 rounded-full text-xs font-bold border-2 backdrop-blur-sm ${
                candidate.membershipStatus === 'active' 
                  ? 'bg-green-50 dark:bg-green-900/30 text-green-700 dark:text-green-300 border-green-200 dark:border-green-700 shadow-green-200/50' 
                  : candidate.membershipStatus === 'warning' 
                  ? 'bg-yellow-50 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 border-yellow-200 dark:border-yellow-700 shadow-yellow-200/50' 
                  : 'bg-red-50 dark:bg-red-900/30 text-red-700 dark:text-red-300 border-red-200 dark:border-red-700 shadow-red-200/50'
              } shadow-lg`}>
                {candidate.membershipStatus === 'active' ? 'Active' : 
                 candidate.membershipStatus === 'warning' ? 'Warning' : 'Expired'}
              </div>
            </div>
          </div>

          {/* Enhanced Action buttons with better spacing and effects */}
          <div className="mb-6">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              <button
                onClick={() => handleViewAllVisumes(candidate)}
                className="group/btn relative overflow-hidden bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-200 transform hover:scale-105 hover:shadow-lg hover:shadow-blue-500/25"
              >
                <div className="relative z-10 flex items-center justify-center gap-2">
                  <Video className="w-4 h-4" />
                  <span className="text-sm">View Visumes</span>
                  <ChevronRight className="w-4 h-4 group-hover/btn:translate-x-1 transition-transform" />
                </div>
                <div className="absolute inset-0 bg-white opacity-0 group-hover/btn:opacity-10 transition-opacity"></div>
              </button>
              
              <button
                onClick={() => handleManageCredits(candidate)}
                className="group/btn relative overflow-hidden bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-200 transform hover:scale-105 hover:shadow-lg hover:shadow-purple-500/25"
              >
                <div className="relative z-10 flex items-center justify-center gap-2">
                  <Edit3 className="w-4 h-4" />
                  <span className="text-sm">Manage Credits</span>
                </div>
                <div className="absolute inset-0 bg-white opacity-0 group-hover/btn:opacity-10 transition-opacity"></div>
              </button>
            </div>
          </div>

          {/* Enhanced stats grid with better visual hierarchy */}
          <div className="grid grid-cols-2 gap-4 mb-6">
            <div className="relative overflow-hidden bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-xl p-4 border border-blue-200/50 dark:border-blue-700/50">
              <div className="relative z-10">
                <div className="flex items-center justify-between mb-2">
                  <div className="text-2xl font-bold text-blue-700 dark:text-blue-300">
                    {candidate.creditsAssigned}
                  </div>
                  <div className="bg-blue-200 dark:bg-blue-800 p-2 rounded-lg">
                    <Crown className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                  </div>
                </div>
                <div className="text-xs font-semibold text-blue-600 dark:text-blue-400 uppercase tracking-wide">
                  Total Credits
                </div>
              </div>
              <div className="absolute top-0 right-0 w-20 h-20 bg-blue-200/20 dark:bg-blue-400/10 rounded-full -translate-y-10 translate-x-10"></div>
            </div>
            
            <div className="relative overflow-hidden bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-xl p-4 border border-purple-200/50 dark:border-purple-700/50">
              <div className="relative z-10">
                <div className="flex items-center justify-between mb-2">
                  <div className="text-2xl font-bold text-purple-700 dark:text-purple-300">
                    {candidate.creditsUsed}
                  </div>
                  <div className="bg-purple-200 dark:bg-purple-800 p-2 rounded-lg">
                    <Activity className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                  </div>
                </div>
                <div className="text-xs font-semibold text-purple-600 dark:text-purple-400 uppercase tracking-wide">
                  Credits Used
                </div>
              </div>
              <div className="absolute top-0 right-0 w-20 h-20 bg-purple-200/20 dark:bg-purple-400/10 rounded-full -translate-y-10 translate-x-10"></div>
            </div>
            
            <div className="relative overflow-hidden bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-xl p-4 border border-green-200/50 dark:border-green-700/50">
              <div className="relative z-10">
                <div className="flex items-center justify-between mb-2">
                  <div className="text-2xl font-bold text-green-700 dark:text-green-300">
                    {creditsRemaining}
                  </div>
                  <div className="bg-green-200 dark:bg-green-800 p-2 rounded-lg">
                    <HiOutlineSparkles className="w-4 h-4 text-green-600 dark:text-green-400" />
                  </div>
                </div>
                <div className="text-xs font-semibold text-green-600 dark:text-green-400 uppercase tracking-wide">
                  Remaining
                </div>
              </div>
              <div className="absolute top-0 right-0 w-20 h-20 bg-green-200/20 dark:bg-green-400/10 rounded-full -translate-y-10 translate-x-10"></div>
            </div>
            
            <div className="relative overflow-hidden bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 rounded-xl p-4 border border-orange-200/50 dark:border-orange-700/50">
              <div className="relative z-10">
                <div className="flex items-center justify-between mb-2">
                  <div className="text-2xl font-bold text-orange-700 dark:text-orange-300">
                    {candidate.shortlisted}
                  </div>
                  <div className="bg-orange-200 dark:bg-orange-800 p-2 rounded-lg">
                    <Star className="w-4 h-4 text-orange-600 dark:text-orange-400" />
                  </div>
                </div>
                <div className="text-xs font-semibold text-orange-600 dark:text-orange-400 uppercase tracking-wide">
                  Shortlisted
                </div>
              </div>
              <div className="absolute top-0 right-0 w-20 h-20 bg-orange-200/20 dark:bg-orange-400/10 rounded-full -translate-y-10 translate-x-10"></div>
            </div>
          </div>

          {/* Enhanced credits usage progress bar */}
          <div className="mb-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-xs font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wide">Credit Usage</span>
              <span className="text-xs font-bold text-gray-700 dark:text-gray-300">{Math.round(creditsUsagePercentage)}%</span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 overflow-hidden">
              <div 
                className={`h-2 rounded-full transition-all duration-500 ease-out ${
                  creditsUsagePercentage >= 90 ? 'bg-gradient-to-r from-red-500 to-red-600' :
                  creditsUsagePercentage >= 70 ? 'bg-gradient-to-r from-yellow-500 to-yellow-600' :
                  'bg-gradient-to-r from-green-500 to-green-600'
                }`}
                style={{ width: `${Math.min(creditsUsagePercentage, 100)}%` }}
              ></div>
            </div>
          </div>
          
          {/* Enhanced footer with better typography and spacing */}
          <div className="flex items-center justify-between pt-4 border-t border-gray-100 dark:border-gray-700">
            <div className="flex items-center gap-3">
              <div className="bg-gradient-to-br from-purple-100 to-purple-200 dark:from-purple-900/30 dark:to-purple-800/30 p-2 rounded-lg">
                <Crown className="w-4 h-4 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <div className="text-sm font-bold text-gray-900 dark:text-white">
                  {candidate.planName}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  Membership Plan
                </div>
              </div>
            </div>
            
            <div className="text-right">
              <div className="text-xs font-semibold text-gray-700 dark:text-gray-300">
                Joined {formatDate(candidate.joinDate)}
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                Member since
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="container mx-auto px-4 py-4 mb-4 max-w-7xl">
        {/* Search with Results Summary */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-l border border-gray-200 dark:border-gray-700 p-4 sm:p-6 mb-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex-1 min-w-0">
              <AdminSearchBar
                searchTerm={searchTerm}
                setSearchTerm={setSearchTerm}
                placeholder="Search by name, email, or skills"
                loading={loading}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
            </div>
            <div className="sm:ml-4 lg:ml-6 flex-shrink-0">
              <p className="text-sm text-gray-600 dark:text-gray-400 sm:whitespace-nowrap text-center sm:text-left">
                Showing {filteredCandidates.length} of {candidates.length} candidates
              </p>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Candidates</p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">{candidates.length}</p>
              </div>
              <div className="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-lg">
                <Users className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Active Candidates</p>
                <p className="text-3xl font-bold text-green-600 dark:text-green-400">
                  {candidates.filter(candidate => candidate.membershipStatus === 'active').length}
                </p>
              </div>
              <div className="bg-green-100 dark:bg-green-900/30 p-3 rounded-lg">
                <Star className="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Low Credits</p>
                <p className="text-3xl font-bold text-yellow-600 dark:text-yellow-400">
                  {candidates.filter(candidate => candidate.membershipStatus === 'warning').length}
                </p>
              </div>
              <div className="bg-yellow-100 dark:bg-yellow-900/30 p-3 rounded-lg">
                <FileText className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Expired</p>
                <p className="text-3xl font-bold text-red-600 dark:text-red-400">
                  {candidates.filter(candidate => candidate.membershipStatus === 'expired').length}
                </p>
              </div>
              <div className="bg-red-100 dark:bg-red-900/30 p-3 rounded-lg">
                <Crown className="w-6 h-6 text-red-600 dark:text-red-400" />
              </div>
            </div>
          </div>
        </div>

        {/* Candidates Grid */}
        {loading ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {[...Array(6)].map((_, index) => (
              <div key={index} className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 animate-pulse">
                <div className="flex items-center gap-4 mb-4">
                  <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
                    <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3"></div>
                  </div>
                </div>
                <div className="grid grid-cols-4 gap-4">
                  {[...Array(4)].map((_, i) => (
                    <div key={i} className="h-16 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        ) : filteredCandidates.length > 0 ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredCandidates.map((candidate) => (
              <CandidateCard key={candidate.id} candidate={candidate} />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <Users className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No candidates found
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              Try adjusting your search criteria or filters
            </p>
          </div>
        )}

        {/* Credit Management Modal */}
        <CreditManagementModal
          isOpen={showCreditModal}
          onClose={handleCancelCreditEdit}
          onSave={handleSaveCredits}
          onTopUp={handleTopUpCredits}
          selectedEntity={selectedCandidateForCredits}
          entityType="candidate"
          value={editCredits}
          setValue={setEditCredits}
          isUpdating={isUpdatingCredits}
          StatusBadge={StatusBadge}
        />
      </div>
    </div>
  );
};

export default CandidateProfiles;