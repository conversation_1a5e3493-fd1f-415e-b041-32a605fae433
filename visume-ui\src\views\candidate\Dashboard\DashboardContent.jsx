import React, { useState } from "react";
import defaultProfileImg from "assets/img/default-profile.png";
import JobCard from "../components/JobCard";
import CreateVR from "../components/CreateVR/CreateVR";
import VideoProfileCard from "../components/VideoProfileCard";
import VidProfPopup from "../components/VidProfPopup";
import UpgradeModal from "../components/UpgradeModal";
import { HiOutlineSparkles, HiVideoCamera } from "react-icons/hi";
import SmLoader from "../../../components/SmLoader";
import {
  Eye,
  Video,
  ClipboardCheck,
  MousePointerClick,
  Unlock,
  ArrowUpCircle,
} from "lucide-react";

export default function DashboardContent({
  jstoken,
  candData,
  loadingInfo,
  togglePopupVR,
  profile_picture,
  imageError,
  setImageError,
  userStats,
  jobData,
  videoProfiles,
  isLoading,
  createVRpopup,
  showVideoProfilePopup,
  toggleVideoProfilePopup,
  membershipStatus,
  membershipLoading,
  inviteData,
}) {
  // Add state for the upgrade modal
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);

  // Function to toggle the upgrade modal
  const toggleUpgradeModal = () => {
    setShowUpgradeModal(prev => !prev);
  };

  return (
    <>
      {jstoken ? (
        <div className="space-y-5">
          {/* Profile Card with Stats */}
          <div className="bg-white dark:bg-gray-800 rounded-xl border border-blue-100 dark:border-gray-700 hover:border-blue-200 transition-colors duration-200">
            {/* Profile Header */}
            <div className="p-5">
              <div className="flex items-center justify-between">
              {/* Left: Profile Section */}
              <div className="flex items-center gap-3">
                {imageError ? (
                  <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-br from-blue-500 to-purple-600 text-sm font-semibold text-white">
                    {candData && candData?.cand_name[0]?.toUpperCase()}
                  </div>
                ) : (
                  <img
                    className="h-10 w-10 rounded-full border border-gray-200 object-cover dark:border-gray-600"
                    src={profile_picture || defaultProfileImg}
                    alt={candData?.cand_name}
                    onError={(e) => {
                      if (e.target.src !== defaultProfileImg) {
                        e.target.onerror = null;
                        e.target.src = defaultProfileImg;
                      }
                      setImageError(true);
                    }}
                  />
                )}
                <div>
                  <h2 className="text-base font-semibold text-gray-900 dark:text-white">
                    {loadingInfo ? (
                      <div className="h-4 w-24 animate-pulse rounded bg-gray-200 dark:bg-gray-700" />
                    ) : (
                      candData.cand_name
                    )}
                  </h2>
                  <div className="flex items-center gap-2 mt-1">
                    <span className="inline-flex items-center rounded-full bg-blue-50 dark:bg-blue-900/20 px-2 py-0.5 text-xs font-medium text-blue-700 dark:text-blue-300">
                      {membershipStatus?.planName || "Free Plan"}
                    </span>
                    <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400">
                      <Video className="w-3 h-3" />
                      <span>
                        {membershipStatus ?
                          `${membershipStatus.visumesUsed !== undefined ? membershipStatus.visumesUsed : (membershipStatus.currentVisumeCount || 0)}/${membershipStatus.allowedVisumes} Visume${membershipStatus.allowedVisumes !== 1 ? 's' : ''}` :
                          '0/1 Visume'
                        }
                      </span>
                      <div className="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-1 ml-2">
                        <div
                          className={`h-1 rounded-full transition-all duration-500 ${
                            membershipStatus && !membershipStatus.canCreateVisume
                              ? 'bg-gradient-to-r from-red-500 to-red-600'
                              : 'bg-gradient-to-r from-blue-500 to-purple-600'
                          }`}
                          style={{
                            width: membershipStatus ?
                              `${Math.min(((membershipStatus.visumesUsed !== undefined ? membershipStatus.visumesUsed : (membershipStatus.currentVisumeCount || 0)) / membershipStatus.allowedVisumes) * 100, 100)}%` :
                              "0%"
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>


              {/* Right: Action Button */}
              <button
                onClick={membershipStatus && !membershipStatus.canCreateVisume ? toggleUpgradeModal : togglePopupVR}
                className="inline-flex items-center gap-2 bg-gray-900 dark:bg-white hover:bg-gray-800 dark:hover:bg-gray-100 text-white dark:text-gray-900 px-4 py-2 rounded-lg font-medium transition-all duration-200"
              >
                <Video className="h-4 w-4" />
                Create Visume
              </button>
              </div>
            </div>
            
            {/* Stats Cards */}
            <div className="px-5 pb-5">
              <div className="grid grid-cols-4 gap-4">
                {userStats.map(({ count, label, Icon }) => (
                  <div
                    key={label}
                    className="bg-blue-50/50 dark:bg-gray-700/50 rounded-lg p-3 border border-blue-100 dark:border-gray-600"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                        <Icon className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                      </div>
                    </div>
                    <div className="text-xl font-bold text-gray-900 dark:text-white">
                      {count}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {label}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Main Content Grid */}
          <div className="grid grid-cols-1 gap-5 lg:grid-cols-2">
            {/* Suggested Jobs */}
            <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-100 dark:border-gray-700">
              <div className="border-b border-gray-100 dark:border-gray-700 p-5">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-purple-50 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                      <HiOutlineSparkles className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                    </div>
                    <div>
                      <h2 className="text-base font-semibold text-gray-900 dark:text-white">
                        Suggested Jobs
                      </h2>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        AI-curated opportunities
                      </p>
                    </div>
                  </div>
                  <button className="text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200">
                    See All
                  </button>
                </div>
              </div>
              <div className="p-4">
                {jobData.length > 0 ? (
                  <div className="max-h-80 space-y-2 overflow-y-auto">
                    {jobData.slice(0, 4).map((job) => (
                      <JobCard key={job.id} iconUrl={job.image} job={job} />
                    ))}
                  </div>
                ) : (
                  <div className="py-8 text-center">
                    <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-purple-50 dark:bg-purple-900/20">
                      <HiOutlineSparkles className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                    </div>
                    <h3 className="mb-2 text-sm font-medium text-gray-900 dark:text-white">
                      Finding perfect matches
                    </h3>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      AI is analyzing your profile
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Your Visumes */}
            <div className="bg-white dark:bg-gray-800 border border-gray-100 dark:border-gray-700 rounded-xl">
              <div className="p-5 border-b border-gray-100 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-blue-50 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                      <HiVideoCamera className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <h2 className="text-base font-semibold text-gray-900 dark:text-white">
                        Your Visumes
                      </h2>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {videoProfiles.length} video resumes
                      </p>
                    </div>
                  </div>

                  <button className="text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200">
                    See All
                  </button>
                </div>
              </div>
              <div className="p-4">
                {isLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <SmLoader text={"Loading..."} />
                  </div>
                ) : videoProfiles.length === 0 ? (
                  <div className="py-8 text-center">
                    <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-50 dark:bg-blue-900/20">
                      <HiVideoCamera className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <h3 className="mb-2 text-sm font-medium text-gray-900 dark:text-white">
                      Ready to stand out?
                    </h3>
                    <p className="mb-4 text-xs text-gray-500 dark:text-gray-400">
                      Create your first video resume
                    </p>
                    <button
                      onClick={membershipStatus && !membershipStatus.canCreateVisume ? toggleUpgradeModal : togglePopupVR}
                      className="inline-flex items-center gap-2 bg-gray-900 dark:bg-white hover:bg-gray-800 dark:hover:bg-gray-100 text-white dark:text-gray-900 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200"
                    >
                      <Video className="h-4 w-4" />
                      Create First Visume
                    </button>
                  </div>
                ) : (
                  <div className="max-h-80 space-y-2 overflow-y-auto">
                    {videoProfiles.slice(0, 4).map((profile) => (
                      <VideoProfileCard
                        key={profile.vpid}
                        profile={profile}
                        toggleVideoProfilePopup={toggleVideoProfilePopup}
                      />
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      ) : (
        <h2>Please SignIn</h2>
      )}

      {/* Start Popup for Video Resume */}
      {createVRpopup && (
        <>
          {console.log("Rendering CreateVR modal. createVRpopup:", createVRpopup, "inviteData:", inviteData)}
          <CreateVR key="create-vr" togglePopupVR={togglePopupVR} inviteData={inviteData} />
        </>
      )}
      {/* End Popup for Video Resume */}

      {/* Video profile popup */}
      {showVideoProfilePopup && (
        <VidProfPopup
          key="vid-prof"
          toggleVideoProfilePopup={toggleVideoProfilePopup}
        />
      )}

      {/* Upgrade Modal */}
      <UpgradeModal 
        isOpen={showUpgradeModal} 
        onClose={toggleUpgradeModal} 
        membershipStatus={membershipStatus || {}}
      />
    </>
  );
}
