"use client"
import { useState } from "react"
import Navbar from "./components/Navbar"
import Hero from "./components/Hero"
import EmpHero from "./components/EmpHero"
import ProblemStatementJobSeeker from "./components/ProblemStatementJobSeeker"
import ProblemStatementHiringManager from "./components/ProblemStatementHiringManager"
import HowItWorksJobSeeker from "./components/HowItWorksJobSeeker"
import HowItWorksHiringManager from "./components/HowItWorksHiringManager"
import FeaturesJobSeeker from "./components/FeaturesJobSeeker"
import FeaturesHiringManager from "./components/FeaturesHiringManager"
import MovingTagsJobSeeker from "./components/MovingTagsJobSeeker"
import MovingTagsHiringManager from "./components/MovingTagsHiringManager"
import FAQJobSeeker from "./components/FAQJobSeeker"
import FAQHiringManager from "./components/FAQHiringManager"
import Contact from "./components/Contact"
import Footer from "./components/Footer"
import HiringBentoGrid from "./components/HiringBentoGrid"

const WaitList = () => {
  const [activeView, setActiveView] = useState("jobseeker")

  return (
    <div className="min-h-screen">
      <Navbar activeView={activeView} setActiveView={setActiveView} />

      {activeView === "jobseeker" ? (
        <>
          <Hero activeView={activeView} setActiveView={setActiveView} />
          <ProblemStatementJobSeeker />
          <HowItWorksJobSeeker />
          <FeaturesJobSeeker />
          <MovingTagsJobSeeker />
          <FAQJobSeeker />
        </>
      ) : activeView === "hiringmanager" ? (
        <>
          <EmpHero activeView={activeView} setActiveView={setActiveView} />
          <ProblemStatementHiringManager />
          <HowItWorksHiringManager />
          <FeaturesHiringManager />
          <HiringBentoGrid />
          <MovingTagsHiringManager />
          <FAQHiringManager />
        </>
      ) : (
        // Fallback to job seeker if activeView is unexpected
        <>
          <Hero activeView={activeView} setActiveView={setActiveView} />
          <ProblemStatementJobSeeker />
          <HowItWorksJobSeeker />
          <FeaturesJobSeeker />
          <MovingTagsJobSeeker />
          <FAQJobSeeker />
        </>
      )}

      <Contact />
      <Footer />
    </div>
  )
}

export default WaitList