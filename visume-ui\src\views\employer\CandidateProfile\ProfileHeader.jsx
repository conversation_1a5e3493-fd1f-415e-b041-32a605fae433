// ProfileHeader.jsx
import React, { useState, useEffect } from "react";
import {
  MapPin,
  IndianRupee,
  Star,
  MessageCircle,
  BarChart2,
} from "lucide-react";
import Progress from "components/progress";
import toast from "react-hot-toast";
import { Play, Pause, SkipBack, SkipForward, Volume2, Maximize, X } from "lucide-react";

function CustomVideoPlayer({ videoRef, src, questionsAndAnswers, onQuestionClick, videoPlaying, setVideoPlaying }) {
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isExpanded, setIsExpanded] = useState(false);
  const [currentQuestion, setCurrentQuestion] = useState(null);
  const [currentAnswer, setCurrentAnswer] = useState(null);
  const [showSubtitle, setShowSubtitle] = useState(false);

  const handleTimeUpdate = () => {
    if (videoRef.current) {
      const time = videoRef.current.currentTime;
      setCurrentTime(time);
      
      // Find current question based on timestamp
      if (questionsAndAnswers && questionsAndAnswers.length > 0) {
        let foundQuestion = null;
        let foundAnswer = null;
        
        for (let i = 0; i < questionsAndAnswers.length; i++) {
          const qa = questionsAndAnswers[i];
          const questionTime = getQuestionTime(qa);
          const nextQuestionTime = i < questionsAndAnswers.length - 1 
            ? getQuestionTime(questionsAndAnswers[i + 1]) 
            : duration;
          
          if (time >= questionTime - 1 && time < nextQuestionTime) { // Show question 1 second earlier
            foundQuestion = qa.question;
            foundAnswer = qa.answer;
            // Show subtitle during answer (assuming question lasts ~3 seconds, then answer)
            setShowSubtitle(time >= questionTime + 3 && time < nextQuestionTime);
            break;
          }
        }
        
        setCurrentQuestion(foundQuestion);
        setCurrentAnswer(foundAnswer);
      }
    }
  };

  const handleLoadedMetadata = () => {
    if (videoRef.current) {
      setDuration(videoRef.current.duration);
    }
  };

  const handleProgressClick = (e) => {
    if (videoRef.current) {
      const rect = e.currentTarget.getBoundingClientRect();
      const percent = (e.clientX - rect.left) / rect.width;
      const newTime = percent * duration;
      videoRef.current.currentTime = newTime;
    }
  };

  const togglePlayPause = () => {
    if (videoRef.current) {
      if (videoPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
    }
  };

  const handleVolumeChange = (e) => {
    const newVolume = parseFloat(e.target.value);
    setVolume(newVolume);
    if (videoRef.current) {
      videoRef.current.volume = newVolume;
    }
  };

  const formatTime = (time) => {
    if (!time) return "00:00";
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  // Handle escape key to close expanded view
  React.useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape' && isExpanded) {
        setIsExpanded(false);
      }
    };
    
    if (isExpanded) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
    
    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = '';
    };
  }, [isExpanded]);

  const getQuestionTime = (qa) => {
    if (!qa.startTimestamp || !questionsAndAnswers.length) return 0;
    
    if (typeof qa.startTimestamp === "number") {
      return qa.startTimestamp;
    }
    
    const base = new Date(questionsAndAnswers[0].startTimestamp);
    const target = new Date(qa.startTimestamp);
    if (!isNaN(base.getTime()) && !isNaN(target.getTime())) {
      return Math.max(0, (target.getTime() - base.getTime()) / 1000);
    }
    return 0;
  };

  // Navigate to previous question
  const goToPreviousQuestion = () => {
    if (!questionsAndAnswers || questionsAndAnswers.length === 0) return;
    
    const currentTime = videoRef.current?.currentTime || 0;
    let previousQuestion = null;
    
    // Find the previous question
    for (let i = questionsAndAnswers.length - 1; i >= 0; i--) {
      const questionTime = getQuestionTime(questionsAndAnswers[i]);
      if (questionTime < currentTime - 2) { // 2 second buffer
        previousQuestion = questionsAndAnswers[i];
        break;
      }
    }
    
    if (previousQuestion && videoRef.current) {
      videoRef.current.currentTime = getQuestionTime(previousQuestion);
    } else if (videoRef.current) {
      // If no previous question, go to beginning
      videoRef.current.currentTime = 0;
    }
  };

  // Navigate to next question
  const goToNextQuestion = () => {
    if (!questionsAndAnswers || questionsAndAnswers.length === 0) return;
    
    const currentTime = videoRef.current?.currentTime || 0;
    let nextQuestion = null;
    
    // Find the next question
    for (let i = 0; i < questionsAndAnswers.length; i++) {
      const questionTime = getQuestionTime(questionsAndAnswers[i]);
      if (questionTime > currentTime + 2) { // 2 second buffer
        nextQuestion = questionsAndAnswers[i];
        break;
      }
    }
    
    if (nextQuestion && videoRef.current) {
      videoRef.current.currentTime = getQuestionTime(nextQuestion);
    }
  };

  const videoPlayerContent = (
    <>
      <video
        ref={videoRef}
        src={src}
        poster="/src/assets/img/videores-illustration.png"
        onTimeUpdate={handleTimeUpdate}
        onLoadedMetadata={handleLoadedMetadata}
        onPlay={() => setVideoPlaying(true)}
        onPause={() => setVideoPlaying(false)}
        onClick={togglePlayPause}
      />
      
      {/* Current Question Overlay */}
      {currentQuestion && (
        <div className={`current-question-overlay ${videoPlaying ? 'visible' : ''}`}>
          <div style={{ fontWeight: '600', marginBottom: '4px', fontSize: '12px', opacity: 0.8 }}>
            Current Question:
          </div>
          <div>{currentQuestion}</div>
        </div>
      )}
      
      {/* Subtitle Overlay */}
      {showSubtitle && currentAnswer && (
        <div className={`subtitle-overlay ${showSubtitle ? 'visible' : ''}`}>
          {currentAnswer.length > 150 ? currentAnswer.substring(0, 150) + '...' : currentAnswer}
        </div>
      )}
      
      {/* Custom Controls */}
      <div className="video-controls">
        <div className="progress-bar" onClick={handleProgressClick}>
          <div 
            className="progress-fill" 
            style={{ width: `${duration ? (currentTime / duration) * 100 : 0}%` }}
          />
        </div>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <button 
              className={`control-button ${!videoPlaying ? 'paused-state' : ''}`} 
              onClick={togglePlayPause}
              title={videoPlaying ? "Pause" : "Play"}
            >
              {videoPlaying ? <Pause size={16} /> : <Play size={16} />}
            </button>
            <button 
              className="control-button"
              onClick={goToPreviousQuestion}
              title="Previous Question"
            >
              <SkipBack size={16} />
            </button>
            <button 
              className="control-button"
              onClick={goToNextQuestion}
              title="Next Question"
            >
              <SkipForward size={16} />
            </button>
            <button 
              className="expand-button"
              onClick={() => setIsExpanded(true)}
              title="Expand video"
            >
              <Maximize size={16} />
            </button>
            <div style={{ display: 'flex', alignItems: 'center', gap: '4px', marginLeft: '8px' }}>
              <Volume2 size={14} color="white" />
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={volume}
                onChange={handleVolumeChange}
                style={{
                  width: '60px',
                  height: '4px',
                  background: 'rgba(255,255,255,0.3)',
                  borderRadius: '2px'
                }}
              />
            </div>
          </div>
          <div style={{ color: 'white', fontSize: '12px', fontFamily: 'monospace' }}>
            {formatTime(currentTime)} / {formatTime(duration)}
          </div>
        </div>
      </div>
    </>
  );

  if (isExpanded) {
    return (
      <div className="expanded-video-player" onClick={(e) => e.target === e.currentTarget && setIsExpanded(false)}>
        <button 
          className="close-button"
          onClick={() => setIsExpanded(false)}
          title="Close expanded view"
        >
          <X size={20} />
        </button>
        <div className="custom-video-player" style={{ border: 'none', borderRadius: '8px', maxWidth: '90vw', maxHeight: '80vh' }}>
          {videoPlayerContent}
        </div>
      </div>
    );
  }

  return (
    <div className="custom-video-player mt-2">
      {videoPlayerContent}
    </div>
  );
}

function ScoreCard({ icon, title, score, color }) {
  const getColorClasses = (color) => {
    switch (color) {
      case "yellow":
        return {
          bg: "bg-amber-50 dark:bg-amber-900/20",
          icon: "text-amber-600 dark:text-amber-400",
          border: "border-amber-200 dark:border-amber-800",
          text: "text-amber-700 dark:text-amber-300",
          progress: "from-amber-400 to-amber-600"
        };
      case "blue":
        return {
          bg: "bg-blue-50 dark:bg-blue-900/20",
          icon: "text-blue-600 dark:text-blue-400",
          border: "border-blue-200 dark:border-blue-800",
          text: "text-blue-700 dark:text-blue-300",
          progress: "from-blue-400 to-blue-600"
        };
      case "green":
        return {
          bg: "bg-emerald-50 dark:bg-emerald-900/20",
          icon: "text-emerald-600 dark:text-emerald-400",
          border: "border-emerald-200 dark:border-emerald-800",
          text: "text-emerald-700 dark:text-emerald-300",
          progress: "from-emerald-400 to-emerald-600"
        };
      default:
        return {
          bg: "bg-gray-50 dark:bg-gray-900/20",
          icon: "text-gray-600 dark:text-gray-400",
          border: "border-gray-200 dark:border-gray-800",
          text: "text-gray-700 dark:text-gray-300",
          progress: "from-gray-400 to-gray-600"
        };
    }
  };

  const colors = getColorClasses(color);
  
  return (
    <div className={`min-w-[80px] max-w-[100px] rounded-lg border ${colors.border} ${colors.bg} p-2 shadow-sm`}>
      <div className="flex items-center justify-between mb-1.5">
        <div className={`w-5 h-5 rounded ${colors.bg} flex items-center justify-center`}>
          {React.cloneElement(icon, { className: `h-3 w-3 ${colors.icon}` })}
        </div>
        <div className="text-right">
          <div className="text-base font-bold text-gray-900 dark:text-white">
            {Math.round(score)}/10
          </div>
        </div>
      </div>
      
      <div className="space-y-1">
        <span className="text-xs font-medium text-gray-700 dark:text-gray-300 block">
          {title}
        </span>
        
        {/* Progress Bar */}
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-0.5 overflow-hidden">
          <div 
            className={`h-0.5 bg-gradient-to-r ${colors.progress} rounded-full transition-all duration-500 ease-out`}
            style={{ width: `${Math.min(score * 10, 100)}%` }}
          />
        </div>
      </div>
    </div>
  );
}

const ProfileHeader = ({
  candidateProf,
  profileData,
  strippedResumeJson,
  questionsAndAnswers, // Added prop
  videoRef, // NEW: video ref for controlling playback
}) => {
  const [shortlistLoading, setShortlistLoading] = useState(false);
  const [shortlisted, setShortlisted] = useState(false);
  const [videoPlaying, setVideoPlaying] = useState(false);

  // Handler to seek and play video at a given timestamp or ISO string
  const handleQuestionClick = (qa) => {
    let seekTime = 0;
    if (
      qa &&
      qa.startTimestamp &&
      Array.isArray(questionsAndAnswers) &&
      questionsAndAnswers.length > 0 &&
      questionsAndAnswers[0].startTimestamp
    ) {
      if (typeof qa.startTimestamp === "number") {
        seekTime = qa.startTimestamp;
      } else {
        const base = new Date(questionsAndAnswers[0].startTimestamp);
        const target = new Date(qa.startTimestamp);
        if (!isNaN(base.getTime()) && !isNaN(target.getTime())) {
          seekTime = Math.max(0, (target.getTime() - base.getTime()) / 1000);
        }
      }
    }
    if (videoRef.current) {
      videoRef.current.currentTime = seekTime;
      videoRef.current.play();
    }
  };

  // Shortlist handler
  const handleShortlist = async () => {
    if (!candidateProf?.cand_id || !candidateProf?.emp_id) {
      toast.error("Candidate ID or Employer ID not found.");
      return;
    }
    setShortlistLoading(true);
    try {
      const emp_id = candidateProf.emp_id;
      const cand_id = candidateProf.video_profile_id;
      console.log(
        "Shortlist API call - emp_id:",
        emp_id,
        "cand_id (video_profile_id):",
        cand_id
      );
      const response = await fetch(
        `${
          import.meta.env.VITE_APP_HOST
        }/api/v1/employer-profiles/shortlist-candidate`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ emp_id, cand_id }),
        }
      );
      const data = await response.json();
      if (!response.ok) {
        toast.error(data?.message || "Failed to shortlist candidate");
        return;
      }
      setShortlisted(true);
      toast.success(data?.message || "Candidate shortlisted!");
    } catch (error) {
      toast.error(error.message || "Failed to shortlist candidate");
    } finally {
      setShortlistLoading(false);
    }
  };

  return (
    <div className="max-w-full space-y-2 p-0">
      <div className="flex flex-col gap-2 rounded-xl bg-white p-3 shadow-sm">
        <style>
          {`
            .custom-video-player {
              position: relative;
              border: 2px solid #3B82F6;
              border-radius: 12px;
              overflow: hidden;
              background: linear-gradient(135deg, #EBF8FF 0%, #DBEAFE 100%);
            }
            .custom-video-player video {
              width: 100%;
              height: 400px;
              object-fit: contain;
              background: #000;
            }
            .expanded-video-player {
              position: fixed;
              top: 0;
              left: 0;
              width: 100vw;
              height: 100vh;
              z-index: 9999;
              background: rgba(0, 0, 0, 0.95);
              display: flex;
              align-items: center;
              justify-content: center;
            }
            .expanded-video-player video {
              width: 90vw;
              height: 80vh;
              object-fit: contain;
            }
            .close-button {
              position: absolute;
              top: 20px;
              right: 20px;
              background: rgba(59, 130, 246, 0.9);
              border: none;
              border-radius: 50%;
              width: 40px;
              height: 40px;
              display: flex;
              align-items: center;
              justify-content: center;
              cursor: pointer;
              color: white;
              transition: all 0.2s ease;
              z-index: 10000;
            }
            .close-button:hover {
              background: #3B82F6;
              transform: scale(1.1);
            }
            .video-controls {
              position: absolute;
              bottom: 0;
              left: 0;
              right: 0;
              background: linear-gradient(transparent, rgba(59, 130, 246, 0.9));
              padding: 16px;
              transform: translateY(100%);
              transition: transform 0.3s ease;
            }
            .custom-video-player:hover .video-controls {
              transform: translateY(0);
            }
            .progress-bar {
              width: 100%;
              height: 4px;
              background: rgba(255, 255, 255, 0.3);
              border-radius: 2px;
              overflow: hidden;
              margin-bottom: 12px;
            }
            .progress-fill {
              height: 100%;
              background: linear-gradient(90deg, #60A5FA, #3B82F6);
              transition: width 0.1s ease;
            }
            .control-button {
              background: rgba(255, 255, 255, 0.9);
              border: none;
              border-radius: 6px;
              padding: 8px;
              margin-right: 8px;
              cursor: pointer;
              transition: all 0.2s ease;
              color: #3B82F6;
            }
            .control-button:hover {
              background: white;
              transform: scale(1.05);
            }
            .control-button.paused-state {
              background: rgba(239, 68, 68, 0.9);
              color: white;
              box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.3);
              animation: pulse-pause 2s infinite;
            }
            .control-button.paused-state:hover {
              background: rgb(239, 68, 68);
              color: white;
            }
            @keyframes pulse-pause {
              0%, 100% { box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.3); }
              50% { box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.5); }
            }
            .current-question-overlay {
              position: absolute;
              top: 20px;
              left: 20px;
              right: 20px;
              background: rgba(59, 130, 246, 0.95);
              backdrop-filter: blur(8px);
              border-radius: 12px;
              padding: 12px 16px;
              color: white;
              font-size: 14px;
              line-height: 1.4;
              opacity: 0;
              transform: translateY(-20px);
              transition: all 0.3s ease;
              pointer-events: none;
              z-index: 10;
            }
            .current-question-overlay.visible {
              opacity: 1;
              transform: translateY(0);
            }
            .subtitle-overlay {
              position: absolute;
              bottom: 120px;
              left: 20px;
              right: 20px;
              background: rgba(0, 0, 0, 0.8);
              color: white;
              padding: 8px 12px;
              border-radius: 8px;
              font-size: 14px;
              line-height: 1.3;
              text-align: center;
              opacity: 0;
              transform: translateY(20px);
              transition: all 0.3s ease;
              z-index: 10;
            }
            .subtitle-overlay.visible {
              opacity: 1;
              transform: translateY(0);
            }
            .expand-button {
              background: rgba(255, 255, 255, 0.9);
              border: none;
              border-radius: 6px;
              padding: 8px;
              cursor: pointer;
              transition: all 0.2s ease;
              color: #3B82F6;
            }
            .expand-button:hover {
              background: white;
              transform: scale(1.05);
            }
          `}
        </style>
        <div className="flex flex-col items-center gap-1.5 md:flex-row md:items-center md:justify-start">
          <div className="flex h-12 w-12 flex-shrink-0 items-center justify-center overflow-hidden rounded-full border-2 border-brand-200 bg-white shadow-sm">
            {candidateProf.profile_picture &&
            candidateProf.profile_picture !== "" ? (
              <img
                src={
                  candidateProf.profile_picture.startsWith("http")
                    ? candidateProf.profile_picture
                    : `${import.meta.env.VITE_APP_HOST}/${
                        candidateProf.profile_picture
                      }`
                }
                alt={candidateProf.cand_name}
                className="h-full w-full object-cover object-center"
                onError={(e) => {
                  e.target.style.display = "none";
                }}
              />
            ) : (
              <svg
                className="h-10 w-10 text-brand-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"
                />
              </svg>
            )}
          </div>
          <div className="flex flex-col items-center md:items-start">
            <span className="text-base font-bold text-gray-900">
              {candidateProf.cand_name}
            </span>
            <span className="flex items-center gap-1.5 text-sm font-semibold text-brand-600">
              <BarChart2 className="h-3.5 w-3.5 text-brand-600" />
              {profileData.role}
            </span>
          </div>
        </div>
        {profileData.video_url ? (
          <CustomVideoPlayer
            videoRef={videoRef}
            src={profileData.video_url}
            questionsAndAnswers={questionsAndAnswers}
            onQuestionClick={handleQuestionClick}
            videoPlaying={videoPlaying}
            setVideoPlaying={setVideoPlaying}
          />
        ) : (
          <div className="mt-2 flex h-[400px] w-full items-center justify-center rounded-xl border-2 border-blue-300 bg-gradient-to-br from-blue-100 via-blue-50 to-blue-100 text-blue-600 shadow-lg">
            No Video URL Found
          </div>
        )}
        {/* Header with Scores */}
        <div className="mt-4 space-y-2">
          {/* Header Row */}
          <div>
            <h3 className="flex items-center gap-2 text-lg font-semibold text-gray-900 dark:text-white">
              <Star className="h-5 w-5 text-amber-500" />
              Assessment Scores
            </h3>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">
              AI-powered candidate evaluation metrics
            </p>
          </div>

          {/* Score Cards and Shortlist Button Row */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {(() => {
                let scoreObj = {};
                try {
                  scoreObj = JSON.parse(profileData.score)?.score || {};
                } catch (e) {}
                return (
                  <>
                    <ScoreCard
                      icon={<Star />}
                      title="Skills"
                      score={scoreObj.Skill_Score ?? scoreObj.Skill ?? 0}
                      color="yellow"
                    />
                    <ScoreCard
                      icon={<MessageCircle />}
                      title="Communication"
                      score={
                        scoreObj.Communication_Score ??
                        scoreObj.Communication ??
                        0
                      }
                      color="blue"
                    />
                    <ScoreCard
                      icon={<BarChart2 />}
                      title="Overall"
                      score={
                        scoreObj.Overall_Score ??
                        scoreObj.Overall ??
                        0
                      }
                      color="green"
                    />
                  </>
                );
              })()}
            </div>
            
            {/* Right-aligned Shortlist Button */}
            <button
              className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium text-white shadow-sm transition-all duration-200 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                shortlisted 
                  ? "bg-green-600 hover:bg-green-700 focus:ring-green-500 cursor-default" 
                  : shortlistLoading
                  ? "bg-blue-400 cursor-not-allowed"
                  : "bg-blue-600 hover:bg-blue-700 focus:ring-blue-500"
              }`}
              onClick={handleShortlist}
              disabled={shortlistLoading || shortlisted}
            >
              {shortlisted ? (
                <>
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Shortlisted
                </>
              ) : shortlistLoading ? (
                <>
                  <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"/>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"/>
                  </svg>
                  Shortlisting...
                </>
              ) : (
                <>
                  <Star className="w-4 h-4" />
                  Shortlist
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export { ScoreCard };
export default ProfileHeader;