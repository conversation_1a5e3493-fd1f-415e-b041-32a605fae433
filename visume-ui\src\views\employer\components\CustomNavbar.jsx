import React from "react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import { Ri<PERSON>oon<PERSON>ill, RiSunFill } from "react-icons/ri";
import { Menu, X } from "lucide-react";
import avatar from "assets/img/avatars/avatar4.png";
import Cookies from "js-cookie";
import { useNavigate } from "react-router-dom";
import Dropdown from "../../../components/dropdown";

const CustomNavbar = ({ links, profile_picture }) => {
  const navigate = useNavigate();
  const [darkmode, setDarkmode] = React.useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = React.useState(false);
  const [empData, setEmpData] = React.useState(null);

  // Fetch employer data to get company logo
  React.useEffect(() => {
    const fetchEmployerData = async () => {
      const empId = Cookies.get("employerId");
      if (!empId) return;

      try {
        const response = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/employerProfilesData`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              Authorization: empId,
            },
          }
        );
        if (response.ok) {
          const data = await response.json();
          setEmpData(data.data);
        }
      } catch (error) {
        console.error("Error fetching employer data:", error);
      }
    };

    fetchEmployerData();
  }, []);

  // Listen for company logo updates
  React.useEffect(() => {
    const handleLogoUpdate = (event) => {
      // Refresh employer data when logo is updated
      const fetchEmployerData = async () => {
        const empId = Cookies.get("employerId");
        if (!empId) return;

        try {
          const response = await fetch(
            `${import.meta.env.VITE_APP_HOST}/api/v1/employerProfilesData`,
            {
              method: "GET",
              headers: {
                "Content-Type": "application/json",
                Authorization: empId,
              },
            }
          );
          if (response.ok) {
            const data = await response.json();
            setEmpData(data.data);
          }
        } catch (error) {
          console.error("Error fetching updated employer data:", error);
        }
      };

      fetchEmployerData();
    };

    window.addEventListener('companyLogoUpdated', handleLogoUpdate);

    return () => {
      window.removeEventListener('companyLogoUpdated', handleLogoUpdate);
    };
  }, []);

  // Determine avatar URL with priority: company_logo > profile_picture prop > profile_picture from API > default avatar
  const avatarUrl = empData?.company_logo
    ? (empData.company_logo.startsWith("http")
        ? empData.company_logo
        : `${import.meta.env.VITE_APP_HOST}/${empData.company_logo}`)
    : profile_picture
    ? `${import.meta.env.VITE_APP_HOST}/${profile_picture}`
    : empData?.profile_picture
    ? (empData.profile_picture.startsWith("http")
        ? empData.profile_picture
        : `${import.meta.env.VITE_APP_HOST}/${empData.profile_picture}`)
    : avatar;

  const deletecookies = () => {
    // Get current role before removing cookies
    const currentRole = Cookies.get('role');
    
    Cookies.remove('jstoken');
    Cookies.remove('role');
    Cookies.remove('candId');
    Cookies.remove('formData');
    Cookies.remove('questions');
    Cookies.remove('videoProfileId');
    Cookies.remove('skills');
    Cookies.remove('jobRole');
    
    // Redirect based on current role
    if (currentRole === "employer") {
      navigate('/employer/sign-in');
    } else {
      navigate('/candidate/sign-in');
    }
  };

  return (
    <>
      {/* Google Fonts Import */}
      <link href="https://fonts.googleapis.com/css2?family=Sora:wght@600&family=Manrope:wght@400;500;600;700&display=swap" rel="stylesheet" />

      <div className="fixed top-6 left-0 right-0 z-50 flex justify-center px-4">
        <motion.nav
          initial={{ y: -100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          className="relative"
        >
          <div className="bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm rounded-full shadow-sm border border-blue-200/60 dark:border-gray-700/60 pl-6 pr-1 py-1">
            <div className="flex items-center gap-8">
              {/* Logo */}
              <motion.div
                className="flex items-center space-x-2"
                whileHover={{ scale: 1.02 }}
                onClick={() => navigate('/employer')}
                style={{ cursor: 'pointer' }}
              >
                <img src="/visume-logo-new.png" alt="Visume Logo" className="h-6 w-6 object-contain" />
                <span className="font-semibold text-lg text-gray-900 dark:text-white" style={{ fontFamily: "Sora, sans-serif" }}>
                  Visume
                </span>
              </motion.div>

              {/* Navigation Links (desktop) */}
              <div className="hidden md:flex items-center space-x-8">
                {links.map((link, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 + 0.2 }}
                  >
                    <Link
                      to={link.url}
                      className={`text-sm font-semibold text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors duration-200 flex items-center gap-2 ${link.className || ''}`}
                      style={{ fontFamily: "Sora, sans-serif" }}
                    >
                      {link.icon && <span>{link.icon}</span>}
                      {link.text}
                    </Link>
                  </motion.div>
                ))}
              </div>

              {/* Right side - Controls (desktop) */}
<div className="hidden md:flex items-center space-x-4">
  {/* Dark Mode Toggle */}
  <motion.button
    className="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors duration-200 p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
    onClick={() => {
      document.body.classList.toggle("dark");
      setDarkmode(!darkmode);
    }}
    whileHover={{ scale: 1.05 }}
    whileTap={{ scale: 0.95 }}
  >
    {darkmode ? (
      <RiSunFill className="h-5 w-5" />
    ) : (
      <RiMoonFill className="h-5 w-5" />
    )}
  </motion.button>

  {/* Profile Dropdown */}
  <Dropdown
    button={
      <img
        className="h-8 w-8 rounded-full object-cover"
        src={avatarUrl}
        alt={empData?.company_logo ? "Company Logo" : "Profile"}
        onError={(e) => {
          if (e.target.src !== avatar) {
            e.target.onerror = null;
            e.target.src = avatar;
          }
        }}
      />
    }
    children={
      <div className="flex w-56 flex-col justify-start rounded-2xl bg-white dark:bg-gray-900 border border-blue-200/60 dark:border-gray-700/60 shadow-xl shadow-blue-200/25 dark:shadow-gray-900/25">
        <div className="p-4">
          <div className="flex items-center gap-2">
            <p className="text-sm font-bold text-slate-800 dark:text-white" style={{ fontFamily: "Manrope, sans-serif" }}>
              👋 Hey, {Cookies.get('role')}
            </p>
          </div>
        </div>
        <div className="h-px w-full bg-blue-200/60 dark:bg-gray-700/60" />
        <div className="flex flex-col p-4">
          <a
            href=" "
            className="text-sm text-slate-600 dark:text-gray-300 hover:text-slate-800 dark:hover:text-white transition-colors duration-200"
            style={{ fontFamily: "Sora, sans-serif" }}
          >
            Profile Settings
          </a>
          <a
            href=" "
            onClick={deletecookies}
            className="mt-3 text-sm font-medium text-red-500 dark:text-red-400 hover:text-red-600 dark:hover:text-red-300 transition-colors duration-200"
            style={{ fontFamily: "Sora, sans-serif" }}
          >
            Log Out
          </a>
        </div>
      </div>
    }
    classNames={"py-2 top-8 -left-[180px] w-max"}
  />
</div>

{/* Mobile Menu Button */}
<button className="md:hidden p-2 text-gray-600 dark:text-gray-300" onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}>
  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={isMobileMenuOpen ? "M6 18L18 6M6 6l12 12" : "M4 6h16M4 12h16M4 18h16"} />
  </svg>
</button>

      </div>

      {/* Navigation Links and Controls */}
      <div className={`${isMobileMenuOpen ? 'flex' : 'hidden'} sm:flex flex-col sm:flex-row items-center w-full sm:w-auto mt-4 sm:mt-0 space-y-4 sm:space-y-0 sm:space-x-6`}>
        {/* Links */}
        

        {/* Controls */}
        <div className="flex items-center space-x-4">
          {/* Dark Mode Toggle */}
          {/* <button
            className="text-gray-600 dark:text-white"
            onClick={() => {
              document.body.classList.toggle("dark");
              setDarkmode(!darkmode);
            }}
          >
            {darkmode ? (
              <RiSunFill className="h-5 w-5" />
            ) : (
              <RiMoonFill className="h-5 w-5" />
            )}
          </button> */}

          </div>

              {/* Mobile menu button */}
              <div className="md:hidden">
                <button
                  aria-label={isMobileMenuOpen ? "Close menu" : "Open menu"}
                  className="p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
                  onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                >
                  {isMobileMenuOpen ? <X className="h-4 w-4 text-gray-900 dark:text-white" /> : <Menu className="h-4 w-4 text-gray-900 dark:text-white" />}
                </button>
              </div>
            </div>

            {/* Mobile menu */}
            {isMobileMenuOpen && (
              <motion.div
                initial={{ opacity: 0, y: -10, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: -10, scale: 0.95 }}
                transition={{ duration: 0.2 }}
                className="absolute top-full left-0 right-0 mt-2 bg-white/95 dark:bg-gray-900/95 backdrop-blur-md rounded-2xl shadow-lg border border-blue-200/80 dark:border-gray-700/80 p-4 md:hidden"
              >
                <div className="flex flex-col space-y-3">
                  {links.map((link, index) => (
                    <Link
                      key={index}
                      to={link.url}
                      className={`text-sm font-semibold text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white py-2 px-2 rounded-lg hover:bg-gray-100/80 dark:hover:bg-gray-700/80 transition-colors duration-200 flex items-center gap-2 ${link.className || ''}`}
                      style={{ fontFamily: "Sora, sans-serif" }}
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      {link.icon && <span>{link.icon}</span>}
                      {link.text}
                    </Link>
                  ))}
                  
                  <div className="flex items-center justify-between pt-2 border-t border-blue-200/60 dark:border-gray-700/60">
                    {/* Mobile Dark Mode Toggle */}
                    <button
                      className="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors duration-200 p-2 rounded-lg hover:bg-gray-100/80 dark:hover:bg-gray-700/80"
                      onClick={() => {
                        document.body.classList.toggle("dark");
                        setDarkmode(!darkmode);
                      }}
                    >
                      {darkmode ? (
                        <RiSunFill className="h-5 w-5" />
                      ) : (
                        <RiMoonFill className="h-5 w-5" />
                      )}
                    </button>

                    {/* Mobile Profile */}
                    <div className="flex items-center gap-2">
                      <img
                        className="h-8 w-8 rounded-full border-2 border-blue-200/60"
                        src={avatarUrl}
                        alt="Profile"
                        onError={(e) => {
                          if (e.target.src !== avatar) {
                            e.target.onerror = null;
                            e.target.src = avatar;
                          }
                        }}
                      />
                      <button
                        onClick={deletecookies}
                        className="text-sm font-medium text-red-500 dark:text-red-400 hover:text-red-600 dark:hover:text-red-300 transition-colors duration-200"
                        style={{ fontFamily: "Sora, sans-serif" }}
                      >
                        Log Out
                      </button>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
          </div>
        </motion.nav>
      </div>
    </>
  );
};

export default CustomNavbar;