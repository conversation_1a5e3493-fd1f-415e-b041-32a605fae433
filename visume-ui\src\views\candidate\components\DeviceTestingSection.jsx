import React, { useEffect, useState } from "react";
import VideoPreview from "../smaller_comp/VideoPreview";
import StatusIndicator from "../smaller_comp/StatusIndicator";
import Button from "../smaller_comp/Button";
import { Mic, Camera, Shield, Play, CheckCircle, AlertCircle, ScanFace } from "lucide-react";

const DeviceTestingSection = ({ localCamStream, startWebcam, onStartInterview }) => {
  const [devices, setDevices] = useState({ cameras: [], microphones: [] });
  const [selectedCamera, setSelectedCamera] = useState("");
  const [selectedMicrophone, setSelectedMicrophone] = useState("");
  const [volumeLevel, setVolumeLevel] = useState(0); // Store the current volume level
  const [micColor, setMicColor] = useState("gray"); // Initial color (gray when inactive)
  const [audioStream, setAudioStream] = useState(null); // Track audio stream for cleanup
  const [audioContext, setAudioContext] = useState(null); // Track audio context for cleanup
  const [audioTestPassed, setAudioTestPassed] = useState(false); // Track if audio test is passed
  const [audioTestProgress, setAudioTestProgress] = useState(0); // Progress towards passing test
  const [isTestingAudio, setIsTestingAudio] = useState(true); // Auto-start testing audio

  useEffect(() => {
    // Function to list media devices
    const getMediaDevices = async () => {
      const devices = await navigator.mediaDevices.enumerateDevices();
      const cameras = devices.filter((device) => device.kind === "videoinput");
      const microphones = devices.filter(
        (device) => device.kind === "audioinput"
      );
      setDevices({ cameras, microphones });

      // Set default camera and microphone
      if (cameras.length > 0) setSelectedCamera(cameras[0].deviceId);
      if (microphones.length > 0)
        setSelectedMicrophone(microphones[0].deviceId);
    };

    getMediaDevices();
    startWebcam(); // Automatically starts the webcam when component mounts
  }, []);

  useEffect(() => {
    if (!selectedMicrophone) return;

    const startAudioStream = async () => {
      try {
        // Clean up previous stream if exists
        if (audioStream) {
          audioStream.getTracks().forEach(track => track.stop());
        }
        if (audioContext) {
          audioContext.close();
        }

        // Access the selected microphone
        const stream = await navigator.mediaDevices.getUserMedia({
          audio: { deviceId: selectedMicrophone },
        });

        // Store stream for cleanup
        setAudioStream(stream);

        // Create an audio context and analyser node for volume detection
        const newAudioContext = new (window.AudioContext ||
          window.webkitAudioContext)();
        setAudioContext(newAudioContext);

        const analyser = newAudioContext.createAnalyser();
        const microphoneSource = newAudioContext.createMediaStreamSource(stream);

        microphoneSource.connect(analyser);
        analyser.fftSize = 256; // FFT size to define frequency resolution
        const bufferLength = analyser.frequencyBinCount;
        const dataArray = new Uint8Array(bufferLength);

        const updateVolume = () => {
          // Check if audio context is still valid
          if (newAudioContext.state === 'closed') return;

          analyser.getByteFrequencyData(dataArray);

          let sum = 0;
          for (let i = 0; i < dataArray.length; i++) {
            sum += dataArray[i];
          }

          const average = sum / dataArray.length;
          setVolumeLevel(average);

          // Audio test logic - require decent volume level (25+ decibels) to pass
          const REQUIRED_DECIBEL_LEVEL = 25; // Lowered threshold for better sensitivity
          const PROGRESS_DURATION = 2000; // 2 seconds of consistent audio to pass

          if (isTestingAudio && !audioTestPassed) {
            if (average >= REQUIRED_DECIBEL_LEVEL) {
              setAudioTestProgress(prev => {
                const newProgress = Math.min(prev + (100 / (PROGRESS_DURATION / 100)), 100);
                if (newProgress >= 100) {
                  setAudioTestPassed(true);
                  setIsTestingAudio(false);
                }
                return newProgress;
              });
            } else {
              // Gradually decrease progress if volume drops below required level
              setAudioTestProgress(prev => Math.max(prev - 2, 0));
            }
          }

          // Set the microphone icon color based on volume level
          if (audioTestPassed) {
            setMicColor("green"); // Test passed
          } else if (average < 30) {
            setMicColor("gray"); // Very low volume
          } else if (average < REQUIRED_DECIBEL_LEVEL) {
            setMicColor("yellow"); // Low volume
          } else if (average < 70) {
            setMicColor("orange"); // Good volume
          } else {
            setMicColor("red"); // Very high volume
          }

          // Continue to analyze the audio
          requestAnimationFrame(updateVolume);
        };

        // Start volume monitoring
        updateVolume();
      } catch (error) {
        console.error("Error accessing microphone", error);
      }
    };

    startAudioStream();

    // Cleanup function
    return () => {
      if (audioStream) {
        console.log("Cleaning up device testing audio stream");
        audioStream.getTracks().forEach(track => {
          track.stop();
          console.log("Stopped device testing audio track:", track.kind);
        });
      }
      if (audioContext && audioContext.state !== 'closed') {
        audioContext.close();
      }
    };

  }, [selectedMicrophone]); // Restart when the microphone is selected

  // Cleanup on component unmount
  useEffect(() => {
    return () => {
      console.log("DeviceTestingSection unmounting - cleaning up audio resources");
      if (audioStream) {
        audioStream.getTracks().forEach(track => {
          track.stop();
          console.log("Stopped audio track on unmount:", track.kind);
        });
      }
      if (audioContext && audioContext.state !== 'closed') {
        audioContext.close();
      }
    };
  }, [audioStream, audioContext]);

  const handleCameraChange = (e) => {
    setSelectedCamera(e.target.value);
    startWebcam(e.target.value); // Pass the selected camera ID to start the webcam
  };

  const handleMicrophoneChange = (e) => {
    setSelectedMicrophone(e.target.value);
    // Reset audio test when microphone changes
    setAudioTestPassed(false);
    setAudioTestProgress(0);
    setIsTestingAudio(true); // Auto-restart testing
  };

  const resetAudioTest = () => {
    setAudioTestPassed(false);
    setAudioTestProgress(0);
    setIsTestingAudio(true); // Auto-restart testing
  };

  return (
    <div className="flex w-full h-full overflow-hidden items-center justify-center">
      {/* Left side - Video Preview */}
        <div className="w-full max-w-2xl">
          {/* Positioning note */}
          <div className="mb-3 text-center">
            <p className="text-sm font-medium text-gray-700" style={{ fontFamily: 'Sora, sans-serif' }}>
              Position your face within the scanning area
            </p>
          </div>
          
          <div className="relative">
            <VideoPreview stream={localCamStream} />
            
            {/* Face scanning overlay */}
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
              <ScanFace className="w-40 h-40 text-blue-500/70" strokeWidth={1.2} />
            </div>
          </div>
          
          {/* Quick Tips under video */}
          <div className="mt-3 p-3 bg-blue-50 rounded-lg border border-blue-100 shadow-sm">
            <h4 className="text-sm font-bold text-blue-900 mb-2 flex items-center gap-2" style={{ fontFamily: 'Sora, sans-serif' }}>
              <div className="w-1 h-4 bg-blue-600 rounded-full"></div>
              Quick Tips
            </h4>
            <div className="space-y-1" style={{ fontFamily: 'Manrope, sans-serif' }}>
              <div className="flex items-start gap-2 text-xs text-blue-800">
                <div className="w-1 h-1 bg-blue-600 rounded-full mt-1.5 flex-shrink-0"></div>
                <span>Find a quiet, well-lit space</span>
              </div>
              <div className="flex items-start gap-2 text-xs text-blue-800">
                <div className="w-1 h-1 bg-blue-600 rounded-full mt-1.5 flex-shrink-0"></div>
                <span>Keep your face clearly visible</span>
              </div>
              <div className="flex items-start gap-2 text-xs text-blue-800">
                <div className="w-1 h-1 bg-blue-600 rounded-full mt-1.5 flex-shrink-0"></div>
                <span>Interview will switch to fullscreen</span>
              </div>
            </div>
          </div>
        </div>

      {/* Right side - Controls Panel (Google Meet style) */}
      <div className="p-10 flex flex-col">
        <div className="flex-1">
          {/* Ready to join section */}
          <div className="">
            <h2 className="text-lg font-semibold text-gray-900 mb-4" style={{ fontFamily: 'Sora, sans-serif' }}>
              Ready to start?
            </h2>
            
            <button
              onClick={onStartInterview}
              disabled={!localCamStream || !audioTestPassed}
              className={`w-full mb-4 px-4 py-3 rounded-lg font-semibold transition-all duration-200 ${
                (localCamStream && audioTestPassed)
                  ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-sm'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              }`}
              style={{ fontFamily: 'Sora, sans-serif' }}
            >
              <Play className="w-4 h-4 inline mr-2" />
              Start Interview
            </button>
            {(!localCamStream || !audioTestPassed) && (
              <p className="text-xs text-gray-500 mb-4" style={{ fontFamily: 'Manrope, sans-serif' }}>
                {!localCamStream && !audioTestPassed ? "Camera access and audio test required" :
                 !localCamStream ? "Camera access required" : 
                 "Audio test must pass before starting"}
              </p>
            )}
          </div>

          {/* Device Settings */}
          <div className="mb-6">
            <h3 className="text-sm font-semibold text-gray-900 mb-3" style={{ fontFamily: 'Sora, sans-serif' }}>
              Device Settings
            </h3>
            
            {/* Camera Selection */}
            <div className="mb-3">
              <label htmlFor="camera-select" className="block text-xs font-medium text-gray-700 mb-1" style={{ fontFamily: 'Manrope, sans-serif' }}>
                Camera
              </label>
              <select
                id="camera-select"
                value={selectedCamera}
                onChange={handleCameraChange}
                className="w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                style={{ fontFamily: 'Manrope, sans-serif' }}
              >
                {devices.cameras.map((camera) => (
                  <option key={camera.deviceId} value={camera.deviceId}>
                    {camera.label || `Camera ${camera.deviceId}`}
                  </option>
                ))}
              </select>
            </div>

            {/* Microphone Selection */}
            <div className="mb-3">
              <label htmlFor="microphone-select" className="block text-xs font-medium text-gray-700 mb-1" style={{ fontFamily: 'Manrope, sans-serif' }}>
                Microphone
              </label>
              <select
                id="microphone-select"
                value={selectedMicrophone}
                onChange={handleMicrophoneChange}
                className="w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                style={{ fontFamily: 'Manrope, sans-serif' }}
              >
                {devices.microphones.map((mic) => (
                  <option key={mic.deviceId} value={mic.deviceId}>
                    {mic.label || `Microphone ${mic.deviceId}`}
                  </option>
                ))}
              </select>
            </div>

            {/* Audio Test Section */}
            <div className="mb-4">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <Mic className={`w-4 h-4 transition-colors duration-300 ${
                    audioTestPassed ? "text-green-500" : 
                    micColor === "gray" ? "text-gray-400" : 
                    micColor === "yellow" ? "text-yellow-400" : 
                    micColor === "orange" ? "text-orange-400" : 
                    micColor === "red" ? "text-red-500" : 
                    micColor === "green" ? "text-green-500" : "text-gray-600"
                  }`} />
                  <span className="text-xs font-medium text-gray-700" style={{ fontFamily: 'Manrope, sans-serif' }}>
                    Audio Test
                  </span>
                </div>
                {audioTestPassed ? (
                  <div className="flex items-center gap-1">
                    <CheckCircle className="w-3 h-3 text-green-500" />
                    <span className="text-xs text-green-600 font-medium" style={{ fontFamily: 'Manrope, sans-serif' }}>
                      Passed
                    </span>
                  </div>
                ) : (
                  <div className="flex items-center gap-1">
                    <AlertCircle className="w-3 h-3 text-orange-500" />
                    <span className="text-xs text-orange-600 font-medium" style={{ fontFamily: 'Manrope, sans-serif' }}>
                      {isTestingAudio ? "Listening..." : "Testing"}
                    </span>
                  </div>
                )}
              </div>
              
              <div className="flex items-center gap-3">
                <div className="flex-1">
                  {/* Test Progress Bar */}
                  <div className="w-full bg-gray-200 rounded-full h-2 mb-1">
                    <div
                      className={`h-2 rounded-full transition-all duration-200 ${
                        audioTestPassed ? "bg-green-500" : "bg-blue-500"
                      }`}
                      style={{ width: `${audioTestPassed ? 100 : audioTestProgress}%` }}
                    />
                  </div>
                  
                  <p className="text-xs text-gray-500 mt-1" style={{ fontFamily: 'Manrope, sans-serif' }}>
                    {audioTestPassed ? (
                      <span className="text-green-600 font-medium">Audio test passed! ✓</span>
                    ) : isTestingAudio ? (
                      volumeLevel >= 25 ? (
                        <span className="text-green-600">Keep speaking... ({Math.round(audioTestProgress)}%)</span>
                      ) : (
                        <span className="text-orange-600">Please speak to test your microphone</span>
                      )
                    ) : volumeLevel < 25 ? (
                      "Speak clearly to test your microphone"
                    ) : volumeLevel < 50 ? (
                      "Good level - testing in progress"
                    ) : (
                      "Excellent volume level"
                    )}
                  </p>
                </div>
              </div>
              
              {audioTestPassed && (
                <button
                  onClick={resetAudioTest}
                  className="text-xs text-gray-500 hover:text-gray-700 mt-1"
                  style={{ fontFamily: 'Manrope, sans-serif' }}
                >
                  Reset test
                </button>
              )}
            </div>
          </div>

          {/* System Status */}
          <div className="mb-4">
            <h3 className="text-sm font-semibold text-gray-900 mb-3" style={{ fontFamily: 'Sora, sans-serif' }}>
              System Status
            </h3>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                {localCamStream ? (
                  <CheckCircle className="w-3 h-3 text-green-500" />
                ) : (
                  <AlertCircle className="w-3 h-3 text-red-500" />
                )}
                <span className={`text-xs font-medium ${localCamStream ? 'text-green-700' : 'text-red-700'}`} style={{ fontFamily: 'Manrope, sans-serif' }}>
                  Camera & Mic Access
                </span>
              </div>
              <div className="flex items-center gap-2">
                {audioTestPassed ? (
                  <CheckCircle className="w-3 h-3 text-green-500" />
                ) : (
                  <AlertCircle className="w-3 h-3 text-orange-500" />
                )}
                <span className={`text-xs font-medium ${audioTestPassed ? 'text-green-700' : 'text-orange-700'}`} style={{ fontFamily: 'Manrope, sans-serif' }}>
                  Audio Test {audioTestPassed ? 'Passed' : 'Pending'}
                </span>
              </div>
              <div className="flex items-center gap-2">
                {(localCamStream && audioTestPassed) ? (
                  <CheckCircle className="w-3 h-3 text-green-500" />
                ) : (
                  <AlertCircle className="w-3 h-3 text-orange-500" />
                )}
                <span className={`text-xs font-medium ${(localCamStream && audioTestPassed) ? 'text-green-700' : 'text-orange-700'}`} style={{ fontFamily: 'Manrope, sans-serif' }}>
                  Recording Ready
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeviceTestingSection;
