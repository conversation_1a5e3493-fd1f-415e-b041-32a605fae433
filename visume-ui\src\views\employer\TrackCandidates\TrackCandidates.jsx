import React, { useEffect, useState } from "react";
import {
  HiOutlineShare,
  HiOutlineArchive,
  HiOutlinePencil,
  HiOutlineUpload,
  HiOutlineSearch,
  HiLockOpen,
  HiOutlineBriefcase,
  HiOutlineDownload,
  HiOutlineVideoCamera,
  HiDocumentText,
  HiOutlineDocumentText,
  HiHeart,
  HiLockClosed,
} from "react-icons/hi";
import { 
  ClipboardList, 
  Unlock, 
  MessageSquare, 
  PartyPopper 
} from "lucide-react";
import { useLocation, useNavigate } from "react-router-dom";
import Cookies from "js-cookie";
import Loader from "components/Loader";
import toast from "react-hot-toast";
import { validateEmployerCreditUsage } from "services/employerMembershipService";
import UniversalMembershipModal from "components/shared/UniversalMembershipModal";

import CandidateRow from "./CandidateRow";

const TrackCandidates = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const params = new URLSearchParams(location.search);
  const tabName = params.get("tab");
  const [loader, setLoader] = useState(false);
  const [showSearch, setShowSearch] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [candidates, setCandidates] = useState([]);
  const [activeTabName, setActiveTabName] = useState("Shortlisted");
  const [loadingId, setLoadingId] = useState(null);
  const [noShortlistedProfiles, setNoShortlistedProfiles] = useState(false);
  // 🎯 EMPLOYER MEMBERSHIP: Modal state for credit limit handling
  const [showMembershipModal, setShowMembershipModal] = useState(false);
  const [membershipStatus, setMembershipStatus] = useState({});
  const handleSearchToggle = () => setShowSearch((prev) => !prev);

  const handleSearchChange = (e) => setSearchTerm(e.target.value);

  // 🎯 EMPLOYER MEMBERSHIP: Enhanced status change function with credit validation
  const handleStatusChange = async (video_profile_id, newStatus) => {
    if (newStatus !== "Unlocked") {
      toast.error("This function is not ready yet");
      return;
    }

    if (loadingId === video_profile_id) return;
    setLoadingId(video_profile_id);

    try {
      const emp_id = Cookies.get("employerId");
      if (!emp_id) {
        return toast.error(
          "You need to be an employer to update video profile status"
        );
      }

      // Check credit status before attempting to unlock
      const creditValidation = await validateEmployerCreditUsage(emp_id);

      if (!creditValidation.canUseCredits) {
        setMembershipStatus(creditValidation.membershipStatus);
        setShowMembershipModal(true);
        return;
      }

      const response = await fetch(
        `${
          import.meta.env.VITE_APP_HOST
        }/api/v1/employer-profiles/unlockVideoProfile`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ emp_id, video_profile_id }),
        }
      );

      if (!response.ok) {
        const msg = await response.json();

        // Handle credit exhaustion from backend
        if (response.status === 403 && msg.membershipStatus) {
          setMembershipStatus(msg.membershipStatus);
          setShowMembershipModal(true);
          return;
        }

        return toast.error(msg.message);
      }

      const data = await response.json();
      toast.success(data.message);
      // Instead of refetching all candidates, update state locally to avoid shaking
      // Update candidate status in place to avoid remounts
      setCandidates((prev) =>
        prev.map((c) =>
          c.id === video_profile_id ? { ...c, status: "unlocked" } : c
        )
      );
    } catch (error) {
      toast.error("Error updating video resume status");
      console.error("Error during updating video resume status:", error);
    } finally {
      setLoadingId(null);
    }
  };

  const handleClickOutside = (event) => {
    // Only close if clicking outside both the search button and input
    if (!event.target.closest('.search-container')) {
      setShowSearch(false);
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const toggleUnlockCandidate = (candidateId) => {
    const updateCandidates = (prevData) =>
      prevData.map((candidate) =>
        candidate.id === candidateId
          ? { ...candidate, unlocked: !candidate.unlocked }
          : candidate
      );

    if (activeTabName === "Shortlisted") {
      setShortListedCandidates(updateCandidates);
    } else {
      setUnlockedData(updateCandidates);
    }
  };

  const filteredCandidates = candidates
    .filter((candidate) => {
      if (activeTabName === "Shortlisted") return candidate.status === "shortlisted";
      if (activeTabName === "Unlocked") return candidate.status === "unlocked";
      if (activeTabName === "Interview") return candidate.status === "interview";
      if (activeTabName === "Offers") return candidate.status === "offers";
      return false;
    })
    .filter((candidate) => {
      const name =
        candidate.cand_name ||
        (candidate.jobseeker && candidate.jobseeker.cand_name) ||
        "";
      return name.toLowerCase().includes(searchTerm.toLowerCase());
    });

  console.log(
    "Filtered candidates for tab",
    activeTabName,
    ":",
    filteredCandidates
  );
  const [currentPage, setCurrentPage] = useState(1);
  const candidatesPerPage = 10;
  const totalPages = Math.ceil(filteredCandidates.length / candidatesPerPage);
  const currentCandidates = filteredCandidates.slice(
    (currentPage - 1) * candidatesPerPage,
    currentPage * candidatesPerPage
  );

  const handlePageChange = (page) => {
    if (page > 0 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  const unShortlistCandidate = async (id, cand_id) => {
    if (loadingId === id) return;
    setLoadingId(cand_id);
    try {
      const emp_id = Cookies.get("employerId");
      if (!emp_id) {
        return toast.error(
          "You need to be an employer to UnShortlist profiles"
        );
      }
      const response = await fetch(
        `${
          import.meta.env.VITE_APP_HOST
        }/api/v1/employer-profiles/unshortlist-candidate`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ emp_id, cand_id: id }),
        }
      );
      if (!response.ok) {
        const msg = await response.json();
        toast.error(msg.message);
        throw new Error(`HTTP error! status: ${msg.message}`);
      }
      const data = await response.json();
      toast.success(data.message);
      await fetchCandidates();
    } catch (error) {
      console.error("Error during shortlist operation:", error);
    } finally {
      setLoadingId(null);
    }
  };

  const fetchCandidates = async () => {
    setLoader(true);
    const emp_id = Cookies.get("employerId");
    if (!emp_id) {
      toast.error("You are not an employer");
      navigate("/");
      return;
    }

    try {
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/shortlisted-profiles/${emp_id}`
      );
      if (response.status === 404) {
        setNoShortlistedProfiles(true);
        setCandidates([]);
        setLoader(false);
        return;
      } else {
        setNoShortlistedProfiles(false);
      }
      const data = await response.json();

      if (data.data) {
        console.log("Fetched candidates:", data.data);
        setCandidates(data.data);
        // Only switch tab if current tab is empty and another has data
        const shortlistedCandidates = data.data.filter(
          (e) => e.status === "shortlisted"
        );
        const unlockedCandidates = data.data.filter(
          (e) => e.status === "unlocked"
        );
        if (
          shortlistedCandidates.length > 0 &&
          unlockedCandidates.length === 0 &&
          activeTabName !== "Shortlisted"
        ) {
          setActiveTabName("Shortlisted");
        } else if (
          unlockedCandidates.length > 0 &&
          shortlistedCandidates.length === 0 &&
          activeTabName !== "Unlocked"
        ) {
          setActiveTabName("Unlocked");
        }
      }
    } catch (err) {
      console.error("Error fetching shortlisted profiles:", err);
    } finally {
      setLoader(false);
    }
  };

  useEffect(() => {
    fetchCandidates();
  }, []);

  useEffect(() => {
    if (tabName) {
      setActiveTabName(tabName);
    }
  }, [tabName]);

  return (
    <div className="space-y-6">
      {loader && <Loader text={"Fetching Shortlisted Profiles"} />}

      {/* Header Section */}
      <div className="flex flex-col items-start justify-between gap-4 md:flex-row md:items-center">
        <div className="flex items-center gap-3">
          <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600">
            <HiOutlineBriefcase className="h-6 w-6 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white" style={{fontFamily: 'Sora, sans-serif'}}>
              Track Candidates
            </h1>
            <p className="text-sm text-gray-500 dark:text-gray-400" style={{fontFamily: 'Manrope, sans-serif'}}>
              Manage your candidate pipeline
            </p>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <div className="search-container relative">
            {!showSearch ? (
              <button
                onClick={handleSearchToggle}
                className="flex items-center gap-2 rounded-lg border border-gray-200 bg-white px-3 py-2 text-gray-700 transition-all hover:bg-gray-50 hover:border-gray-300 dark:border-navy-600 dark:bg-navy-800 dark:text-gray-300 dark:hover:bg-navy-700"
                style={{fontFamily: 'Manrope, sans-serif'}}
              >
                <HiOutlineSearch className="h-4 w-4" />
                <span className="text-sm font-medium">Search</span>
              </button>
            ) : (
              <div className="flex items-center gap-2">
                <div className="relative">
                  <HiOutlineSearch className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={handleSearchChange}
                    className="w-72 rounded-lg border border-gray-300 bg-white pl-10 pr-4 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-100 dark:border-navy-600 dark:bg-navy-800 dark:text-white dark:focus:border-blue-400"
                    placeholder="Search candidates..."
                    style={{fontFamily: 'Manrope, sans-serif'}}
                    autoFocus
                  />
                </div>
                <button
                  onClick={() => {
                    setShowSearch(false);
                    setSearchTerm('');
                  }}
                  className="rounded-lg border border-gray-200 bg-white px-3 py-2 text-gray-500 hover:bg-gray-50 hover:text-gray-700 dark:border-navy-600 dark:bg-navy-800 dark:text-gray-400"
                >
                  ✕
                </button>
              </div>
            )}
          </div>
          <button className="flex items-center gap-2 rounded-lg bg-gradient-to-r from-blue-600 to-blue-700 px-4 py-2 text-white shadow-sm transition-all hover:from-blue-700 hover:to-blue-800 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2" style={{fontFamily: 'Manrope, sans-serif'}}>
            <HiOutlineDownload className="h-4 w-4" />
            <span className="text-sm font-medium">Export Data</span>
          </button>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white dark:bg-navy-800">
        <nav className="flex space-x-1 rounded-lg bg-gray-100 p-1 dark:bg-navy-700">
          {["Shortlisted", "Unlocked", "Interview", "Offers"].map((tab) => {
            // Determine if the tab should be disabled
            const isDisabled =
              (tab === "Shortlisted" && candidates.filter((c) => c.status === "shortlisted").length === 0) ||
              (tab === "Unlocked" && candidates.filter((c) => c.status === "unlocked").length === 0) ||
              (tab === "Interview" && candidates.filter((c) => c.status === "interview").length === 0) ||
              (tab === "Offers" && candidates.filter((c) => c.status === "offers").length === 0);

            const getTabConfig = (tabName) => {
              switch (tabName) {
                case "Shortlisted":
                  return { 
                    color: "blue", 
                    bgColor: "bg-blue-600", 
                    hoverColor: "hover:bg-blue-50",
                    icon: ClipboardList
                  };
                case "Unlocked":
                  return { 
                    color: "orange", 
                    bgColor: "bg-orange-600", 
                    hoverColor: "hover:bg-orange-50",
                    icon: Unlock
                  };
                case "Interview":
                  return { 
                    color: "green", 
                    bgColor: "bg-green-600", 
                    hoverColor: "hover:bg-green-50",
                    icon: MessageSquare
                  };
                case "Offers":
                  return { 
                    color: "purple", 
                    bgColor: "bg-purple-600", 
                    hoverColor: "hover:bg-purple-50",
                    icon: PartyPopper
                  };
                default:
                  return { 
                    color: "gray", 
                    bgColor: "bg-gray-600", 
                    hoverColor: "hover:bg-gray-50",
                    icon: ClipboardList
                  };
              }
            };

            const getCount = (tabName) => {
              switch (tabName) {
                case "Shortlisted":
                  return candidates.filter((c) => c.status === "shortlisted").length;
                case "Unlocked":
                  return candidates.filter((c) => c.status === "unlocked").length;
                case "Interview":
                  return candidates.filter((c) => c.status === "interview").length;
                case "Offers":
                  return candidates.filter((c) => c.status === "offers").length;
                default:
                  return 0;
              }
            };

            const tabConfig = getTabConfig(tab);
            const count = getCount(tab);
            const IconComponent = tabConfig.icon;

            return (
              <button
                key={tab}
                className={`relative flex flex-1 items-center justify-center gap-2 rounded-md px-3 py-2.5 text-sm font-medium transition-all duration-200 ${
                  activeTabName === tab
                    ? `${tabConfig.bgColor} text-white shadow-sm`
                    : isDisabled
                    ? "cursor-not-allowed text-gray-400 bg-transparent"
                    : `text-gray-700 bg-transparent ${tabConfig.hoverColor} hover:text-gray-900 dark:text-gray-300 dark:hover:bg-navy-600 dark:hover:text-white`
                }`}
                onClick={() => {
                  if (!isDisabled) {
                    setActiveTabName(tab);
                  }
                }}
                disabled={isDisabled}
                style={{fontFamily: 'Manrope, sans-serif'}}
              >
                <IconComponent className="h-4 w-4" />
                <span>{tab}</span>
                <span className={`inline-flex h-5 w-5 items-center justify-center rounded-full text-xs font-bold transition-colors ${
                  activeTabName === tab
                    ? "bg-white/20 text-white"
                    : count > 0
                    ? `${tabConfig.bgColor} text-white`
                    : "bg-gray-200 text-gray-600 dark:bg-navy-600 dark:text-gray-400"
                }`}>
                  {count}
                </span>
                {count > 0 && activeTabName !== tab && (
                  <div className={`absolute -top-1 -right-1 h-2 w-2 rounded-full ${tabConfig.bgColor}`} />
                )}
              </button>
            );
          })}
        </nav>
      </div>

      {noShortlistedProfiles ? (
        <div className="flex flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 py-12 dark:border-navy-500 dark:bg-navy-800/50">
          <div className="mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/20">
            <HiOutlineBriefcase className="h-8 w-8 text-blue-500 dark:text-blue-400" />
          </div>
          <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white" style={{fontFamily: 'Sora, sans-serif'}}>
            No Candidates Yet
          </h3>
          <p className="max-w-md text-center text-gray-600 dark:text-gray-400" style={{fontFamily: 'Manrope, sans-serif'}}>
            No shortlisted profiles found. Start by shortlisting candidates using Profile Search to see them here.
          </p>
        </div>
      ) : filteredCandidates.length > 0 ? (
        <div className="space-y-4">
          {/* Candidates Table */}
          <div className="overflow-hidden rounded-lg border border-gray-200 bg-white dark:border-navy-600 dark:bg-navy-800">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 dark:bg-navy-700">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300" style={{fontFamily: 'Manrope, sans-serif'}}>
                      Candidate
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300" style={{fontFamily: 'Manrope, sans-serif'}}>
                      Role
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300" style={{fontFamily: 'Manrope, sans-serif'}}>
                      Skills
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300" style={{fontFamily: 'Manrope, sans-serif'}}>
                      Scores
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300" style={{fontFamily: 'Manrope, sans-serif'}}>
                      Contact
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300" style={{fontFamily: 'Manrope, sans-serif'}}>
                      Status
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300" style={{fontFamily: 'Manrope, sans-serif'}}>
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white dark:divide-navy-600 dark:bg-navy-800">
                  {currentCandidates.map((candidate) => (
                    <CandidateRow
                      tabName={activeTabName}
                      loadingId={loadingId}
                      key={candidate?.id}
                      candidate={candidate}
                      onStatusChange={handleStatusChange}
                      onToggleUnlock={toggleUnlockCandidate}
                      unShortlistCandidate={unShortlistCandidate}
                    />
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-center space-x-2">
              <button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className={`px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                  currentPage === 1
                    ? "cursor-not-allowed bg-gray-100 text-gray-400"
                    : "bg-white border border-gray-300 text-gray-700 hover:bg-gray-50"
                }`}
                style={{fontFamily: 'Manrope, sans-serif'}}
              >
                Previous
              </button>

              <div className="flex space-x-1">
                {Array.from({ length: totalPages }, (_, index) => (
                  <button
                    key={index + 1}
                    onClick={() => handlePageChange(index + 1)}
                    className={`px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                      currentPage === index + 1
                        ? "bg-blue-600 text-white"
                        : "bg-white border border-gray-300 text-gray-700 hover:bg-gray-50"
                    }`}
                    style={{fontFamily: 'Manrope, sans-serif'}}
                  >
                    {index + 1}
                  </button>
                ))}
              </div>

              <button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className={`px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                  currentPage === totalPages
                    ? "cursor-not-allowed bg-gray-100 text-gray-400"
                    : "bg-white border border-gray-300 text-gray-700 hover:bg-gray-50"
                }`}
                style={{fontFamily: 'Manrope, sans-serif'}}
              >
                Next
              </button>
            </div>
          )}
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 py-12 dark:border-navy-500 dark:bg-navy-800/50">
          <div className="mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-orange-100 dark:bg-orange-900/20">
            <HiOutlineSearch className="h-8 w-8 text-orange-500 dark:text-orange-400" />
          </div>
          <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white" style={{fontFamily: 'Sora, sans-serif'}}>
            No {activeTabName} Profiles Found
          </h3>
          <p className="max-w-md text-center text-gray-600 dark:text-gray-400" style={{fontFamily: 'Manrope, sans-serif'}}>
            No {activeTabName.toLowerCase()} profiles found for your current search. Try adjusting your search terms or browse all candidates.
          </p>
        </div>
      )}

      {/* 🎯 EMPLOYER MEMBERSHIP: Universal membership modal for credit limits */}
      <UniversalMembershipModal
        isOpen={showMembershipModal}
        onClose={() => setShowMembershipModal(false)}
        userType="employer"
        membershipStatus={membershipStatus}
        whatsappNumber="[WHATSAPP_NUMBER_PLACEHOLDER]"
      />
    </div>
  );
};

export default TrackCandidates;
