import React, { useState, useEffect } from "react";
import Cookies from "js-cookie";
import VideoProfileCard from "./components/VideoProfileCard";
import { useNavigate } from "react-router-dom";
import toast from "react-hot-toast";
import SmLoader from "../../components/SmLoader";
import { HiVideoCamera, HiOutlineSparkles } from "react-icons/hi";
import { Video, Plus } from "lucide-react";

function AllVideoProfiles() {
  const [videoProfiles, setVideoProfiles] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const jstoken = Cookies.get("jstoken");
  const candId = Cookies.get("candId");
  const navigate = useNavigate()

  useEffect(() => {
    const fetchVideoProfiles = async () => {
      try {
        // Make the API request with the dynamic candId
        setIsLoading(true);
        const response = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/video-resume/${candId}`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        if (response.ok) {
          const data = await response.json();

          // Iterate over the response and pick out the necessary fields
          const newProfiles = data.map((profile) => ({
            vpid: profile.video_profile_id,
            role: profile.role,
            skills: profile.skills.split(",").map((skill) => skill.trim()), // Convert skills to an array
            status: profile.status,
          }));
          // Append the new profiles to the existing videoProfiles list
          setVideoProfiles((prevProfiles) => {
            const allProfiles = [...prevProfiles, ...newProfiles];

            if (allProfiles.length > 0) {
              return allProfiles; // Update state with the new list of profiles
            } else {
              navigate("../"); // Redirect to home
              toast("No Video Resumes Found. Create Now."); // Show toast notification
              return prevProfiles; // Optionally return the previous profiles to avoid state becoming undefined
            }
          });
        } else {
          console.error(
            "Error fetching video profiles:",
            response.status,
            response.statusText
          );
        }
      } catch (error) {
        console.error("Network error:", error);
      }
      setIsLoading(false);
    };

    // Only fetch if candId is provided
    if (candId) {
      fetchVideoProfiles();
    }
  }, [candId]);

  return (
    <>
      {jstoken ? (
        <div className="p-4 space-y-8">
          {/* Enhanced Header */}
          <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 rounded-xl shadow-sm">
            <div className="p-6 border-b border-gray-200 dark:border-gray-800">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-br from-blue-100 to-cyan-100 dark:from-blue-900/50 dark:to-cyan-900/50 rounded-lg">
                    <HiVideoCamera className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
                      All Video Resumes
                    </h1>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Manage and view all your created video resumes
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => navigate('/candidate')}
                  className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                >
                  <Plus className="w-4 h-4" />
                  Create New
                </button>
              </div>
            </div>
          </div>

          {/* Enhanced Content */}
          <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 rounded-xl shadow-sm">
            <div className="p-6">
              {isLoading ? (
                <div className="flex items-center justify-center py-12">
                  <SmLoader text={"Loading your video resumes..."} />
                </div>
              ) : videoProfiles.length === 0 ? (
                <div className="text-center py-12">
                  <div className="w-20 h-20 bg-gradient-to-br from-blue-100 to-cyan-100 dark:from-blue-900/50 dark:to-cyan-900/50 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
                    <HiVideoCamera className="w-10 h-10 text-blue-600 dark:text-blue-400" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                    No video resumes yet
                  </h3>
                  <p className="text-gray-500 dark:text-gray-400 mb-6 max-w-sm mx-auto">
                    Create your first video resume to showcase your skills and personality to potential employers
                  </p>
                  <button
                    onClick={() => navigate('/candidate')}
                    className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                  >
                    <Video className="w-5 h-5" />
                    Create Your First Visume
                  </button>
                </div>
              ) : (
                <div className="space-y-4">
                  {videoProfiles.map((profile, index) => (
                    <VideoProfileCard
                      key={index}
                      profile={profile}
                      toggleVideoProfilePopup={() => {}}
                    />
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      ) : (
        <div className="p-4">
          <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 rounded-xl shadow-sm p-8 text-center">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">Please Sign In</h2>
            <p className="text-gray-500 dark:text-gray-400">You need to be signed in to view your video resumes</p>
          </div>
        </div>
      )}
    </>
  );
}

export default AllVideoProfiles;
