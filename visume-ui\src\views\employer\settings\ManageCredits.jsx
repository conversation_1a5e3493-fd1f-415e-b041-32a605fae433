import { useEffect, useState } from "react";
import { ArrowUpRight, HelpCircle, Settings, User, Lock, CreditCard, Users } from "lucide-react";
import { HiOutlineUserGroup } from "react-icons/hi";
import { MdCreditScore } from "react-icons/md";
import GeneralProfile from "./GeneralProfile";
import Cookies from "js-cookie";
import EmployerPasswordSettings from "./EmployerPasswordSettings";

const formatDate = (date) => {
  const dates = new Date(date);
  const options = { month: "long", day: "numeric", year: "numeric" };
  return dates.toLocaleDateString("en-US", options);
};

function ManageCredits() {
  const [activeSection, setActiveSection] = useState("general");
  const emp_id = Cookies.get("employerId");
  const [empData, setEmpData] = useState({
    plan_name: "Visume Exclusive",
    creditsLeft: 100,
    totalCredits: 100,
    end_date: Date.now() + 365 * 24 * 60 * 60 * 1000,
  });

  useEffect(() => {
    // Set default active section to general for tab-based navigation
    setActiveSection('general');
  }, []);

  useEffect(() => {
    const fetchCandidates = async () => {
      try {
        const profileData = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/employerProfilesData`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              Authorization: emp_id,
            },
          }
        );
        const profileJson = await profileData.json();

        if (profileJson.data) {
          setEmpData({
            plan_name: profileJson?.data?.plan_name || "Visume Exclusive",
            creditsLeft: profileJson?.data?.creditsLeft,
            totalCredits: profileJson?.data?.totalCredits || profileJson?.data?.credits_assigned || 10, // 🎯 CREDIT FIX: Use dynamic total credits
            end_date: profileJson?.data?.end_date,
          });
        }
      } catch (err) {
        console.error("Error fetching shortlisted profiles:", err);
      }
    };

    fetchCandidates();
  }, [emp_id]);

  const handleSectionChange = (section) => {
    // Update state directly for tab-based navigation instead of routing
    setActiveSection(section);
  };

  const renderSection = () => {

    switch (activeSection) {
      case "general":
        return (<GeneralProfile />);
      case "password":
        return <EmployerPasswordSettings />;
      case "team":
        return (
          <div className="p-6">
            <div className="mb-8 flex items-center gap-3">
              <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-br from-orange-500 to-red-600 rounded-lg">
                <HiOutlineUserGroup className="w-4 h-4 text-white" />
              </div>
              <h2 className="text-xl font-bold text-gray-900 dark:text-white">Team Management</h2>
            </div>

            <div className="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-navy-700 dark:to-navy-800 rounded-xl border border-gray-200 dark:border-navy-600 p-8 text-center">
              <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-orange-100 to-red-100 dark:from-orange-900/20 dark:to-red-900/20 rounded-full mb-4 mx-auto">
                <HiOutlineUserGroup className="w-8 h-8 text-orange-500 dark:text-orange-400" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Team Management</h3>
              <p className="text-gray-600 dark:text-gray-400 max-w-md mx-auto">
                Manage your team members and roles here. This feature will be available soon.
              </p>
            </div>
          </div>
        );
      case "plan-billing":
        return (
          <div className="p-6">
            <div className="mb-8 flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg">
                  <MdCreditScore className="w-4 h-4 text-white" />
                </div>
                <h2 className="text-xl font-bold text-gray-900 dark:text-white">Plan & Billing</h2>
              </div>
              <button className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors duration-200">
                Manage payments
                <ArrowUpRight className="w-4 h-4" />
              </button>
            </div>

            {/* Current Plan Card */}
            <div className="mb-8 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-navy-700 dark:to-navy-800 rounded-xl border border-gray-200 dark:border-navy-600 p-6 shadow-sm">
              <div className="mb-4 flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Current Plan</h3>
                <button className="px-4 py-2 text-sm font-medium text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors duration-200">
                  Change plan
                </button>
              </div>
              <div className="flex items-center gap-3 mb-2">
                <span className="text-2xl font-bold text-gray-900 dark:text-white">
                  {empData.plan_name} Plan
                </span>
                <div className="flex items-center gap-2">
                  <span
                    className={`h-2 w-2 rounded-full ${
                      new Date(empData.end_date) > Date.now()
                        ? "bg-green-500"
                        : "bg-red-500"
                    }`}
                  ></span>
                  <span
                    className={`text-sm font-medium ${
                      new Date(empData.end_date) > Date.now()
                        ? "text-green-600 dark:text-green-400"
                        : "text-red-600 dark:text-red-400"
                    }`}
                  >
                    {new Date(empData.end_date) > Date.now() ? "Active" : "Inactive"}
                  </span>
                </div>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Renew at {formatDate(empData.end_date)}
              </p>
            </div>

            {/* Usage Section */}
            <div>
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Usage</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Your usage is updated at the end of every day.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Profile Credits Card */}
                <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl border border-blue-200 dark:border-blue-800 p-6 shadow-sm">
                  <div className="flex items-center gap-2 mb-4">
                    <span className="text-sm font-semibold text-blue-900 dark:text-blue-100">
                      Profile Credits
                    </span>
                    <HelpCircle className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                  </div>
                  <p className="text-2xl font-bold text-blue-900 dark:text-blue-100 mb-3">
                    {empData.creditsLeft} of {empData.totalCredits}
                  </p>
                  <div className="w-full bg-blue-200 dark:bg-blue-800 rounded-full h-3">
                    <div
                      className="h-3 rounded-full bg-gradient-to-r from-blue-500 to-indigo-600 transition-all duration-300"
                      style={{
                        width: `${
                          ((empData.totalCredits - empData.creditsLeft) /
                            empData.totalCredits) *
                          100
                        }%`,
                      }}
                    ></div>
                  </div>
                </div>

                {/* AI Interviews Card */}
                <div className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-xl border border-purple-200 dark:border-purple-800 p-6 shadow-sm">
                  <div className="flex items-center gap-2 mb-4">
                    <span className="text-sm font-semibold text-purple-900 dark:text-purple-100">
                      AI Interviews
                    </span>
                    <HelpCircle className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                  </div>
                  <p className="text-2xl font-bold text-purple-900 dark:text-purple-100 mb-3">0 of 3</p>
                  <div className="w-full bg-purple-200 dark:bg-purple-800 rounded-full h-3">
                    <div
                      className="h-3 rounded-full bg-gradient-to-r from-purple-500 to-pink-600 transition-all duration-300"
                      style={{ width: "0%" }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      default:
        return (<GeneralProfile />);
    }
  };

  // Tab configuration
  const tabs = [
    { id: "general", label: "Profile", icon: User },
    { id: "password", label: "Security", icon: Lock },
    { id: "plan-billing", label: "Plan & Billing", icon: CreditCard },
    { id: "team", label: "Team", icon: Users }
  ];

  return (
    <div className="p-3 max-w-6xl mx-auto">
      {/* Compact Header */}
      <div className="bg-white dark:bg-slate-800 rounded-2xl border border-slate-200 dark:border-slate-700 mb-6">
        <div className="flex items-center justify-between p-5">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-600 rounded-xl">
              <Settings className="h-5 w-5 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-slate-900 dark:text-white font-sora">
                Settings
              </h1>
              <p className="text-sm text-slate-600 dark:text-slate-400 font-manrope">
                Manage your account
              </p>
            </div>
          </div>
          <div className="hidden sm:flex items-center gap-2 bg-slate-50 dark:bg-slate-700 rounded-lg px-3 py-2">
            <User className="w-4 h-4 text-slate-500" />
            <span className="text-sm text-slate-600 dark:text-slate-400 font-manrope">
              Employer
            </span>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="bg-white dark:bg-slate-800 rounded-2xl border border-slate-200 dark:border-slate-700">
        {/* Compact Tab Navigation */}
        <div className="border-b border-slate-200 dark:border-slate-700 p-4">
          <nav className="flex flex-wrap gap-1">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              const isActive = activeSection === tab.id;
              return (
                <button
                  key={tab.id}
                  onClick={() => handleSectionChange(tab.id)}
                  className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-all font-manrope ${
                    isActive
                      ? 'bg-blue-600 text-white shadow-md'
                      : 'text-slate-600 dark:text-slate-400 hover:bg-slate-100 dark:hover:bg-slate-700'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span className="hidden sm:block">{tab.label}</span>
                </button>
              );
            })}
          </nav>
        </div>
        <div className="p-4">
          {/* Content Area */}
          <div>
            {renderSection()}
          </div>
        </div>
      </div>
    </div>
  );
}

export default ManageCredits;