import { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { ArrowUpRight, EyeIcon, EyeOffIcon, HelpCircle } from "lucide-react";
import { HiAdjustments,HiLockClosed,HiOutlineUserGroup,} from "react-icons/hi";
import { MdCreditScore } from "react-icons/md";
import GeneralProfile from "./GeneralProfile";
import Cookies from "js-cookie";
import toast from "react-hot-toast";
import EmployerPasswordSettings from "./EmployerPasswordSettings";

const formatDate = (date) => {
  const dates = new Date(date);
  const options = { month: "long", day: "numeric", year: "numeric" };
  return dates.toLocaleDateString("en-US", options);
};

function ManageCredits() {

  const navigate = useNavigate();
  const location = useLocation();                                            //currentpath url 
  const [activeSection, setActiveSection] = useState("general");             
  const emp_id = Cookies.get("employerId");
  console.log(emp_id)
  const [empData, setEmpData] = useState({
    plan_name: "Visume Exclusive",
    creditsLeft: 100,
    totalCredits: 100,
    end_date: Date.now() + 365 * 24 * 60 * 60 * 1000,
  });

  useEffect(() => {
    // Extract the section from the URL
    const path = location.pathname.split("/").pop();       // console.log(path) ["employer"/"settings"].pop() output:settings

    if (path && path !== 'settings') {               
      setActiveSection(path);
    } else {
      setActiveSection('general');
    }
  }, [location]);

  useEffect(() => {
    const fetchCandidates = async () => {
      try {
        const profileData = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/employerProfilesData`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              Authorization: emp_id,
            },
          }
        );
        const profileJson = await profileData.json();

        if (profileJson.data) {
          setEmpData({
            plan_name: profileJson?.data?.plan_name || "Visume Exclusive",
            creditsLeft: profileJson?.data?.creditsLeft,
            totalCredits: profileJson?.data?.totalCredits || profileJson?.data?.credits_assigned || 10, // 🎯 CREDIT FIX: Use dynamic total credits
            end_date: profileJson?.data?.end_date,
          });
        }
      } catch (err) {
        console.error("Error fetching shortlisted profiles:", err);
      }
    };

    fetchCandidates();
  }, [emp_id]);

  const handleSectionChange = (section) => {                      //section = general,password,team,plan-billing
    const path = section === 'general' ? '/employer/settings' : `/employer/settings/${section}`;
    navigate(path);
    setActiveSection(section);   
    console.log(activeSection)
  };

  const renderSection = () => {

    switch (activeSection) {
      case "general":
        return (<GeneralProfile />);
      case "password":
        return <EmployerPasswordSettings />;
      case "team":
        return (
          <div className="p-6">
            <div className="mb-8 flex items-center gap-3">
              <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-br from-orange-500 to-red-600 rounded-lg">
                <HiOutlineUserGroup className="w-4 h-4 text-white" />
              </div>
              <h2 className="text-xl font-bold text-gray-900 dark:text-white">Team Management</h2>
            </div>

            <div className="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-navy-700 dark:to-navy-800 rounded-xl border border-gray-200 dark:border-navy-600 p-8 text-center">
              <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-orange-100 to-red-100 dark:from-orange-900/20 dark:to-red-900/20 rounded-full mb-4 mx-auto">
                <HiOutlineUserGroup className="w-8 h-8 text-orange-500 dark:text-orange-400" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Team Management</h3>
              <p className="text-gray-600 dark:text-gray-400 max-w-md mx-auto">
                Manage your team members and roles here. This feature will be available soon.
              </p>
            </div>
          </div>
        );
      case "plan-billing":
        return (
          <div className="p-6">
            <div className="mb-8 flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg">
                  <MdCreditScore className="w-4 h-4 text-white" />
                </div>
                <h2 className="text-xl font-bold text-gray-900 dark:text-white">Plan & Billing</h2>
              </div>
              <button className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors duration-200">
                Manage payments
                <ArrowUpRight className="w-4 h-4" />
              </button>
            </div>

            {/* Current Plan Card */}
            <div className="mb-8 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-navy-700 dark:to-navy-800 rounded-xl border border-gray-200 dark:border-navy-600 p-6 shadow-sm">
              <div className="mb-4 flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Current Plan</h3>
                <button className="px-4 py-2 text-sm font-medium text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors duration-200">
                  Change plan
                </button>
              </div>
              <div className="flex items-center gap-3 mb-2">
                <span className="text-2xl font-bold text-gray-900 dark:text-white">
                  {empData.plan_name} Plan
                </span>
                <div className="flex items-center gap-2">
                  <span
                    className={`h-2 w-2 rounded-full ${
                      new Date(empData.end_date) > Date.now()
                        ? "bg-green-500"
                        : "bg-red-500"
                    }`}
                  ></span>
                  <span
                    className={`text-sm font-medium ${
                      new Date(empData.end_date) > Date.now()
                        ? "text-green-600 dark:text-green-400"
                        : "text-red-600 dark:text-red-400"
                    }`}
                  >
                    {new Date(empData.end_date) > Date.now() ? "Active" : "Inactive"}
                  </span>
                </div>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Renew at {formatDate(empData.end_date)}
              </p>
            </div>

            {/* Usage Section */}
            <div>
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Usage</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Your usage is updated at the end of every day.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Profile Credits Card */}
                <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl border border-blue-200 dark:border-blue-800 p-6 shadow-sm">
                  <div className="flex items-center gap-2 mb-4">
                    <span className="text-sm font-semibold text-blue-900 dark:text-blue-100">
                      Profile Credits
                    </span>
                    <HelpCircle className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                  </div>
                  <p className="text-2xl font-bold text-blue-900 dark:text-blue-100 mb-3">
                    {empData.creditsLeft} of {empData.totalCredits}
                  </p>
                  <div className="w-full bg-blue-200 dark:bg-blue-800 rounded-full h-3">
                    <div
                      className="h-3 rounded-full bg-gradient-to-r from-blue-500 to-indigo-600 transition-all duration-300"
                      style={{
                        width: `${
                          ((empData.totalCredits - empData.creditsLeft) /
                            empData.totalCredits) *
                          100
                        }%`,
                      }}
                    ></div>
                  </div>
                </div>

                {/* AI Interviews Card */}
                <div className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-xl border border-purple-200 dark:border-purple-800 p-6 shadow-sm">
                  <div className="flex items-center gap-2 mb-4">
                    <span className="text-sm font-semibold text-purple-900 dark:text-purple-100">
                      AI Interviews
                    </span>
                    <HelpCircle className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                  </div>
                  <p className="text-2xl font-bold text-purple-900 dark:text-purple-100 mb-3">0 of 3</p>
                  <div className="w-full bg-purple-200 dark:bg-purple-800 rounded-full h-3">
                    <div
                      className="h-3 rounded-full bg-gradient-to-r from-purple-500 to-pink-600 transition-all duration-300"
                      style={{ width: "0%" }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      default:
        return (<GeneralProfile />);
    }
  };

  return (
    <div className="rounded-2xl bg-gradient-to-br from-white via-gray-50 to-gray-100 p-6 shadow-xl dark:bg-gradient-to-br dark:from-navy-700 dark:via-navy-800 dark:to-navy-900">
      {/* Header Section */}
      <div className="mb-8 flex flex-col md:flex-row items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center gap-3">
            <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg shadow-sm">
              <HiAdjustments className="w-5 h-5 text-white" />
            </div>
            <h1 className="text-sm md:text-2xl font-bold text-gray-900 dark:text-white">
              Settings
            </h1>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-5 gap-6">
        {/* Sidebar Navigation */}
        <div className="lg:col-span-1">
          <div className="bg-white dark:bg-navy-800 rounded-xl shadow-lg border border-gray-200 dark:border-navy-600 p-6">
            {/* My Account Section */}
            <div className="mb-8">
              <h2 className="text-sm font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-4">
                My Account
              </h2>
              <ul className="space-y-2">
                <li>
                  <button
                    onClick={() => handleSectionChange("general")}
                    className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                      activeSection === "general"
                        ? "bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg"
                        : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-navy-700"
                    }`}
                  >
                    <HiAdjustments className="w-4 h-4" />
                    General
                  </button>
                </li>
                <li>
                  <button
                    onClick={() => handleSectionChange("password")}
                    className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                      activeSection === "password"
                        ? "bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg"
                        : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-navy-700"
                    }`}
                  >
                    <HiLockClosed className="w-4 h-4" />
                    Password
                  </button>
                </li>
              </ul>
            </div>

            {/* Organization Section */}
            <div>
              <h2 className="text-sm font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-4">
                Organization
              </h2>
              <ul className="space-y-2">
                <li>
                  <button
                    onClick={() => handleSectionChange("plan-billing")}
                    className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                      activeSection === "plan-billing"
                        ? "bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg"
                        : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-navy-700"
                    }`}
                  >
                    <MdCreditScore className="w-4 h-4" />
                    Plan & Billing
                  </button>
                </li>
                <li>
                  <button
                    onClick={() => handleSectionChange("team")}
                    className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                      activeSection === "team"
                        ? "bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg"
                        : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-navy-700"
                    }`}
                  >
                    <HiOutlineUserGroup className="w-4 h-4" />
                    Team
                  </button>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Content Area */}
        <div className="lg:col-span-4">
          <div className="bg-white dark:bg-navy-800 rounded-xl shadow-lg border border-gray-200 dark:border-navy-600 min-h-[70vh]">
            {renderSection()}
          </div>
        </div>
      </div>
    </div>
  );
}

export default ManageCredits;