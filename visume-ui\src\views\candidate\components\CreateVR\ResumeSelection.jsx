import React from "react";
import { FileText, Upload, Check<PERSON>ircle, <PERSON>, Eye } from "lucide-react";
import ResumePreview from "components/ResumePreview";

const ResumeSelection = ({
  resumeData,
  existingResumes,
  selectedResumeId,
  isLoadingResumes,
  isUploadingResume,
  showResumePreview,
  setShowResumePreview,
  handleResumeSelection,
  handleNewResumeUpload,
  candId,
}) => (
  <div className="space-y-6">
    <div className="text-center">
      <div className="mx-auto mb-4 flex h-14 w-14 items-center justify-center rounded-lg bg-blue-50 border border-blue-100">
        <FileText className="h-7 w-7 text-blue-600" />
      </div>
      <h1 className="mb-2 text-2xl font-semibold text-gray-900">
        Select Your Resume
      </h1>
      <p className="text-gray-600">
        Choose an existing resume or create a new one
      </p>
    </div>

    {isLoadingResumes ? (
      <div className="flex flex-col items-center justify-center py-12">
        <div className="h-8 w-8 animate-spin rounded-full border-3 border-blue-600 border-t-transparent"></div>
        <span className="mt-4 text-gray-600">
          Loading your resumes...
        </span>
      </div>
    ) : (
      <div className="space-y-4">
        {/* Resume Options */}
        <div className="grid gap-4">
          {/* Default Resume Option */}
          {resumeData && (
            <label
              className={`relative cursor-pointer rounded-lg border-2 p-4 transition-all duration-200 ${
                selectedResumeId === "default"
                  ? "border-blue-500 bg-blue-50 shadow-sm"
                  : "border-gray-200 bg-white hover:border-blue-300 hover:shadow-sm"
              }`}
            >
              <input
                type="radio"
                name="resumeSelect"
                value="default"
                checked={selectedResumeId === "default"}
                onChange={handleResumeSelection}
                className="sr-only"
              />
              <div className="flex items-center gap-4">
                <div className="rounded-lg bg-blue-100 p-3">
                  <FileText className="h-6 w-6 text-blue-600" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-3">
                    <h3 className="font-semibold text-gray-900">
                      My Uploaded Resume
                    </h3>
                    <span className="inline-flex items-center rounded-full bg-blue-100 px-3 py-1 text-xs font-medium text-blue-700">
                      Recommended
                    </span>
                  </div>
                  <p className="mt-1 text-sm text-gray-600">
                    Use your previously uploaded resume data
                  </p>
                </div>
                {selectedResumeId === "default" && (
                  <CheckCircle className="h-6 w-6 text-blue-600" />
                )}
              </div>
            </label>
          )}

          {/* Upload New Resume Option */}
          <label
            htmlFor="new-resume-upload-trigger"
            className="relative cursor-pointer rounded-lg border-2 border-gray-200 bg-white p-4 transition-all duration-200 hover:border-blue-300 hover:shadow-sm"
          >
            <div className="flex items-center gap-4">
              <div className="rounded-lg bg-gray-100 p-3">
                <Upload className="h-6 w-6 text-gray-600" />
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-gray-900">
                  Upload New Resume
                </h3>
                <p className="mt-1 text-sm text-gray-600">
                  Click to upload a new resume file and create your video profile
                </p>
                {isUploadingResume && (
                  <div className="mt-3 flex items-center gap-2">
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-blue-600 border-t-transparent"></div>
                    <span className="text-sm font-medium text-blue-600">
                      Uploading your resume...
                    </span>
                  </div>
                )}
              </div>
              <Upload className="h-6 w-6 text-gray-400" />
            </div>
            <input
              id="new-resume-upload-trigger"
              type="file"
              accept=".pdf,.doc,.docx"
              className="hidden"
              onChange={handleNewResumeUpload}
              disabled={isUploadingResume}
            />
          </label>
        </div>

        {/* No resumes message */}
        {existingResumes.length === 0 && !resumeData && (
          <div className="rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 py-8 text-center">
            <FileText className="mx-auto mb-3 h-10 w-10 text-gray-400" />
            <p className="text-gray-600">
              No existing resumes found. Create a new one to get started.
            </p>
          </div>
        )}

        {/* Success message for default resume */}
        {selectedResumeId === "default" && (
          <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
            <div className="flex items-center gap-3">
              <CheckCircle className="h-5 w-5 text-blue-600" />
              <p className="font-medium text-blue-800">
                Perfect! Using your uploaded resume data
              </p>
            </div>
          </div>
        )}

      </div>
    )}
  </div>
);

export default ResumeSelection;