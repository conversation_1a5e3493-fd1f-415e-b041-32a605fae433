import React, { useState } from "react";
import PropTypes from "prop-types";
import { CreditCard, Plus, Settings } from "lucide-react";
import UnifiedManagementModal from "./UnifiedManagementModal";

/**
 * Credit Management Modal that allows choosing between Set Credits and Top Up Credits
 */
const CreditManagementModal = ({
  isOpen,
  onClose,
  onSave,
  onTopUp,
  selectedEntity,
  entityType,
  value,
  setValue,
  isUpdating,
  StatusBadge,
}) => {
  const [selectedMode, setSelectedMode] = useState(null); // null, 'replace', or 'topup'

  // Reset mode when modal closes
  React.useEffect(() => {
    if (!isOpen) {
      setSelectedMode(null);
    }
  }, [isOpen]);

  // If a mode is selected, show the UnifiedManagementModal
  if (selectedMode) {
    return (
      <UnifiedManagementModal
        isOpen={isOpen}
        onClose={() => {
          setSelectedMode(null);
          onClose();
        }}
        onSave={onSave}
        onTopUp={onTopUp}
        selectedEntity={selectedEntity}
        entityType={entityType}
        value={value}
        setValue={setValue}
        isUpdating={isUpdating}
        StatusBadge={StatusBadge}
        mode={selectedMode}
      />
    );
  }

  // Show mode selection screen
  if (!isOpen || !selectedEntity) return null;

  const entityName = selectedEntity?.name || selectedEntity?.cand_name || "";
  const isEmployer = entityType === "employer";

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50 backdrop-blur-sm">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl max-w-md w-full mx-4 p-6">
        {/* Header */}
        <div className="text-center mb-6">
          <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-3 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
            <CreditCard className="w-8 h-8 text-white" />
          </div>
          <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
            Manage {isEmployer ? "Credits" : "Visume Limit"}
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Choose how you want to update {entityName}'s {isEmployer ? "credits" : "visume limit"}
          </p>
        </div>

        {/* Current Status */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
          <h3 className="font-medium text-gray-900 dark:text-white mb-2">Current Status</h3>
          {isEmployer ? (
            <div className="text-sm text-gray-600 dark:text-gray-400">
              <p>Available: {selectedEntity?.creditsLeft || 0} credits</p>
              <p>Total Limit: {selectedEntity?.totalCredits || selectedEntity?.creditLimit || 0} credits</p>
              <p>Used: {(selectedEntity?.totalCredits || selectedEntity?.creditLimit || 0) - (selectedEntity?.creditsLeft || 0)} credits</p>
            </div>
          ) : (
            <div className="text-sm text-gray-600 dark:text-gray-400">
              <p>Used: {selectedEntity?.visumesUsed || 0} visumes</p>
              <p>Total Limit: {selectedEntity?.allowedVisumes || selectedEntity?.visumeLimit || 1} visumes</p>
              <p>Available: {(selectedEntity?.allowedVisumes || selectedEntity?.visumeLimit || 1) - (selectedEntity?.visumesUsed || 0)} visumes</p>
            </div>
          )}
        </div>

        {/* Mode Selection Buttons */}
        <div className="space-y-3 mb-6">
          <button
            onClick={() => setSelectedMode('replace')}
            className="w-full p-4 border-2 border-blue-200 dark:border-blue-700 rounded-lg hover:border-blue-400 dark:hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200 text-left group"
          >
            <div className="flex items-center gap-3">
              <div className="bg-blue-100 dark:bg-blue-900 p-2 rounded-lg group-hover:bg-blue-200 dark:group-hover:bg-blue-800 transition-colors">
                <Settings className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white">
                  Set {isEmployer ? "Credit Limit" : "Visume Limit"}
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Replace current limit and reset usage history
                </p>
              </div>
            </div>
          </button>

          <button
            onClick={() => setSelectedMode('topup')}
            className="w-full p-4 border-2 border-green-200 dark:border-green-700 rounded-lg hover:border-green-400 dark:hover:border-green-500 hover:bg-green-50 dark:hover:bg-green-900/20 transition-all duration-200 text-left group"
          >
            <div className="flex items-center gap-3">
              <div className="bg-green-100 dark:bg-green-900 p-2 rounded-lg group-hover:bg-green-200 dark:group-hover:bg-green-800 transition-colors">
                <Plus className="w-5 h-5 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white">
                  Top Up {isEmployer ? "Credits" : "Visumes"}
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Add {isEmployer ? "credits" : "visumes"} to current limit without resetting usage
                </p>
              </div>
            </div>
          </button>
        </div>

        {/* Cancel Button */}
        <button
          onClick={onClose}
          className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg font-medium transition-colors dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-300"
        >
          Cancel
        </button>
      </div>
    </div>
  );
};

CreditManagementModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onSave: PropTypes.func.isRequired,
  onTopUp: PropTypes.func.isRequired,
  selectedEntity: PropTypes.object,
  entityType: PropTypes.oneOf(["candidate", "employer"]).isRequired,
  value: PropTypes.string.isRequired,
  setValue: PropTypes.func.isRequired,
  isUpdating: PropTypes.bool,
  StatusBadge: PropTypes.elementType,
};

CreditManagementModal.defaultProps = {
  isUpdating: false,
  StatusBadge: null,
};

export default CreditManagementModal;
