import React, { useState } from "react";
import Cookies from "js-cookie";
import toast from "react-hot-toast";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import {
  HiOutlineCloudUpload,
  HiChevronLeft,
  HiChevronRight,
  HiX,
} from "react-icons/hi";

import {
  fetchJobRoles,
  fetchSkills,
} from "../../candidate/components/CreateVR/createVRApi";

const JobDescriptionModal = ({ isOpen, onClose, onJobDescriptionChange }) => {
  const [jobRoles, setJobRoles] = useState([]);
  const [skillsList, setSkillsList] = useState([]);

  // Refs for dropdowns
  const roleDropdownRef = React.useRef(null);
  const locationDropdownRef = React.useRef(null);
  const skillsDropdownRef = React.useRef(null);

  // Hide dropdowns on outside click
  React.useEffect(() => {
    function handleClickOutside(event) {
      if (
        roleDropdownRef.current &&
        !roleDropdownRef.current.contains(event.target)
      ) {
        setFilteredRoles([]);
      }
      if (
        locationDropdownRef.current &&
        !locationDropdownRef.current.contains(event.target)
      ) {
        setFilteredLocations([]);
      }
      if (
        skillsDropdownRef.current &&
        !skillsDropdownRef.current.contains(event.target)
      ) {
        setFilteredSkills([]);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Fetch roles and skills on mount
  React.useEffect(() => {
    async function fetchData() {
      const roles = await fetchJobRoles();
      setJobRoles(roles || []);
      setFilteredRoles(roles || []);
      const skills = await fetchSkills();
      setSkillsList(skills || []);
      setFilteredSkills(skills || []);
    }
    fetchData();
    // eslint-disable-next-line
  }, []);

  const locations = ["Bangalore", "Delhi", "Mumbai", "Hyderabad"];
  const emp_id = Cookies.get("employerId");
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState(1);
  const [fileName, setFileName] = useState("");
  const [role, setRole] = useState("");
  const [filteredRoles, setFilteredRoles] = useState([]);
  const [location, setLocation] = useState("");
  const [filteredLocations, setFilteredLocations] = useState(locations);
  const [skills, setSkills] = useState([]);
  const [filteredSkills, setFilteredSkills] = useState([]);
  const [showSkillSuggestions, setShowSkillSuggestions] = useState(false);
  const [skillsInput, setSkillsInput] = useState("");
  const [experience, setExperience] = useState("");
  const [companyType, setCompanyType] = useState("");
  const [salary, setSalary] = useState("");
  const [uploadedUrl, setUploadedUrl] = useState("");

  const handleFileUpload = async (e) => {
    const file = e.target.files[0];
    if (file) {
      try {
        setLoading(true);

        // Create FormData and append the file
        const formData = new FormData();
        formData.append("job_description", file);

        // Make API call
        const response = await fetch(
          `${
            import.meta.env.VITE_APP_HOST
          }/api/v1/upload-job-description/${emp_id}`,
          {
            method: "POST",
            body: formData,
          }
        );

        if (!response.ok) {
          const errorData = await response.json();
          toast.error(errorData.message || "Failed to upload job description");
          setLoading(false);
          setStep(1);
          return;
        }

        const data = await response.json();
        if (data?.JobDescription) {
          setLocation(data.JobDescription.location?.[0] || "");
          setRole(data.JobDescription.role || "");

          // Only allow experience values that exist in the dropdown schema
          const allowedExperiences = ["0-1", "2-3", "4-5", "5+"];
          const extractedExperience = data.JobDescription.experience || "";
          setExperience(
            allowedExperiences.includes(extractedExperience)
              ? extractedExperience
              : ""
          );

          setUploadedUrl(data.filePath || "");

          // Prefill salary and companyType if available, else set starting value
          setSalary(data.JobDescription.salary || "0-5");
          setCompanyType(data.JobDescription.companyType || "startup");

          // Fetch top 5 skills for the extracted role using backend AI endpoint (POST /recommend-skills)
          let recommendedSkills = [];
          try {
            const extractedRole = data.JobDescription.role || "";
            const skillsResponse = await fetch(
              `${import.meta.env.VITE_APP_HOST}/api/v1/recommend-skills`,
              {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify({ jobRole: extractedRole }),
              }
            );
            if (skillsResponse.ok) {
              const skillsData = await skillsResponse.json();
              recommendedSkills = skillsData.skills?.slice(0, 5) || [];
            }
          } catch (err) {
            // fallback to extracted skills if API fails
            recommendedSkills = Array.isArray(data.JobDescription.skills)
              ? data.JobDescription.skills.slice(0, 5)
              : JSON.parse(data.JobDescription.skills || "[]").slice(0, 5);
          }
          setSkills(recommendedSkills);

          // Hide dropdowns after auto-fill
          setFilteredRoles([]);
          setFilteredLocations([]);
          setFilteredSkills([]);
          toast.success(data.message);
        } else {
          toast.error("Failed to extract necessary details from the file.");
        }
      } catch (err) {
        console.error(`Failed to upload job description: ${err}`);
        toast.error(
          err?.message ||
            "An error occurred while uploading the job description"
        );
      } finally {
        setLoading(false);
        setStep(2);
      }
    } else {
      toast.error("No file selected. Please choose a file to upload.");
    }
  };

  const handleSave = async () => {
    if (!role || !experience || !location || !skills.length) {
      toast.error("Please fill all fields and add at least one skill.");
      return;
    }
    // Debug: log values being sent
    console.log("Submitting JD:", {
      role,
      experience,
      location,
      skills,
      uploadedUrl,
      emp_id,
    });
    setLoading(true);
    const jobDescription = {
      role,
      experience,
      location,
      skills,
      companyType,
      salary,
    };
    try {
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/create-job-description`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            ...jobDescription,
            filePath: uploadedUrl,
            emp_id: emp_id,
          }),
        }
      );

      if (!response.ok) {
        const msg = await response.json();
        toast.error(msg.message);
        throw new Error(`HTTP error! status: ${msg.message}`);
      }

      const data = await response.json();
      toast.success(data.message || "Job Description created successfully.");
      setStep(1);
      setRole("");
      setExperience("");
      setLocation("");
      setCompanyType("");
      setSalary("");
      setSkills([]);
      setUploadedUrl("");
      setFileName("");
    } catch (err) {
      console.error(`Failed to upload job description: ${err}`);
      toast.error(
        err?.message || "An error occurred while uploading the job description"
      );
    } finally {
      setLoading(false);
      onClose();
      if (onJobDescriptionChange) onJobDescriptionChange();
    }
  };

  // State for all job descriptions
  const [jobDescriptions, setJobDescriptions] = useState([]);

  // Fetch all job descriptions for employer
  React.useEffect(() => {
    async function fetchJobDescriptions() {
      try {
        const response = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/job-description/${emp_id}`
        );
        if (response.ok) {
          const data = await response.json();
          setJobDescriptions(data.jobDescriptions || []);
        }
      } catch (err) {
        // ignore error
      }
    }
    if (isOpen) fetchJobDescriptions();
    // eslint-disable-next-line
  }, [isOpen]);

  // Delete job description handler (stub: assumes DELETE endpoint exists)
  const handleDeleteJobDescription = async () => {
    if (!createdJobDescription?._id) return;
    try {
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/job-description/${
          createdJobDescription._id
        }`,
        { method: "DELETE" }
      );
      if (response.ok) {
        toast.success("Job description deleted");
        setCreatedJobDescription(null);
      } else {
        toast.error("Failed to delete job description");
      }
    } catch {
      toast.error("Failed to delete job description");
    }
    if (onJobDescriptionChange) onJobDescriptionChange();
  };

  return (
    <div
      className={`bg-black/50 fixed inset-0 z-[9999] flex items-center justify-center backdrop-blur-sm ${
        isOpen ? "block" : "hidden"
      }`}
      style={{ margin: 0, padding: 0, backdropFilter: "blur(8px)", WebkitBackdropFilter: "blur(8px)" }}
      aria-labelledby="modal-title"
      role="dialog"
      aria-modal="true"
    >
      <div className="w-full max-w-3xl">
        <div className="flex max-h-[90vh] flex-col overflow-hidden rounded-xl border border-gray-200 bg-white shadow-2xl dark:border-gray-800 dark:bg-gray-900">
          {/* Header */}
          <div className="flex-shrink-0 bg-gradient-to-r from-blue-600 to-purple-600 px-4 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="rounded-lg bg-white/20 p-1.5">
                  <HiOutlineCloudUpload className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h2 className="text-lg font-semibold text-white">
                    {step === 1
                      ? "Upload Job Description"
                      : "Enter Job Details"}
                  </h2>
                  <p className="text-xs text-blue-100">Step {step} of 2</p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="rounded-lg p-1.5 text-white/80 transition-all duration-200 hover:bg-white/20 hover:text-white"
              >
                <HiX className="h-4 w-4" />
              </button>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="flex-shrink-0 border-b border-gray-200 bg-gray-50 px-4 py-2 dark:border-gray-700 dark:bg-gray-800">
            <div className="mb-1 flex items-center justify-between">
              <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                Progress
              </span>
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {Math.round((step / 2) * 100)}%
              </span>
            </div>
            <div className="h-1.5 w-full rounded-full bg-gray-200 dark:bg-gray-700">
              <div
                className="h-1.5 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 transition-all duration-500"
                style={{ width: `${(step / 2) * 100}%` }}
              ></div>
            </div>
          </div>

          <div className="flex-1 overflow-y-auto">
            <form className="p-4">
              {/* Step 1: Upload */}
              {step === 1 && (
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="mx-auto mb-3 flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/50 dark:to-purple-900/50">
                      <HiOutlineCloudUpload className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <h1 className="mb-1 text-xl font-bold text-gray-900 dark:text-white">
                      Upload Job Description
                    </h1>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Upload a job description file or fill details manually
                    </p>
                  </div>
                  <div className="flex w-full flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 p-6">
                    <input
                      type="file"
                      id="file-upload"
                      className="hidden"
                      accept=".pdf,.doc,.docx"
                      onChange={handleFileUpload}
                    />
                    <label
                      htmlFor="file-upload"
                      className="flex cursor-pointer flex-col items-center justify-center text-center text-gray-600 dark:text-gray-300"
                    >
                      <HiOutlineCloudUpload className="mb-2 text-3xl" />
                      <span className="font-semibold">
                        Drag and drop your job description file here, or
                      </span>
                      <span className="font-semibold text-brand-500">
                        Browse files
                      </span>
                    </label>
                    <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                      Supported formats: PDF, DOC, DOCX
                    </p>
                  </div>
                  {fileName && (
                    <div className="mt-4 text-sm text-gray-600 dark:text-gray-300">
                      Selected file: <strong>{fileName}</strong>
                    </div>
                  )}
                  <div className="mt-4">
                    <button
                      type="button"
                      className="w-full rounded-lg bg-gradient-to-r from-blue-600 to-purple-600 px-4 py-2 font-semibold text-white shadow-lg transition-all hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      onClick={() => setStep(2)}
                    >
                      Fill Details Manually
                    </button>
                  </div>
                </div>
              )}

              {/* Step 2: Manual Entry */}
              {step === 2 && (
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="mx-auto mb-3 flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-purple-100 to-pink-100 dark:from-purple-900/50 dark:to-pink-900/50">
                      <HiOutlineCloudUpload className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                    </div>
                    <h1 className="mb-1 text-xl font-bold text-gray-900 dark:text-white">
                      Enter Job Details
                    </h1>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Fill in the job description details below
                    </p>
                  </div>
                  {/* Role Input */}
                  <div className="relative mb-4">
                    <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Role
                    </label>
                    <input
                      type="text"
                      className="focus:border-transparent w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm text-gray-900 placeholder-gray-500 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:placeholder-gray-400"
                      placeholder="Enter the role"
                      value={role}
                      onChange={(e) => {
                        const input = e.target.value;
                        setRole(input);
                        setFilteredRoles(
                          jobRoles.filter((r) =>
                            r.toLowerCase().includes(input.toLowerCase())
                          )
                        );
                      }}
                    />
                    {role && filteredRoles.length > 0 && (
                      <ul
                        ref={roleDropdownRef}
                        className="absolute z-10 mt-1 max-h-32 w-full overflow-y-auto rounded-lg border border-gray-200 bg-white shadow-xl dark:border-gray-600 dark:bg-gray-800"
                      >
                        {filteredRoles.map((suggestedRole, index) => (
                          <li
                            key={index}
                            className="cursor-pointer border-b border-gray-100 px-3 py-2 text-sm text-gray-900 transition-colors last:border-b-0 hover:bg-blue-50 dark:border-gray-700 dark:text-white dark:hover:bg-blue-900/20"
                            onClick={() => {
                              setRole(suggestedRole);
                              setFilteredRoles([]);
                            }}
                          >
                            {suggestedRole}
                          </li>
                        ))}
                      </ul>
                    )}
                  </div>
                  {/* Experience Dropdown */}
                  <div className="mb-4">
                    <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Experience
                    </label>
                    <select
                      className="focus:border-transparent w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm text-gray-900 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800 dark:text-white"
                      value={experience}
                      onChange={(e) => setExperience(e.target.value)}
                    >
                      <option value="">Select experience level</option>
                      <option value="0-1">0-1 years (Fresher)</option>
                      <option value="2-3">2-3 years (Intermediate)</option>
                      <option value="4-5">4-5 years (Experienced)</option>
                      <option value="5+">5+ years (Senior)</option>
                    </select>
                  </div>
                  {/* Location Input */}
                  <div className="relative mb-4">
                    <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Location
                    </label>
                    <input
                      type="text"
                      className="focus:border-transparent w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm text-gray-900 placeholder-gray-500 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:placeholder-gray-400"
                      placeholder="Enter the location"
                      value={location}
                      onChange={(e) => {
                        const input = e.target.value;
                        setLocation(input);
                        setFilteredLocations(
                          locations.filter((loc) =>
                            loc.toLowerCase().includes(input.toLowerCase())
                          )
                        );
                      }}
                    />
                    {location && filteredLocations.length > 0 && (
                      <ul
                        ref={locationDropdownRef}
                        className="absolute z-10 mt-1 max-h-32 w-full overflow-y-auto rounded-lg border border-gray-200 bg-white shadow-xl dark:border-gray-600 dark:bg-gray-800"
                      >
                        {filteredLocations.map((suggestedLocation, index) => (
                          <li
                            key={index}
                            className="cursor-pointer border-b border-gray-100 px-3 py-2 text-sm text-gray-900 transition-colors last:border-b-0 hover:bg-blue-50 dark:border-gray-700 dark:text-white dark:hover:bg-blue-900/20"
                            onClick={() => {
                              setLocation(suggestedLocation);
                              setFilteredLocations([]);
                            }}
                          >
                            {suggestedLocation}
                          </li>
                        ))}
                      </ul>
                    )}
                  </div>
                  {/* Company Type Dropdown */}
                  <div className="mb-4">
                    <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Company Type
                    </label>
                    <select
                      className="focus:border-transparent w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm text-gray-900 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800 dark:text-white"
                      value={companyType}
                      onChange={(e) => setCompanyType(e.target.value)}
                    >
                      <option value="">Select company type</option>
                      <option value="mnc">MNC</option>
                      <option value="mid_range">Mid-Size</option>
                      <option value="startup">Startup</option>
                    </select>
                  </div>
                  {/* Salary Dropdown */}
                  <div className="mb-4">
                    <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Salary
                    </label>
                    <select
                      className="focus:border-transparent w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm text-gray-900 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800 dark:text-white"
                      value={salary}
                      onChange={(e) => setSalary(e.target.value)}
                    >
                      <option value="">Select salary range</option>
                      <option value="0-5">0-5 LPA</option>
                      <option value="5-10">5-10 LPA</option>
                      <option value="10-15">10-15 LPA</option>
                      <option value="15-20">15-20 LPA</option>
                      <option value="20+">20+ LPA</option>
                    </select>
                  </div>
                  {/* Skills Input */}
                  <div className="relative mb-4">
                    <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Skills
                    </label>
                    <input
                      type="text"
                      className="focus:border-transparent w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm text-gray-900 placeholder-gray-500 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:placeholder-gray-400"
                      placeholder="Type a skill"
                      value={skillsInput || ""}
                      onFocus={() => setShowSkillSuggestions(true)}
                      onBlur={() => setShowSkillSuggestions(false)}
                      onChange={(e) => {
                        const input = e.target.value;
                        setSkillsInput(input);
                        setFilteredSkills(
                          skillsList.filter((skill) =>
                            skill.toLowerCase().includes(input.toLowerCase())
                          )
                        );
                      }}
                      onKeyDown={(e) => {
                        if (
                          e.key === "Enter" &&
                          e.target.value.trim() !== "" &&
                          !skills.includes(e.target.value.trim())
                        ) {
                          setSkills([...skills, e.target.value.trim()]);
                          setSkillsInput("");
                          setShowSkillSuggestions(false);
                        }
                      }}
                    />
                    {showSkillSuggestions && filteredSkills.length > 0 && (
                      <ul
                        ref={skillsDropdownRef}
                        className="absolute z-10 mt-1 max-h-32 w-full overflow-y-auto rounded-lg border border-gray-200 bg-white shadow-xl dark:border-gray-600 dark:bg-gray-800"
                      >
                        {filteredSkills.map((suggestedSkill, index) => (
                          <li
                            key={index}
                            className="cursor-pointer border-b border-gray-100 px-3 py-2 text-sm text-gray-900 transition-colors last:border-b-0 hover:bg-blue-50 dark:border-gray-700 dark:text-white dark:hover:bg-blue-900/20"
                            onMouseDown={() => {
                              if (!skills.includes(suggestedSkill)) {
                                setSkills([...skills, suggestedSkill]);
                              }
                              setSkillsInput("");
                              setFilteredSkills([]);
                              setShowSkillSuggestions(false);
                            }}
                          >
                            {suggestedSkill}
                          </li>
                        ))}
                      </ul>
                    )}
                    <div className="mt-2 flex flex-wrap gap-2">
                      {skills.map((skill, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center gap-1 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 px-2 py-1 text-xs font-medium text-white"
                        >
                          {skill}
                          <button
                            type="button"
                            className="rounded-full p-0.5 transition-colors hover:bg-white/20"
                            onClick={() =>
                              setSkills(skills.filter((s) => s !== skill))
                            }
                          >
                            <HiX size={12} />
                          </button>
                        </span>
                      ))}
                    </div>
                  </div>
                  <div className="mt-4 flex justify-end gap-2">
                    <button
                      type="button"
                      className="rounded-lg border border-blue-200 bg-white px-3 py-2 text-sm font-medium text-blue-600 transition-all duration-200 hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      onClick={() => setStep(1)}
                    >
                      Previous
                    </button>
                    <button
                      type="button"
                      className="rounded-lg bg-gradient-to-r from-blue-600 to-purple-600 px-4 py-2 text-sm font-medium text-white transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      onClick={handleSave}
                    >
                      Create Job Description
                    </button>
                  </div>
                </div>
              )}
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default JobDescriptionModal;
