import React from "react";
import { useNavigate } from "react-router-dom";
import { ArrowLeft, Home, HelpCircle, Mic, Video } from "lucide-react";

const InterviewFlowHeader = ({
  showBackButton = true,
  title = "Video Interview",
  currentStep = "",
  onBack = null,
  // Interview-specific props
  isInterviewSection = false,
  currentIndex = null,
  totalQuestions = null,
  showAudioStatus = false,
  showVideoStatus = false,
}) => {
  const navigate = useNavigate();

  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      navigate(-1);
    }
  };

  const handleHome = () => {
    navigate("/candidate/dashboard");
  };

  return (
    <header className="sticky top-0 z-50 bg-white/80 backdrop-blur-xl border-b border-gray-100 shadow-sm">
      <div className="w-full px-6">
        <div className={`flex items-center justify-between ${isInterviewSection ? 'h-16' : 'h-14'}`}>
          {/* Left: Navigation */}
          <div className="flex items-center gap-3">
            {showBackButton && (
              <button
                onClick={handleBack}
                className="p-2 rounded-xl hover:bg-gray-100 transition-all duration-200"
                title="Go back"
              >
                <ArrowLeft className="h-4 w-4 text-gray-700" />
              </button>
            )}
          </div>

          {/* Center: Logo and Title */}
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-3">
              <img
                src="/visume-logo-new.png"
                alt="Visume"
                className="h-8 w-8"
              />
              <span className="text-lg font-semibold text-gray-900">
                Visume
              </span>
            </div>
            
            {/* Section Title */}
            {title && (
              <div className="flex items-center gap-3">
                <div className="h-6 w-px bg-gray-200"></div>
                <h1 className="text-base font-medium text-gray-700">
                  {title}
                </h1>
              </div>
            )}
          </div>

          {/* Right: Interview Status or Help */}
          <div className="flex items-center gap-3">
            {isInterviewSection ? (
              <>
                {/* Question Progress */}
                {currentIndex !== null && totalQuestions !== null && (
                  <div className="flex items-center gap-3">
                    <div className="bg-gray-100 rounded-full px-3 py-1">
                      <span className="text-sm font-medium text-gray-700">
                        {currentIndex + 1} of {totalQuestions}
                      </span>
                    </div>
                    
                    {/* Status Indicators */}
                    <div className="flex items-center gap-2">
                      {showAudioStatus && (
                        <div className="flex items-center gap-1.5 bg-green-100 rounded-lg px-2 py-1">
                          <div className="relative">
                            <Mic className="w-3 h-3 text-green-600" />
                            <div className="absolute -top-0.5 -right-0.5 h-1.5 w-1.5 bg-green-500 rounded-full animate-pulse"></div>
                          </div>
                          <span className="text-xs font-medium text-green-700">
                            MIC
                          </span>
                        </div>
                      )}
                      {showVideoStatus && (
                        <div className="flex items-center gap-1.5 bg-blue-100 rounded-lg px-2 py-1">
                          <div className="relative">
                            <Video className="w-3 h-3 text-blue-600" />
                            <div className="absolute -top-0.5 -right-0.5 h-1.5 w-1.5 bg-blue-500 rounded-full animate-pulse"></div>
                          </div>
                          <span className="text-xs font-medium text-blue-700">
                            CAM
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </>
            ) : (
              <button
                className="p-2 rounded-xl hover:bg-gray-100 transition-all duration-200"
                title="Help & Support"
              >
                <HelpCircle className="h-4 w-4 text-gray-600" />
              </button>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default InterviewFlowHeader;