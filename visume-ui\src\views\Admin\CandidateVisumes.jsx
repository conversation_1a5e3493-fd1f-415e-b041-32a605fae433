import React, { useState, useEffect } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { ArrowLeft, Video, User, Calendar, MapPin, CheckCircle, XCircle, List } from "lucide-react";
import { ProfileCard } from "../employer/ProfilesUI";
import ProfileSkelLoader from "../employer/components/ProfileSkelLoader";
import avatar from "assets/img/avatars/avatar4.png";
import toast from "react-hot-toast";

const CandidateVisumes = () => {
  const { cand_id } = useParams();
  const navigate = useNavigate();
  const [candidate, setCandidate] = useState(null);
  const [visumes, setVisumes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [openTooltipId, setOpenTooltipId] = useState(null);
  const [activeTab, setActiveTab] = useState('active'); // 'active', 'deleted', 'all'

  // API base URL
  const API_BASE_URL = import.meta.env.VITE_APP_HOST;

  useEffect(() => {
    const fetchCandidateAndVisumes = async () => {
      setLoading(true);
      try {
        // Fetch candidate details first
        const candidateResponse = await fetch(`${API_BASE_URL}/api/v1/admin/candidates`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!candidateResponse.ok) {
          throw new Error(`Failed to fetch candidates: ${candidateResponse.status}`);
        }

        const candidateData = await candidateResponse.json();
        const foundCandidate = candidateData.data.candidates.find(c => c.cand_id === cand_id);
        
        if (!foundCandidate) {
          throw new Error('Candidate not found');
        }

        setCandidate(foundCandidate);

        // Fetch candidate's visumes (including soft-deleted for admin view)
        const visumesResponse = await fetch(`${API_BASE_URL}/api/v1/admin/visumes?cand_id=${cand_id}&include_deleted=true`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!visumesResponse.ok) {
          throw new Error(`Failed to fetch visumes: ${visumesResponse.status}`);
        }

        const visumesData = await visumesResponse.json();

        // Transform visumes data to match ProfileCard expected format
        const transformedVisumes = visumesData.data.visumes.map(visume => ({
          id: visume.id,
          video_profile_id: visume.video_profile_id,
          cand_id: visume.cand_id,
          role: visume.role,
          skills: visume.skills,
          experience_range: visume.experience_range || 'N/A',
          score: visume.score || Math.floor(Math.random() * 100), // Fallback score
          candidateDetails: [{
            cand_name: foundCandidate.name,
            profile_picture: foundCandidate.profile_picture,
            email: foundCandidate.email,
            location: foundCandidate.location || 'Location not specified',
            // Add the required preferred_location field that ProfileCard expects
            preferred_location: foundCandidate.location || 'Location not specified'
          }],
          created_at: visume.created_at,
          status: visume.status || 'completed',
          is_deleted: visume.is_deleted,
          deleted_at: visume.deleted_at,
          deleted_by: visume.deleted_by
        }));

        setVisumes(transformedVisumes);
      } catch (error) {
        console.error("Error fetching candidate visumes:", error);
        toast.error("Failed to load candidate visumes: " + error.message);
      } finally {
        setLoading(false);
      }
    };

    if (cand_id) {
      fetchCandidateAndVisumes();
    }
  }, [cand_id, API_BASE_URL]);

  // Restore a soft-deleted visume
  const handleRestoreVisume = async (videoProfileId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/v1/admin/visume/${videoProfileId}/restore`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        toast.success("Visume restored successfully");
        // Refresh the visumes list
        window.location.reload();
      } else {
        const errorData = await response.json();
        toast.error("Failed to restore visume: " + (errorData.message || 'Unknown error'));
      }
    } catch (error) {
      console.error("Error restoring visume:", error);
      toast.error("Failed to restore visume: " + error.message);
    }
  };

  // Filter visumes based on active tab
  const getFilteredVisumes = () => {
    switch (activeTab) {
      case 'active':
        return visumes.filter(v => !v.is_deleted);
      case 'deleted':
        return visumes.filter(v => v.is_deleted);
      case 'all':
      default:
        return visumes;
    }
  };

  // Tab configuration
  const tabs = [
    {
      id: 'active',
      label: 'Active Visumes',
      icon: CheckCircle,
      color: 'text-green-600 dark:text-green-400',
      bgColor: 'bg-green-50 dark:bg-green-900/20',
      borderColor: 'border-green-200 dark:border-green-700',
      count: visumes.filter(v => !v.is_deleted).length
    },
    {
      id: 'deleted',
      label: 'Deleted Visumes',
      icon: XCircle,
      color: 'text-red-600 dark:text-red-400',
      bgColor: 'bg-red-50 dark:bg-red-900/20',
      borderColor: 'border-red-200 dark:border-red-700',
      count: visumes.filter(v => v.is_deleted).length
    },
    {
      id: 'all',
      label: 'All Visumes',
      icon: List,
      color: 'text-blue-600 dark:text-blue-400',
      bgColor: 'bg-blue-50 dark:bg-blue-900/20',
      borderColor: 'border-blue-200 dark:border-blue-700',
      count: visumes.length
    }
  ];

  const handleGoBack = () => {
    navigate('/admin/candidateProfiles');
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Dummy shortlist handler for ProfileCard compatibility
  const handleShortlist = (video_profile_id) => {
    // Admin doesn't shortlist, but ProfileCard expects this function
    console.log('Admin viewing visume:', video_profile_id);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Header */}
        <div className="mb-5">
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-1">
                <button
                  onClick={handleGoBack}
                  className="flex items-center gap-1 px-2 py-1 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-all duration-200"
                >
                  <ArrowLeft className="w-5 h-5" />
                  Back to Candidates
                </button>
              </div>
            </div>

            {/* Candidate Info Summary */}
            {candidate && (
              <div className="mt-4 p-2 bg-gray-50 dark:bg-gray-700 rounded-xl">
                <div className="flex items-center gap-4">
                  <img
                    src={
                      candidate.profile_picture
                        ? candidate.profile_picture.startsWith("http")
                          ? candidate.profile_picture
                          : `${API_BASE_URL}/${candidate.profile_picture}`
                        : avatar
                    }
                    alt={candidate.name}
                    className="w-16 h-16 rounded-full object-cover border-4 border-white dark:border-gray-600 shadow-md"
                    onError={(e) => {
                      if (e.target.src !== avatar) {
                        e.target.onerror = null;
                        e.target.src = avatar;
                      }
                    }}
                  />
                  <div className="flex-1">
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                      {candidate.name}
                    </h3>
                    <div className="flex items-center gap-4 mt-1 text-sm text-gray-600 dark:text-gray-400">
                      <span className="flex items-center gap-1">
                        <User className="w-4 h-4" />
                        {candidate.email}
                      </span>
                      <span className="flex items-center gap-1">
                        <MapPin className="w-4 h-4" />
                        {candidate.location || 'Location not specified'}
                      </span>
                      <span className="flex items-center gap-1">
                        <Calendar className="w-4 h-4" />
                        Joined {formatDate(candidate.joinDate)}
                      </span>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                      {visumes.length}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      Total Visumes
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Tab Navigation */}
        {!loading && visumes.length > 0 && (
          <div className="mb-6 bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
            <div className="flex flex-col sm:flex-row">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                const isActive = activeTab === tab.id;

                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`
                      flex-1 px-6 py-4 text-left transition-all duration-200 border-b-2 sm:border-b-0 sm:border-r-2 last:border-r-0
                      ${isActive
                        ? `${tab.bgColor} ${tab.borderColor} ${tab.color}`
                        : 'bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-600'
                      }
                    `}
                  >
                    <div className="flex items-center gap-3">
                      <Icon className="w-5 h-5" />
                      <div>
                        <div className="font-medium text-sm">
                          {tab.label}
                        </div>
                        <div className={`text-2xl font-bold ${isActive ? tab.color : 'text-gray-500 dark:text-gray-400'}`}>
                          {tab.count}
                        </div>
                      </div>
                    </div>
                  </button>
                );
              })}
            </div>
          </div>
        )}

        {/* Visumes Grid */}
        <div className="mb-6">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {loading ? 'Loading visumes...' : (() => {
              const filteredVisumes = getFilteredVisumes();
              const activeTabLabel = tabs.find(tab => tab.id === activeTab)?.label || 'Visumes';
              return `Showing ${filteredVisumes.length} ${activeTabLabel.toLowerCase()}`;
            })()}
          </p>
        </div>

        {loading ? (
          <div className="grid gap-6 grid-cols-[repeat(auto-fill,minmax(320px,1fr))]">
            {[...Array(8)].map((_, index) => (
              <ProfileSkelLoader key={index} />
            ))}
          </div>
        ) : visumes.length > 0 ? (
          (() => {
            const filteredVisumes = getFilteredVisumes();
            return filteredVisumes.length > 0 ? (
              <div className="grid gap-6 grid-cols-[repeat(auto-fill,minmax(320px,1fr))]">
                {filteredVisumes.map((visume) => (
                <div key={visume.id || visume.video_profile_id} className="relative">
                  {/* Soft Delete Indicator */}
                  {visume.is_deleted && (
                    <div className="absolute top-2 left-2 z-10 bg-red-500 text-white px-2 py-1 rounded-md text-xs font-medium">
                      Deleted
                    </div>
                  )}

                  {/* Profile Card with conditional styling */}
                  <div className={`${visume.is_deleted ? 'opacity-60 border-2 border-red-200' : ''}`}>
                    <ProfileCard
                      keyValue={visume.id}
                      experience_range={visume.experience_range}
                      score={visume.score}
                      video_profile_id={visume.video_profile_id}
                      candidateDetails={visume.candidateDetails}
                      role={visume.role}
                      id={visume.id}
                      onShortlist={() => handleShortlist(visume.video_profile_id)}
                      isShortlisted={false} // Admin doesn't shortlist
                      isLoading={false}
                      cand_id={visume.cand_id}
                      skills={visume.skills}
                      openTooltipId={openTooltipId}
                      setOpenTooltipId={setOpenTooltipId}
                    />
                  </div>

                  {/* Restore Button for Soft Deleted Visumes */}
                  {visume.is_deleted && (
                    <div className="absolute bottom-2 right-2 z-10">
                      <button
                        onClick={() => handleRestoreVisume(visume.video_profile_id)}
                        className="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded-md text-xs font-medium transition-colors"
                        title={`Deleted on ${new Date(visume.deleted_at).toLocaleDateString()}`}
                      >
                        Restore
                      </button>
                    </div>
                  )}
                </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="bg-gray-100 dark:bg-gray-700 rounded-full p-6 w-24 h-24 mx-auto mb-4 flex items-center justify-center">
                  <Video className="w-12 h-12 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  {activeTab === 'active' ? 'No Active Visumes' :
                   activeTab === 'deleted' ? 'No Deleted Visumes' : 'No Visumes Found'}
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  {activeTab === 'active' ? 'This candidate has no active visumes.' :
                   activeTab === 'deleted' ? 'This candidate has no deleted visumes.' :
                   'This candidate hasn\'t created any visumes yet.'}
                </p>
              </div>
            );
          })()
        ) : (
          <div className="text-center py-12">
            <div className="bg-gray-100 dark:bg-gray-700 rounded-full p-6 w-24 h-24 mx-auto mb-4 flex items-center justify-center">
              <Video className="w-12 h-12 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No Visumes Found
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              This candidate hasn't created any visumes yet.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default CandidateVisumes;
