/**
 * R2UploadService - Simple direct upload service for Cloudflare R2
 * 
 * This replaces the complex multipart upload with a single direct upload
 * using presigned URLs from the R2-compatible S3 API
 */

/**
 * Upload video file directly to Cloudflare R2
 * 
 * @param {File} file - The video file to upload
 * @param {string} filename - The filename to use for storage
 * @param {Object} options - Upload options
 * @param {Function} options.onProgress - Progress callback
 * @param {Function} options.onError - Error callback
 * @param {Function} options.setLoadingText - Loading text setter
 * @returns {Promise<string>} - Final video URL
 */
export async function uploadVideoToR2(file, filename, options = {}) {
  const {
    onProgress = () => {},
    onError = () => {},
    setLoadingText = () => {}
  } = options;

  console.log('R2UploadService: Starting direct upload', {
    fileSize: file.size,
    fileName: filename,
    contentType: file.type
  });

  const maxRetries = 3;
  let lastError = null;

  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      setLoadingText(`Preparing upload... (${attempt + 1}/${maxRetries})`);

      // Step 1: Get presigned URL from backend
      console.log(`Getting R2 presigned URL (attempt ${attempt + 1}/${maxRetries})`);
      const host = import.meta.env.VITE_APP_HOST || "https://api.zoomjobs.in";
      
      const urlResponse = await fetch(
        `${host}/api/v1/r2-upload-url?filename=${encodeURIComponent(filename)}&contentType=${encodeURIComponent(file.type)}`,
        {
          method: 'GET',
          headers: {
            Accept: "application/json",
            "Content-Type": "application/json",
          },
          credentials: "include",
        }
      );

      if (!urlResponse.ok) {
        const errorData = await urlResponse.json();
        throw new Error(`Failed to get R2 upload URL: ${urlResponse.status} ${urlResponse.statusText}. Details: ${errorData.error || errorData.details || 'Unknown error'}`);
      }

      const { url, key, publicUrl } = await urlResponse.json();
      console.log(`Got R2 presigned URL (attempt ${attempt + 1})`, { key });

      // Step 2: Upload file directly to R2
      setLoadingText(`Uploading to R2... (${attempt + 1}/${maxRetries})`);
      console.log(`Uploading file to R2 (attempt ${attempt + 1}/${maxRetries})`);
      
      // Create XMLHttpRequest for better progress tracking
      const uploadPromise = new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        
        // Track upload progress
        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            const percentage = (event.loaded / event.total) * 100;
            const progressData = {
              percentage,
              uploadedBytes: event.loaded,
              totalBytes: event.total,
              completedParts: event.loaded === event.total ? 1 : 0,
              totalParts: 1
            };
            
            setLoadingText(`Uploading: ${Math.round(percentage)}%`);
            onProgress(progressData);
          }
        });

        xhr.addEventListener('load', () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            resolve(xhr);
          } else {
            reject(new Error(`Upload failed: ${xhr.status} ${xhr.statusText}. Response: ${xhr.responseText}`));
          }
        });

        xhr.addEventListener('error', () => {
          reject(new Error(`Network error during upload: ${xhr.statusText}`));
        });

        xhr.addEventListener('timeout', () => {
          reject(new Error('Upload timeout'));
        });

        // Configure request
        xhr.open('PUT', url);
        xhr.setRequestHeader('Content-Type', file.type);
        xhr.timeout = 300000; // 5 minutes timeout
        
        // Start upload
        xhr.send(file);
      });

      await uploadPromise;

      // Upload successful
      console.log(`R2 upload successful on attempt ${attempt + 1}`);
      
      // Final progress update
      onProgress({
        percentage: 100,
        uploadedBytes: file.size,
        totalBytes: file.size,
        completedParts: 1,
        totalParts: 1
      });
      
      setLoadingText("Upload completed successfully!");
      
      // Return the public URL for the uploaded file
      return publicUrl;

    } catch (error) {
      console.error(`R2 upload attempt ${attempt + 1} failed:`, error);
      lastError = error;

      if (attempt < maxRetries - 1) {
        const delay = 1000 * Math.pow(2, attempt); // Exponential backoff
        console.log(`Retrying in ${delay}ms...`);
        setLoadingText(`Upload failed, retrying in ${Math.round(delay/1000)}s...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  // All attempts failed
  setLoadingText("Upload failed after multiple attempts");
  onError(lastError);
  throw lastError;
}

/**
 * Validate video file before upload
 */
export function validateVideoFile(file) {
  const errors = [];
  
  if (!file) {
    errors.push('No file provided');
    return { valid: false, errors };
  }
  
  if (file.size === 0) {
    errors.push('File is empty');
  }
  
  if (!file.type || !file.type.startsWith('video/')) {
    errors.push('File must be a video');
  }
  
  // Accept both webm and mp4 formats
  const supportedFormats = ['video/webm', 'video/mp4'];
  if (!supportedFormats.some(format => file.type.includes(format.split('/')[1]))) {
    errors.push('File must be in webm or mp4 format');
  }
  
  // Check for reasonable file size limits (e.g., 2GB max)
  const maxSize = 2 * 1024 * 1024 * 1024; // 2GB
  if (file.size > maxSize) {
    errors.push(`File too large. Maximum size is ${Math.round(maxSize / (1024 * 1024 * 1024))}GB`);
  }
  
  return {
    valid: errors.length === 0,
    errors,
    info: {
      method: 'direct R2 upload',
      fileSize: file.size,
      fileSizeMB: Math.round(file.size / (1024 * 1024) * 100) / 100,
      contentType: file.type
    }
  };
}

/**
 * Test R2 connectivity
 */
export async function testR2Connection() {
  try {
    const host = import.meta.env.VITE_APP_HOST || "https://api.zoomjobs.in";
    
    const response = await fetch(`${host}/api/v1/r2/health`, {
      method: 'GET',
      headers: {
        Accept: "application/json",
      },
      credentials: "include",
    });

    const data = await response.json();
    
    return {
      success: response.ok,
      status: response.status,
      data
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

export default {
  uploadVideoToR2,
  validateVideoFile,
  testR2Connection
};