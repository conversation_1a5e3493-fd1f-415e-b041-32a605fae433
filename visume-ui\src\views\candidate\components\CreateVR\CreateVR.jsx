import React, { useState, useRef, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import toast from "react-hot-toast";
import {
  X,
  Video,
} from "lucide-react";
import ResumeSelection from "./ResumeSelection";
import JobRoleSkills from "./JobRoleSkills";
import ExperienceLevel from "./ExperienceLevel";
import CompanyType from "./CompanyType";
import JobPreference from "./JobPreference";
import Cookies from "js-cookie";
import TermsAndConditionsSection from "../TermsAndConditionsSection";
import MembershipLimitModal from "../MembershipLimitModal";
import { validateVisumeCreation } from "../../../../services/membershipService";

const CreateVR = ({ togglePopupVR, inviteData = null }) => {
  const navigate = useNavigate();
  const [step, setStep] = useState(0); // Start with step 0 for resume selection

  const candId = Cookies.get("candId"); // Retrieve the candId from the cookie

  // State for existing video resumes
  const [existingResumes, setExistingResumes] = useState([]);
  const [selectedResumeId, setSelectedResumeId] = useState("");
  const [isLoadingResumes, setIsLoadingResumes] = useState(false);

  // State for resume data
  const [resumeData, setResumeData] = useState(null);
  const [showResumePreview, setShowResumePreview] = useState(false);
  const [completeResumeData, setCompleteResumeData] = useState(null);
  const [hasAutoSelected, setHasAutoSelected] = useState(false);

  // Add state for uploading new resume
  const [isUploadingResume, setIsUploadingResume] = useState(false);

  // 🎯 MEMBERSHIP STATE: Handle membership validation and limit modal
  const [showMembershipModal, setShowMembershipModal] = useState(false);
  const [membershipStatus, setMembershipStatus] = useState(null);
  const [isValidatingMembership, setIsValidatingMembership] = useState(false);

  const saveqnscookie = (data) => {
    try {
      if (
        !data ||
        !Array.isArray(data.questions) ||
        data.questions.length === 0
      ) {
        throw new Error("Invalid data format: missing or empty questions");
      }
      // Reduce cookie size by storing only necessary info
      const cookieData = JSON.stringify({
        questions: data.questions,
        videoProfileId: data.videoProfileId,
      });
      Cookies.set("CreateVRres", cookieData, {
        expires: 7,
        sameSite: "Strict",
      }); // Expires in 7 days, strict mode
      // Verify the data was saved correctly
      const savedData = Cookies.get("CreateVRres");
      if (!savedData) {
        throw new Error("Failed to save questions to cookie");
      }
    } catch (err) {
      console.error("Error saving to cookie:", err);
      // Do not throw, just log error to avoid breaking flow
    }
  };

  // Check for invite data or pending invite data
  const getInviteData = () => {
    if (inviteData) {
      return inviteData;
    }
    // Check localStorage for pending invite data (after sign-in/sign-up)
    const pendingData = localStorage.getItem("pendingInviteData");
    if (pendingData) {
      try {
        const parsed = JSON.parse(pendingData);
        // Clear it after use
        localStorage.removeItem("pendingInviteData");
        return parsed;
      } catch (e) {
        console.error("Error parsing pending invite data:", e);
      }
    }
    return null;
  };

  const inviteDataToUse = getInviteData();

  const [formData, setFormData] = useState({
    candId: candId,
    jobRole: inviteDataToUse?.jobRole || "",
    skills: inviteDataToUse?.skills || [],
    companyType: Array.isArray(inviteDataToUse?.companyType)
      ? inviteDataToUse.companyType
      : inviteDataToUse?.companyType
      ? [inviteDataToUse.companyType]
      : [],
    jobPreference: Array.isArray(inviteDataToUse?.jobPreference)
      ? inviteDataToUse.jobPreference
      : [],
    experience: inviteDataToUse?.experience || "",
    salary: {
      current: "",
      expected: "",
    },
  });

  // Flag to prevent blur event from triggering skill selection when suggestion is clicked
  const [isSuggestionClicked, setIsSuggestionClicked] = useState(false);

  // Flag to track if job role was set from resume extraction (should not trigger auto-skill selection)
  const [isJobRoleFromResume, setIsJobRoleFromResume] = useState(false);

  // Ref to track suggestion click state more reliably across re-renders
  const suggestionClickRef = useRef(false);

  // Loading states for skill selection processes
  const [isAutoSelectingSkills, setIsAutoSelectingSkills] = useState(false);
  const [skillSelectionMessage, setSkillSelectionMessage] = useState("");
  const [skillInput, setSkillInput] = useState("");
  const [jobRoleInput, setJobRoleInput] = useState(
    inviteDataToUse?.jobRole || ""
  );
  const [jobRoleSuggestions, setJobRoleSuggestions] = useState([]);
  const [skillSuggestions, setSkillSuggestions] = useState([]);
  const [allDatabaseSkills, setAllDatabaseSkills] = useState([]); // Store original database skills
  const [showJobRoleSuggestions, setShowJobRoleSuggestions] = useState(false);
  const [showSkillSuggestions, setShowSkillSuggestions] = useState(false);
  const formRef = useRef(null);
  const skillRef = useRef(null);
  const jobRef = useRef(null);
  const [error, setError] = useState(""); // Error state
  const [isLoading, setIsLoading] = useState(false); // Add loading state
  const [secondarySkillSuggestions, setSecondarySkillSuggestions] = useState(
    []
  ); 

  // Enhanced skills extraction function with primary skills prioritization
  const extractSkillsFromResumeData = (resumeData) => {
    if (!resumeData || !resumeData.skills) {
      return [];
    }

    // Check if resume has enhanced skills structure
    if (
      resumeData.skills.primary_skills &&
      Array.isArray(resumeData.skills.primary_skills)
    ) {
      // Use primary skills (top 5) as default selection
      return resumeData.skills.primary_skills.slice(0, 5);
    }

    // Backward compatibility: if skills is still an array
    if (Array.isArray(resumeData.skills)) {
      return resumeData.skills.slice(0, 5); // Limit to 5 for consistency
    }

    // Fallback: try to get all_skills for backward compatibility
    if (
      resumeData.skills.all_skills &&
      Array.isArray(resumeData.skills.all_skills)
    ) {
      return resumeData.skills.all_skills.slice(0, 5);
    }

    return [];
  };

  // Extract secondary skills for suggestions
  const extractSecondarySkillsFromResumeData = (resumeData) => {
    if (!resumeData || !resumeData.skills) {
      return [];
    }

    // Get secondary skills if available
    if (
      resumeData.skills.secondary_skills &&
      Array.isArray(resumeData.skills.secondary_skills)
    ) {
      return resumeData.skills.secondary_skills;
    }

    // Fallback: get remaining skills from all_skills that aren't in primary
    if (
      resumeData.skills.all_skills &&
      Array.isArray(resumeData.skills.all_skills) &&
      resumeData.skills.primary_skills &&
      Array.isArray(resumeData.skills.primary_skills)
    ) {
      return resumeData.skills.all_skills.filter(
        (skill) => !resumeData.skills.primary_skills.includes(skill)
      );
    }

    return [];
  };

  const fetchJobRoles = async () => {
    try {
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/get-roles`
      );
      const data = await response.json();

      if (data && Array.isArray(data.roles)) {
        return data.roles.map((role) => role.role_name);
      } else {
        console.warn("Roles data is not in the expected format.");
        return [];
      }
    } catch (error) {
      console.error("Error fetching roles:", error);
      return [];
    }
  };

  const fetchSkills = async () => {
    try {
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/get-skills`
      );
      const data = await response.json();

      if (data && Array.isArray(data.skills)) {
        return data.skills.map((skill) => skill.skill_name);
      } else {
        console.warn("Skills data is not in the expected format.");
        return [];
      }
    } catch (error) {
      console.error("Error fetching skills:", error);
      return [];
    }
  };

  const fetchJobRolesAndSkills = async () => {
    try {
      // Fetch roles and skills in parallel
      const [roleNames, skillNames] = await Promise.all([
        fetchJobRoles(),
        fetchSkills(),
      ]);

      // Update state with the fetched data
      setJobRoleSuggestions(roleNames || []);
      setAllDatabaseSkills(skillNames || []); // Store original database skills
      setSkillSuggestions(skillNames || []); // Initialize with database skills

      if (roleNames.length === 0) console.warn("No roles found.");
      if (skillNames.length === 0) console.warn("No skills found.");

      // Show error only if both fetches failed
      if (roleNames.length === 0 && skillNames.length === 0) {
        throw new Error("Failed to fetch both roles and skills");
      }
    } catch (error) {
      console.error("Fetch error:", error);
      toast.error("Failed to load roles and skills");
    }
  };

  // Fetch existing video resumes for the candidate
  const fetchExistingResumes = async () => {
    try {
      setIsLoadingResumes(true);
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/video-resume/${candId}`
      );

      if (response.ok) {
        const data = await response.json();
        setExistingResumes(data);
      } else {
        console.error("Failed to fetch existing resumes");
        setExistingResumes([]);
      }
    } catch (error) {
      console.error("Error fetching existing resumes:", error);
      setExistingResumes([]);
    } finally {
      setIsLoadingResumes(false);
    }
  };

  // Fetch candidate resume data
  const fetchResumeData = async () => {
    try {
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/candidate/${candId}`
      );

      if (response.ok) {
        const data = await response.json();
        const strippedResume = data.candidateProfile?.[0]?.stripped_resume;

        if (strippedResume) {
          setResumeData(strippedResume);
          setCompleteResumeData(strippedResume);

          // Auto-select the default resume option
          if (!hasAutoSelected) {
            setSelectedResumeId("default");
            setHasAutoSelected(true);
          }
        }
      } else {
        console.error("Failed to fetch resume data");
      }
    } catch (error) {
      console.error("Error fetching resume data:", error);
    }
  };

  useEffect(() => {
    fetchJobRolesAndSkills();
    fetchExistingResumes();
    fetchResumeData();
  }, [candId]);

  // Handle invite data initialization
  useEffect(() => {
    if (inviteDataToUse) {
      // Skip resume selection step and go directly to step 1
      setStep(1);
      // Mark as not coming from resume since it's from invite
      setIsJobRoleFromResume(false);
      console.log(
        "Invite data detected, skipping to step 1 with pre-filled data:",
        inviteDataToUse
      );
    }
  }, [inviteDataToUse]);

  // Close dropdowns when clicking outside
  useEffect(() => {
    function handleClickOutside(event) {
      // Job Role
      const jobInput = document.getElementById("jobRole");
      if (
        showJobRoleSuggestions &&
        jobRef.current &&
        !jobRef.current.contains(event.target) &&
        jobInput &&
        !jobInput.contains(event.target)
      ) {
        setShowJobRoleSuggestions(false);
      }
      // Skill
      const skillInput = document.getElementById("skill-input");
      if (
        showSkillSuggestions &&
        skillRef.current &&
        !skillRef.current.contains(event.target) &&
        skillInput &&
        !skillInput.contains(event.target)
      ) {
        setShowSkillSuggestions(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showJobRoleSuggestions, showSkillSuggestions]);

  // Handle Job Role input
  const handleJobRoleInput = (e) => {
    const value = e.target.value.trim().toLowerCase(); // Normalize input
    setJobRoleInput(e.target.value); // Update the raw input value
    setFormData({ ...formData, jobRole: e.target.value }); // Update form data

    // Reset flags when user starts typing manually
    if (isJobRoleFromResume) {
      setIsJobRoleFromResume(false);
    }
    // Also reset suggestion click flags when user types
    setIsSuggestionClicked(false);
    suggestionClickRef.current = false;
    // Reset the last auto-selected role to allow new selections
    lastAutoSelectedRoleRef.current = null;

    if (value === "") {
      // If the input is empty, show the full list of suggestions
      setShowJobRoleSuggestions(false);
      setJobRoleSuggestions([]);
    } else {
      // Filter from the original list of suggestions
      const filteredSuggestions = jobRoleSuggestions.filter((role) =>
        role.toLowerCase().includes(value)
      );

      setShowJobRoleSuggestions(filteredSuggestions.length > 0); // Show suggestions if there are matches
      setJobRoleSuggestions(filteredSuggestions); // Update suggestions list
      setShowSkillSuggestions(false); // Hide skill suggestions
    }
  };

  // Handle job role blur - trigger AI auto-selection when user finishes typing
  const handleJobRoleBlur = () => {
    console.log("Job role blur triggered", {
      jobRole: formData.jobRole,
      isSuggestionClicked,
      suggestionClickRef: suggestionClickRef.current,
      isJobRoleFromResume,
    });

    if (formData.jobRole && formData.jobRole.trim().length > 0) {
      // Trigger AI auto-selection after a short delay to allow for suggestion clicks
      setTimeout(() => {
        console.log("Blur timeout executed", {
          isSuggestionClicked,
          suggestionClickRef: suggestionClickRef.current,
          isJobRoleFromResume,
        });

        if (
          !isSuggestionClicked &&
          !suggestionClickRef.current &&
          !isJobRoleFromResume
        ) {
          console.log("Triggering skill selection from blur");
          autoSelectSkillsForJobRole(formData.jobRole);
        } else {
          console.log("Skipping skill selection from blur due to flags");
        }
        // Reset the flags after the delay
        setIsSuggestionClicked(false);
        suggestionClickRef.current = false;
        setIsJobRoleFromResume(false);
      }, 300);
    }
  };

  const handleJobRoleSuggestionClick = (suggestion) => {
    console.log("Suggestion clicked:", suggestion);

    // Set flags to prevent blur event from triggering skill selection
    setIsSuggestionClicked(true);
    suggestionClickRef.current = true;

    // Reset resume flag since this is a manual selection
    setIsJobRoleFromResume(false);

    setJobRoleInput(suggestion);
    setFormData({ ...formData, jobRole: suggestion });
    setShowJobRoleSuggestions(false);

    // Trigger AI auto-selection of skills for the selected job role
    console.log("Triggering skill selection from suggestion click");
    autoSelectSkillsForJobRole(suggestion);
  };

  // Handle Skill input - show only database skills in dropdown
  const handleSkillInput = (e) => {
    const value = e.target.value.trim().toLowerCase(); // Normalize input
    setSkillInput(e.target.value); // Update the raw input value

    // Don't show suggestions if at skill limit
    if (formData.skills.length >= 5) {
      setShowSkillSuggestions(false);
      return;
    }

    if (value === "") {
      // If the input is empty, hide suggestions
      setShowSkillSuggestions(false);
      setSkillSuggestions([]);
    } else {
      // Filter only from database skills, excluding already selected skills
      const filteredDatabaseSkills = allDatabaseSkills.filter(
        (skill) =>
          skill.toLowerCase().includes(value) &&
          !formData.skills.includes(skill) // Exclude already selected skills
      );

      setShowSkillSuggestions(filteredDatabaseSkills.length > 0);
      setSkillSuggestions(filteredDatabaseSkills); // Update suggestions with filtered database skills
      setShowJobRoleSuggestions(false); // Hide job role suggestions
    }
  };

  const handleSkillSuggestionClick = (suggestion) => {
    if (formData.skills.length >= 5) {
      toast.error(
        "Maximum of 5 skills allowed. Please remove a skill before adding a new one."
      );
      return;
    }
    setSkillInput(""); // Clear the input after selection
    setFormData({
      ...formData,
      skills: [...formData.skills, suggestion], // Add the selected skill
    });
    setShowSkillSuggestions(false); // Hide suggestions
  };

  // Enhanced addSkill function - unlimited skill selection
  const addSkill = (skill) => {
    if (formData.skills.length >= 5) {
      toast.error(
        "Maximum of 5 skills allowed. Please remove a skill before adding a new one."
      );
      return;
    }

    if (!formData.skills.includes(skill)) {
      setFormData({
        ...formData,
        skills: [...formData.skills, skill],
      });
      setSkillInput("");
      setShowSkillSuggestions(false);
    }
  };

  const removeSkill = (skillToRemove) => {
    setFormData({
      ...formData,
      skills: formData.skills.filter((skill) => skill !== skillToRemove),
    });
  };

  // Clear all selected skills function
  const clearAllSkills = () => {
    setFormData({
      ...formData,
      skills: [],
    });
    setSkillInput("");
    setShowSkillSuggestions(false);
    // Reset any AI-selected skill indicators
    setHasAutoSelected(false);
  };

  // Ref to track the last job role that had skills auto-selected to prevent duplicates
  const lastAutoSelectedRoleRef = useRef(null);

  // AI auto-selection of skills based on job role
  const autoSelectSkillsForJobRole = async (jobRole) => {
    if (!jobRole || jobRole.trim().length === 0) {
      return;
    }

    const trimmedJobRole = jobRole.trim();

    // Prevent duplicate calls for the same job role
    if (lastAutoSelectedRoleRef.current === trimmedJobRole) {
      console.log(`Skipping duplicate skill selection for: ${trimmedJobRole}`);
      return;
    }

    // Set loading state
    setIsAutoSelectingSkills(true);
    setSkillSelectionMessage(
      `Selecting relevant skills for ${trimmedJobRole}...`
    );

    try {
      console.log(`Auto-selecting skills for job role: ${trimmedJobRole}`);
      lastAutoSelectedRoleRef.current = trimmedJobRole;

      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/recommend-skills`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ jobRole: trimmedJobRole }),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (
        data.skills &&
        Array.isArray(data.skills) &&
        data.skills.length === 5
      ) {
        // Auto-select the 5 AI-recommended skills
        setFormData((prev) => ({
          ...prev,
          skills: [...data.skills], // Replace with AI-selected skills
        }));

        setHasAutoSelected(true);
        console.log("Auto-selected skills:", data.skills);
        // toast.success(`Auto-selected 5 relevant skills for ${trimmedJobRole}`);
      } else {
        console.warn("Invalid skills data received from API:", data);
        toast.error("Failed to auto-select skills. Please select manually.");
      }
    } catch (error) {
      console.error("Error auto-selecting skills:", error);
      // Reset the ref on error so user can retry
      lastAutoSelectedRoleRef.current = null;
      toast.error("Failed to auto-select skills. Please select manually.");
    } finally {
      // Clear loading state
      setIsAutoSelectingSkills(false);
      setSkillSelectionMessage("");
    }
  };

  const handleFinalSubmit = (e) => {
    e.preventDefault();
    setIsLoading(true); // Show the loader
    console.log("Form submitted:", formData);

    // Check if user selected an existing resume or default resume
    if (selectedResumeId && selectedResumeId !== "new") {
      if (selectedResumeId === "default") {
        // Use the complete resume data from uploaded resume
        const profileDataWithCompleteResume = {
          ...formData,
          completeResumeData: completeResumeData, // Include complete resume data for AI processing
        };

        console.log(
          "Using default resume with complete data:",
          profileDataWithCompleteResume
        );
        createVideoProfile(profileDataWithCompleteResume);
        return;
      } else {
        // Use existing video resume data
        const selectedResume = existingResumes.find(
          (resume) => resume.id.toString() === selectedResumeId
        );
        if (selectedResume) {
          const existingResumeData = {
            candId: candId,
            jobRole: selectedResume.role,
            skills: selectedResume.skills
              ? selectedResume.skills.split(", ")
              : [],
            companyType: selectedResume.job_type || "",
            experience: selectedResume.experience_range || "",
            salary: selectedResume.salary
              ? JSON.parse(selectedResume.salary)
              : { current: "", expected: "" },
          };

          console.log("Using existing video resume data:", existingResumeData);
          createVideoProfile(existingResumeData);
          return;
        }
      }
    }

    // Create new video profile with form data
    console.log("Creating new video profile with form data:", formData);
    // Ensure companyType is a string before submitting
    const submitData = {
      ...formData,
      companyType: Array.isArray(formData.companyType)
        ? formData.companyType.join(", ")
        : formData.companyType,
    };
    createVideoProfile(submitData);
  };

  const createVideoProfile = async (profileData) => {
    setIsValidatingMembership(true);

    try {
      const validationResult = await validateVisumeCreation(candId);

      if (!validationResult.success && validationResult.limitReached) {
        setMembershipStatus(validationResult.membershipStatus);
        setShowMembershipModal(true);
        setIsLoading(false);
        setIsValidatingMembership(false);
        return;
      }
    } catch (error) {
      console.error("Membership validation error:", error);
      // Continue with creation on validation error (fail-safe)
    }

    setIsValidatingMembership(false);

    const url = `${import.meta.env.VITE_APP_HOST}/api/v1/create-video-resume`;
    console.log("Post method to", url);

    // Prepare data for API call - include resumeData for requirements generation
    const apiPayload = { ...profileData };

    // Ensure companyType is a string for backend compatibility
    if (Array.isArray(apiPayload.companyType)) {
      apiPayload.companyType = apiPayload.companyType.join(", ");
    }

    // Include resume data in API payload for requirements generation
    if (profileData.completeResumeData) {
      apiPayload.resumeData = profileData.completeResumeData;
      localStorage.setItem(
        "completeResumeData",
        JSON.stringify(profileData.completeResumeData)
      );
      console.log(
        "Including resume data in API payload for requirements generation"
      );
    }

    delete apiPayload.completeResumeData;

    // Prevent duplicate video profile creation
    fetch(
      `${import.meta.env.VITE_APP_HOST}/api/v1/video-resume/${
        profileData.candId
      }`
    )
      .then((res) => (res.ok ? res.json() : []))
      .then((existingProfiles) => {
        const duplicate = existingProfiles.find(
          (p) =>
            p.role === apiPayload.jobRole &&
            p.skills === apiPayload.skills.join(", ")
        );
        if (duplicate) {
          toast.error(
            "A video profile with the same role and skills already exists."
          );
          setIsLoading(false);
          return;
        }
        // Proceed with creation if no duplicate
        fetch(url, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(apiPayload),
        })
          .then((response) => {
            const data = response.json().then((data) => {
              if (!response.ok) {
                toast.error(data.message);
                throw new Error(`HTTP error! status: ${response.status}`);
              }
              return data;
            });
            return data;
          })
          .then((data) => {
            console.log("API Response:", data);
            console.log("Questions from API:", data.questions);

            if (
              !data.questions ||
              !Array.isArray(data.questions) ||
              data.questions.length === 0
            ) {
              throw new Error("No questions received from API");
            }

            localStorage.setItem("formData", JSON.stringify(profileData));
            saveqnscookie(data);

            navigate(`/candidate/interview/${data.videoProfileId}`);
          })
          .catch((error) => {
            console.error("API Error:", error);

            let errorMessage = "Failed to generate interview questions. ";
            if (error.message === "No questions received from API") {
              errorMessage +=
                "Please try again or contact support if the issue persists.";
            } else if (error.response?.data?.message) {
              errorMessage += error.response.data.message;
            } else {
              errorMessage += error.message || "An unexpected error occurred.";
            }
            toast.error(errorMessage);
            setIsLoading(false);
          })
          .finally(() => {
            setIsLoading(false);
          });
      })
      .catch((error) => {
        console.error("Error checking for duplicate video profile:", error);
        setIsLoading(false);
      });
    console.log("Form validation completed:", formData);
  };

  // Handle resume selection
  const handleResumeSelection = (e) => {
    const selectedId = e.target.value;
    setSelectedResumeId(selectedId);

    if (selectedId === "new") {
      // User wants to create a new resume, proceed to step 1
      // Reset resume flag since this will be manual input
      setIsJobRoleFromResume(false);
      setStep(1);
    } else if (selectedId === "default") {
      // User selected the default uploaded resume
      if (completeResumeData) {
        // Set loading state for resume skill processing
        setIsAutoSelectingSkills(true);
        setSkillSelectionMessage("Processing resume skills...");

        // Use setTimeout to allow UI to update before processing
        setTimeout(() => {
          try {
            // Extract relevant data from the complete resume for video profile creation using enhanced extraction
            const primarySkills =
              extractSkillsFromResumeData(completeResumeData);
            const secondarySkills =
              extractSecondarySkillsFromResumeData(completeResumeData);
            const experience = completeResumeData.experience || [];
            const latestJob = experience.length > 0 ? experience[0] : null;

            // Set secondary skills for suggestions
            setSecondarySkillSuggestions(secondarySkills);

            setFormData({
              candId: candId,
              jobRole: "", // Keep job role empty for manual user input
              skills: primarySkills, // Use AI-selected top 5 skills
              companyType: "", // Will be filled in next steps
              experience: "", // Will be filled in next steps
              salary: { current: "", expected: "" }, // Will be filled in next steps
            });

            // Keep the input field empty for manual user input
            setJobRoleInput("");

            // Reset the resume flag since job role will be manually entered
            setIsJobRoleFromResume(false);

            console.log("Enhanced skills extraction:", {
              primary_skills: primarySkills,
              secondary_skills: secondarySkills,
              total_available: primarySkills.length + secondarySkills.length,
            });

            setStep(1);
          } catch (error) {
            console.error("Error processing resume skills:", error);
            toast.error(
              "Failed to process resume skills. Please select skills manually."
            );
            // Still proceed to step 1 even if skill extraction fails
            setStep(1);
          } finally {
            // Clear loading state
            setIsAutoSelectingSkills(false);
            setSkillSelectionMessage("");
          }
        }, 100); // Small delay to show loading state
      }
    } else if (selectedId) {
      // User selected an existing video resume, pre-populate form data
      const selectedResume = existingResumes.find(
        (resume) => resume.id.toString() === selectedId
      );
      if (selectedResume) {
        const existingJobRole = selectedResume.role;

        setFormData({
          candId: candId,
          jobRole: existingJobRole,
          skills: selectedResume.skills
            ? selectedResume.skills.split(", ")
            : [],
          companyType: selectedResume.job_type || "",
          experience: selectedResume.experience_range || "",
          salary: selectedResume.salary
            ? JSON.parse(selectedResume.salary)
            : { current: "", expected: "" },
        });

        // Synchronize the input field with the existing resume job role
        setJobRoleInput(existingJobRole);

        // Mark this job role as coming from existing resume (should not trigger auto-skill selection)
        setIsJobRoleFromResume(true);

        // Skip to final step for confirmation
        setStep(4);
      }
    }
    setError("");
  };

  const handleNextStep = (e) => {
    e.preventDefault();
    // Validate based on current step
    if (step === 0) {
      if (!selectedResumeId) {
        setError("Please select a resume or choose to create a new one.");
        return;
      }
      // Handle the selection based on the selected option
      if (selectedResumeId === "new") {
        setStep(1);
      } else if (selectedResumeId === "default") {
        // Use the default resume data
        handleResumeSelection({ target: { value: "default" } });
      } else {
        // Use existing video resume
        handleResumeSelection({ target: { value: selectedResumeId } });
      }
      return;
    } else if (step === 1) {
      if (
        !jobRoleInput ||
        jobRoleInput.trim() === "" ||
        formData.skills.length === 0
      ) {
        setError(
          "Please fill the required Job Role and add at least one skill."
        );
        // Focus the Job Role input if empty
        if (!jobRoleInput || jobRoleInput.trim() === "") {
          const jobRoleInputEl = document.getElementById("jobRole");
          if (jobRoleInputEl) jobRoleInputEl.focus();
        }
        return;
      }
    } else if (step === 2) {
      if (!formData.companyType || formData.companyType.length === 0) {
        setError("Please select at least one company type.");
        return;
      }
    } else if (step === 3) {
      if (!formData.jobPreference || formData.jobPreference.length === 0) {
        setError("Please select at least one job preference.");
        return;
      }
    } else if (step === 4) {
      if (!formData.experience) {
        setError("Please select your experience level.");
        return;
      }
      if (
        (formData.experience === "2-3" || formData.experience === "3-5") &&
        (!formData.salary.current || !formData.salary.expected)
      ) {
        setError("Please enter both current and expected salary.");
        return;
      }
    }
    setError(""); // Clear the error if validation passes
    if (step < 5) {
      setStep(step + 1);
    }
  };

  const handlePrevStep = () => {
    if (step > 0) {
      setStep(step - 1);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, checked } = e.target;

    if (name === "currentSalary") {
      setFormData((prevState) => ({
        ...prevState,
        salary: {
          ...prevState.salary,
          current: value,
        },
      }));
    } else if (name === "expectedSalary") {
      setFormData((prevState) => ({
        ...prevState,
        salary: {
          ...prevState.salary,
          expected: value,
        },
      }));
    } else if (name === "companyType") {
      setFormData((prevState) => {
        let updated = [...prevState.companyType];
        if (checked) {
          if (!updated.includes(value)) updated.push(value);
        } else {
          updated = updated.filter((type) => type !== value);
        }
        return { ...prevState, companyType: updated };
      });
    } else if (name === "jobPreference") {
      setFormData((prevState) => {
        let updated = [...(prevState.jobPreference || [])];
        if (checked) {
          if (!updated.includes(value)) updated.push(value);
        } else {
          updated = updated.filter((type) => type !== value);
        }
        return { ...prevState, jobPreference: updated };
      });
    } else {
      setFormData({ ...formData, [name]: value });
    }
  };

  const salaryOptions = [
    { value: "0-5", label: "0-5 LPA" },
    { value: "5-10", label: "5-10 LPA" },
    { value: "10-15", label: "10-15 LPA" },
    { value: "15-20", label: "15-20 LPA" },
    { value: "20+", label: "20+ LPA" },
  ];

  // Handler for uploading new resume
  const handleNewResumeUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;
    setIsUploadingResume(true);
    try {
      const formData = new FormData();
      formData.append("resume", file);
      formData.append("cand_id", candId);
      // Use the original backend endpoint for uploading resume
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/upload-resume`,
        {
          method: "POST",
          body: formData,
          credentials: "include",
        }
      );
      if (!response.ok) throw new Error("Failed to upload resume");
      toast.success("Resume uploaded and replaced successfully!");
      // Refresh resume data
      await fetchResumeData();
      setSelectedResumeId("default");
    } catch (err) {
      toast.error("Failed to upload new resume");
      console.error(err);
    } finally {
      setIsUploadingResume(false);
    }
  };

  return (
    <>
      <div className="bg-black/50 fixed inset-0 z-[9999] flex items-center justify-center overflow-y-auto p-2 backdrop-blur-sm">
        <div className="my-4 w-full max-w-3xl">
          <div
            ref={formRef}
            className="flex max-h-[95vh] flex-col overflow-hidden rounded-xl border border-gray-200 bg-white shadow-2xl dark:border-gray-800 dark:bg-gray-900"
          >
            {/* Header */}
            <div className="flex-shrink-0 border-b border-gray-100 bg-white px-6 py-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-50 border border-blue-100">
                    <Video className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <h2 className="text-xl font-semibold text-gray-900">
                      Create Visume
                    </h2>
                  </div>
                </div>
                <button
                  onClick={togglePopupVR}
                  className="flex h-8 w-8 items-center justify-center rounded-lg border border-gray-200 text-gray-400 transition-colors hover:bg-gray-50 hover:text-gray-600"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            </div>

            {/* Progress Bar */}
            <div className="flex-shrink-0 border-b border-gray-100 bg-gray-50 px-6 py-3">
              <div className="mb-2 flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">
                  Step {step + 1} of 6
                </span>
                <span className="text-sm font-medium text-blue-600">
                  {Math.round((step / 5) * 100)}%
                </span>
              </div>
              <div className="h-2 w-full rounded-full bg-gray-200">
                <div
                  className="h-2 rounded-full bg-blue-600 transition-all duration-500 ease-out"
                  style={{ width: `${(step / 5) * 100}%` }}
                ></div>
              </div>
            </div>

            <div className="flex-1 overflow-y-auto">
              <form
                id="multistepForm"
                className="p-6"
                onSubmit={handleFinalSubmit}
              >
                {/* Error Message */}
                {error && (
                  <div className="mb-6 rounded-lg border border-red-200 bg-red-50 p-4">
                    <div className="flex items-center gap-3">
                      <div className="flex h-8 w-8 items-center justify-center rounded-full bg-red-100">
                        <X className="h-4 w-4 text-red-600" />
                      </div>
                      <p className="text-sm font-medium text-red-700">
                        {error}
                      </p>
                    </div>
                  </div>
                )}

                {/* Step 0 - Resume Selection */}
                {step === 0 && (
                  <ResumeSelection
                    resumeData={resumeData}
                    existingResumes={existingResumes}
                    selectedResumeId={selectedResumeId}
                    isLoadingResumes={isLoadingResumes}
                    isUploadingResume={isUploadingResume}
                    showResumePreview={showResumePreview}
                    setShowResumePreview={setShowResumePreview}
                    handleResumeSelection={handleResumeSelection}
                    handleNewResumeUpload={handleNewResumeUpload}
                    candId={candId}
                  />
                )}

                {/* Step 1 - Job Role and Skills */}
                {step === 1 && (
                  <JobRoleSkills
                    inviteDataToUse={inviteDataToUse}
                    jobRoleInput={jobRoleInput}
                    handleJobRoleInput={handleJobRoleInput}
                    setShowJobRoleSuggestions={setShowJobRoleSuggestions}
                    handleJobRoleBlur={handleJobRoleBlur}
                    isAutoSelectingSkills={isAutoSelectingSkills}
                    showJobRoleSuggestions={showJobRoleSuggestions}
                    jobRoleSuggestions={jobRoleSuggestions}
                    jobRef={jobRef}
                    handleJobRoleSuggestionClick={handleJobRoleSuggestionClick}
                    skillInput={skillInput}
                    handleSkillInput={handleSkillInput}
                    setShowSkillSuggestions={setShowSkillSuggestions}
                    showSkillSuggestions={showSkillSuggestions}
                    skillSuggestions={skillSuggestions}
                    skillRef={skillRef}
                    addSkill={addSkill}
                    formData={formData}
                    clearAllSkills={clearAllSkills}
                    skillSelectionMessage={skillSelectionMessage}
                    removeSkill={removeSkill}
                    completeResumeData={completeResumeData}
                    hasAutoSelected={hasAutoSelected}
                  />
                )}

                {/* Step 2 - Company Type */}
                {step === 2 && (
                  <CompanyType
                    inviteDataToUse={inviteDataToUse}
                    formData={formData}
                    handleInputChange={handleInputChange}
                    salaryOptions={salaryOptions}
                  />
                )}

                {/* Step 3 - Job Preference */}
                {step === 3 && (
                  <JobPreference
                    inviteDataToUse={inviteDataToUse}
                    formData={formData}
                    handleInputChange={handleInputChange}
                  />
                )}

                {/* Step 4 - Experience Level */}
                {step === 4 && (
                  <ExperienceLevel
                    inviteDataToUse={inviteDataToUse}
                    formData={formData}
                    handleInputChange={handleInputChange}
                    salaryOptions={salaryOptions}
                    setFormData={setFormData}
                  />
                )}

                {/* Step 5 - Terms and Conditions */}
                {step === 5 && (
                  <div>
                    <TermsAndConditionsSection
                      formData={formData}
                      isLoading={isLoading}
                      handleSubmit={handleFinalSubmit}
                    />
                  </div>
                )}

                {/* Navigation Buttons for steps 1-4 */}
                {step < 5 && (
                  <div className="mt-8 flex justify-between">
                    {step > 0 ? (
                      <button
                        type="button"
                        id="prevBtn"
                        className="rounded-lg border border-gray-300 bg-white px-4 py-2.5 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        onClick={handlePrevStep}
                      >
                        Previous
                      </button>
                    ) : (
                      <div></div>
                    )}
                    <button
                      type="button"
                      id="nextBtn"
                      disabled={
                        isAutoSelectingSkills ||
                        isUploadingResume ||
                        isValidatingMembership
                      }
                      className={`rounded-lg px-6 py-2.5 text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        isAutoSelectingSkills ||
                        isUploadingResume ||
                        isValidatingMembership
                          ? "cursor-not-allowed bg-gray-300 text-gray-500"
                          : "bg-blue-600 text-white hover:bg-blue-700"
                      }`}
                      onClick={handleNextStep}
                    >
                      {isAutoSelectingSkills ||
                      isUploadingResume ||
                      isValidatingMembership ? (
                        <div className="flex items-center gap-2">
                          <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-400 border-t-transparent"></div>
                          <span>
                            {isValidatingMembership
                              ? "Checking membership..."
                              : isUploadingResume
                              ? "Uploading Resume..."
                              : "Processing..."}
                          </span>
                        </div>
                      ) : step === 0 ? (
                        selectedResumeId === "default" ? (
                          "Continue with My Resume"
                        ) : selectedResumeId ? (
                          "Continue"
                        ) : (
                          "Select Resume"
                        )
                      ) : (
                        "Next"
                      )}
                    </button>
                  </div>
                )}
              </form>
            </div>
          </div>
        </div>
      </div>

      {/* MEMBERSHIP LIMIT MODAL: Show when candidate reaches Visume limit */}
      <MembershipLimitModal
        isOpen={showMembershipModal}
        onClose={() => setShowMembershipModal(false)}
        membershipStatus={membershipStatus}
        whatsappNumber="[WHATSAPP_NUMBER_PLACEHOLDER]"
      />
    </>
  );
};

export default CreateVR;
