import React, { useState } from "react";
import {
  Search,
  Filter,
  ChevronDown,
  ChevronUp,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  XCircle
} from "lucide-react";

// Design System Compliant Search Bar Component
const AdminSearchBar = ({
  searchTerm,
  setSearchTerm,
  placeholder = "Search...",
  onSearch,
  loading = false
}) => {
  return (
    <div className="relative flex-1 max-w-md">
      <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
        <Search className="h-5 w-5" style={{ color: 'var(--text-muted)' }} />
      </div>
      <input
        type="text"
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        onKeyPress={(e) => e.key === 'Enter' && onSearch && onSearch()}
        placeholder={placeholder}
        className="block w-full pl-12 pr-4 py-3 rounded-lg transition-all duration-200 font-manrope text-base"
        style={{
          background: 'var(--bg-tertiary)',
          border: '1px solid var(--bg-accent)',
          color: 'var(--text-primary)',
          fontFamily: 'Manrope, sans-serif'
        }}
        onFocus={(e) => {
          e.target.style.borderColor = 'var(--brand-primary)';
          e.target.style.boxShadow = '0 0 0 3px var(--focus)';
        }}
        onBlur={(e) => {
          e.target.style.borderColor = 'var(--bg-accent)';
          e.target.style.boxShadow = 'none';
        }}
      />
      {loading && (
        <div className="absolute inset-y-0 right-0 pr-4 flex items-center">
          <RefreshCw className="h-4 w-4 animate-spin" style={{ color: 'var(--text-muted)' }} />
        </div>
      )}
    </div>
  );
};

// Design System Compliant Status Badge Component
const StatusBadge = ({ status, type = "membership" }) => {
  const getStatusConfig = () => {
    if (type === "membership") {
      switch (status?.toLowerCase()) {
        case "active":
          return {
            backgroundColor: 'var(--success)',
            color: 'white',
            icon: CheckCircle
          };
        case "expired":
          return {
            backgroundColor: 'var(--error)',
            color: 'white',
            icon: XCircle
          };
        case "warning":
          return {
            backgroundColor: 'var(--warning)',
            color: 'white',
            icon: AlertCircle
          };
        default:
          return {
            backgroundColor: 'var(--bg-accent)',
            color: 'var(--text-secondary)',
            icon: AlertCircle
          };
      }
    } else if (type === "credits") {
      const credits = parseInt(status) || 0;
      if (credits > 5) {
        return {
          backgroundColor: 'var(--success)',
          color: 'white',
          icon: CheckCircle
        };
      } else if (credits > 0) {
        return {
          backgroundColor: 'var(--warning)',
          color: 'white',
          icon: AlertCircle
        };
      } else {
        return {
          backgroundColor: 'var(--error)',
          color: 'white',
          icon: XCircle
        };
      }
    }
  };

  const config = getStatusConfig();
  const Icon = config.icon;

  return (
    <span
      className="inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs font-medium font-manrope"
      style={{
        backgroundColor: config.backgroundColor,
        color: config.color,
        fontFamily: 'Manrope, sans-serif'
      }}
    >
      <Icon className="w-3 h-3" />
      {status}
    </span>
  );
};

// Design System Compliant Filter Dropdown Component
const FilterDropdown = ({
  label,
  options,
  value,
  onChange,
  icon: Icon
}) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="inline-flex items-center gap-2 px-4 py-3 rounded-lg transition-all duration-200 font-manrope text-base font-medium"
        style={{
          background: 'transparent',
          border: '1px solid var(--bg-tertiary)',
          color: 'var(--text-primary)',
          fontFamily: 'Manrope, sans-serif'
        }}
        onMouseEnter={(e) => {
          e.target.style.background = 'var(--hover)';
          e.target.style.borderColor = 'var(--brand-primary)';
        }}
        onMouseLeave={(e) => {
          e.target.style.background = 'transparent';
          e.target.style.borderColor = 'var(--bg-tertiary)';
        }}
      >
        {Icon && <Icon className="w-4 h-4" />}
        <span>{value || label}</span>
        {isOpen ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
      </button>

      {isOpen && (
        <div
          className="absolute top-full left-0 mt-1 w-48 rounded-lg shadow-lg z-50 py-1"
          style={{
            background: 'var(--bg-secondary)',
            border: '1px solid var(--bg-tertiary)'
          }}
        >
          <button
            onClick={() => {
              onChange("");
              setIsOpen(false);
            }}
            className="block w-full text-left px-4 py-2 text-sm font-manrope transition-colors duration-200"
            style={{
              color: 'var(--text-primary)',
              fontFamily: 'Manrope, sans-serif'
            }}
            onMouseEnter={(e) => e.target.style.background = 'var(--hover)'}
            onMouseLeave={(e) => e.target.style.background = 'transparent'}
          >
            All {label}
          </button>
          {options.map((option) => (
            <button
              key={option.value}
              onClick={() => {
                onChange(option.value);
                setIsOpen(false);
              }}
              className="block w-full text-left px-4 py-2 text-sm font-manrope transition-colors duration-200"
              style={{
                color: 'var(--text-primary)',
                fontFamily: 'Manrope, sans-serif'
              }}
              onMouseEnter={(e) => e.target.style.background = 'var(--hover)'}
              onMouseLeave={(e) => e.target.style.background = 'transparent'}
            >
              {option.label}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

// Design System Compliant Action Button Component
const ActionButton = ({
  onClick,
  variant = "primary",
  size = "sm",
  icon: Icon,
  children,
  loading = false,
  disabled = false
}) => {
  const getVariantStyles = () => {
    switch (variant) {
      case "primary":
        return {
          background: 'var(--brand-primary)',
          color: 'white',
          border: 'none',
          hoverBackground: '#2563EB'
        };
      case "secondary":
        return {
          background: 'transparent',
          color: 'var(--text-primary)',
          border: '1px solid var(--bg-tertiary)',
          hoverBackground: 'var(--hover)'
        };
      case "success":
        return {
          background: 'var(--success)',
          color: 'white',
          border: 'none',
          hoverBackground: '#059669'
        };
      case "warning":
        return {
          background: 'var(--warning)',
          color: 'white',
          border: 'none',
          hoverBackground: '#D97706'
        };
      case "danger":
        return {
          background: 'var(--error)',
          color: 'white',
          border: 'none',
          hoverBackground: '#DC2626'
        };
      default:
        return {
          background: 'var(--brand-primary)',
          color: 'white',
          border: 'none',
          hoverBackground: '#2563EB'
        };
    }
  };

  const getSizePadding = () => {
    switch (size) {
      case "xs":
        return "8px 16px";
      case "sm":
        return "12px 24px";
      case "md":
        return "16px 32px";
      case "lg":
        return "24px 48px";
      default:
        return "12px 24px";
    }
  };

  const getFontSize = () => {
    switch (size) {
      case "xs":
        return "12px";
      case "sm":
        return "14px";
      case "md":
        return "16px";
      case "lg":
        return "18px";
      default:
        return "14px";
    }
  };

  const styles = getVariantStyles();

  return (
    <button
      onClick={onClick}
      disabled={disabled || loading}
      className="inline-flex items-center gap-2 font-medium transition-all duration-200 cursor-pointer"
      style={{
        background: styles.background,
        color: styles.color,
        border: styles.border,
        borderRadius: size === 'xs' ? '8px' : '12px',
        padding: getSizePadding(),
        fontSize: getFontSize(),
        fontFamily: 'Manrope, sans-serif',
        opacity: disabled || loading ? 0.5 : 1,
        cursor: disabled || loading ? 'not-allowed' : 'pointer'
      }}
      onMouseEnter={(e) => {
        if (!disabled && !loading) {
          e.target.style.background = styles.hoverBackground;
          e.target.style.transform = 'translateY(-1px)';
        }
      }}
      onMouseLeave={(e) => {
        if (!disabled && !loading) {
          e.target.style.background = styles.background;
          e.target.style.transform = 'translateY(0)';
        }
      }}
    >
      {loading ? (
        <RefreshCw className="w-4 h-4 animate-spin" />
      ) : Icon ? (
        <Icon className="w-4 h-4" />
      ) : null}
      {children}
    </button>
  );
};

export { AdminSearchBar, StatusBadge, FilterDropdown, ActionButton };
