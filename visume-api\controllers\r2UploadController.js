const { S3Client, PutObjectCommand } = require('@aws-sdk/client-s3');
const { getSignedUrl } = require('@aws-sdk/s3-request-presigner');
const dotenv = require('dotenv');

dotenv.config();

// Initialize R2 client
let r2Client = null;

const initializeR2Client = () => {
  if (!process.env.CLOUDFLARE_ACCOUNT_ID || !process.env.R2_ACCESS_KEY_ID || !process.env.R2_SECRET_ACCESS_KEY || !process.env.R2_BUCKET_NAME) {
    console.error('Missing required R2 configuration. Please ensure these environment variables are set:');
    console.error('- CLOUDFLARE_ACCOUNT_ID');
    console.error('- R2_ACCESS_KEY_ID');
    console.error('- R2_SECRET_ACCESS_KEY');
    console.error('- R2_BUCKET_NAME');
    return false;
  }

  try {
    r2Client = new S3Client({
      region: 'auto', // Cloudflare R2 uses 'auto' region
      endpoint: `https://${process.env.CLOUDFLARE_ACCOUNT_ID}.r2.cloudflarestorage.com`,
      credentials: {
        accessKeyId: process.env.R2_ACCESS_KEY_ID,
        secretAccessKey: process.env.R2_SECRET_ACCESS_KEY,
      },
    });

    console.log('R2 client initialized successfully');
    return true;
  } catch (err) {
    console.error('Error initializing R2 client:', err);
    r2Client = null;
    return false;
  }
};

// Initialize R2 on module load
const initialized = initializeR2Client();
if (!initialized) {
  console.error('Failed to initialize R2 client. R2 operations will not be available.');
}

/**
 * Generate a presigned URL for direct upload to R2
 */
exports.generateUploadUrl = async function (req, res) {
  if (!r2Client) {
    return res.status(503).json({
      error: "R2 service unavailable",
      details: "R2 client is not initialized"
    });
  }

  console.log('Generating R2 upload URL:', req.query);
  
  try {
    const { filename, contentType = 'video/mp4' } = req.query;
    
    if (!filename) {
      return res.status(400).json({ 
        error: 'Filename is required',
        details: 'Please provide a filename parameter'
      });
    }

    // Sanitize filename to ensure it's safe for storage
    const safeName = filename.replace(/[^a-zA-Z0-9._-]/g, '_');
    const key = `videos/${Date.now()}-${safeName}`;

    const command = new PutObjectCommand({
      Bucket: process.env.R2_BUCKET_NAME,
      Key: key,
      ContentType: contentType,
    });

    // Generate presigned URL valid for 1 hour
    const url = await getSignedUrl(r2Client, command, { expiresIn: 3600 });

    // Generate public URL for the uploaded object using custom domain
    const publicUrl = `https://stream.visume.co.in/${key}`;

    console.log('R2 upload URL generated successfully:', {
      key,
      contentType,
      expiresIn: 3600
    });

    res.json({
      url,
      key,
      publicUrl,
      expiresIn: 3600
    });

  } catch (error) {
    console.error('Error generating R2 upload URL:', error);
    
    if (error.name === "CredentialsProviderError") {
      return res.status(503).json({
        error: "R2 service unavailable",
        details: "Invalid R2 credentials configuration"
      });
    }

    res.status(500).json({ 
      error: 'Failed to generate upload URL',
      details: error.message
    });
  }
};

/**
 * Health check endpoint to validate R2 connectivity
 */
exports.checkR2Health = async function (req, res) {
  console.log('[DEBUG] R2 health check requested');
  
  if (!r2Client) {
    return res.status(503).json({
      status: 'unhealthy',
      error: 'R2ClientNotInitialized',
      message: 'R2 client is not initialized',
      configuration: {
        accountId: process.env.CLOUDFLARE_ACCOUNT_ID ? '✓' : '✗',
        bucket: process.env.R2_BUCKET_NAME ? '✓' : '✗',
        accessKeyId: process.env.R2_ACCESS_KEY_ID ? '✓' : '✗',
        secretKey: process.env.R2_SECRET_ACCESS_KEY ? '✓' : '✗'
      }
    });
  }

  try {
    // Test credentials by attempting to generate a presigned URL
    const testCommand = new PutObjectCommand({
      Bucket: process.env.R2_BUCKET_NAME,
      Key: 'health-check-test',
      ContentType: 'text/plain',
    });

    await getSignedUrl(r2Client, testCommand, { expiresIn: 60 });
    
    res.status(200).json({
      status: 'healthy',
      message: 'R2 connection validated',
      configuration: {
        accountId: '✓',
        bucket: '✓',
        accessKeyId: '✓',
        secretKey: '✓'
      }
    });
  } catch (error) {
    console.error('R2 health check failed:', error);
    
    res.status(503).json({
      status: 'unhealthy',
      error: error.name,
      message: error.message,
      configuration: {
        accountId: process.env.CLOUDFLARE_ACCOUNT_ID ? '✓' : '✗',
        bucket: process.env.R2_BUCKET_NAME ? '✓' : '✗',
        accessKeyId: process.env.R2_ACCESS_KEY_ID ? '✓' : '✗',
        secretKey: process.env.R2_SECRET_ACCESS_KEY ? '✓' : '✗'
      }
    });
  }
};

/**
 * Get R2 client instance (for internal use if needed)
 */
exports.getR2Client = function () {
  return r2Client;
};

/**
 * Reinitialize R2 client (useful for configuration updates)
 */
exports.reinitializeR2Client = function () {
  return initializeR2Client();
};