import Cookies from 'js-cookie'
import React, { useEffect, useState } from 'react'

const ViewResume = () => {
  const candId = Cookies.get("candId")
  const [resume, setResume] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    const fetchResume = async () => {
      try {
        setLoading(true)
        const response = await fetch(`${import.meta.env.VITE_APP_HOST}/api/v1/candidate/${candId}`)
        const data = await response.json()

        const strippedResume = data.candidateProfile?.[0]?.stripped_resume
        if (!strippedResume) {
          throw new Error("Resume not available or still processing")
        }

        // Since backend already sends parsed data, no need to parse again
        const resumeData = typeof strippedResume === "string"
          ? JSON.parse(strippedResume)
          : strippedResume;
        setResume(resumeData)
        setError(null)
      } catch (error) {
        console.error('Error fetching resume:', error)
        setError(error.message)
        setResume(null)
      } finally {
        setLoading(false)
      }
    }
    fetchResume()
  }, [candId])

  if (loading) return <div className="p-5">Loading resume...</div>
  if (error) return <div className="p-5 text-red-500">{error}</div>
  if (!resume) return <div className="p-5">No resume data available</div>

  console.log("resume object:", resume);
  console.log("resume.skills value:", resume.skills);

  return (
    <div className="p-6 max-w-3xl mx-auto bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 rounded-xl shadow-sm">
      <div className="space-y-10">
        {/* Personal Information */}
        <section>
          <div className="flex items-center gap-4 mb-6">
            <div className="rounded-xl bg-gradient-to-br from-blue-600 to-purple-600 p-3 text-white">
              <svg className="h-6 w-6" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/></svg>
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Personal Information</h2>
              <p className="text-sm text-gray-500 dark:text-gray-400">Basic details</p>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-xl p-6 border border-blue-100 dark:border-blue-800/50">
            <div>
              <span className="font-semibold text-gray-700 dark:text-gray-300">Name:</span>
              <span className="ml-2 text-gray-900 dark:text-white">{resume.personal_info?.name}</span>
            </div>
            <div>
              <span className="font-semibold text-gray-700 dark:text-gray-300">Email:</span>
              <span className="ml-2 text-gray-900 dark:text-white">{resume.personal_info?.email}</span>
            </div>
            <div>
              <span className="font-semibold text-gray-700 dark:text-gray-300">Phone:</span>
              <span className="ml-2 text-gray-900 dark:text-white">{resume.personal_info?.phone}</span>
            </div>
            <div>
              <span className="font-semibold text-gray-700 dark:text-gray-300">Location:</span>
              <span className="ml-2 text-gray-900 dark:text-white">{resume.personal_info?.location}</span>
            </div>
          </div>
        </section>

        {/* Education */}
        <section>
          <div className="flex items-center gap-4 mb-6">
            <div className="rounded-xl bg-gradient-to-br from-green-600 to-emerald-600 p-3 text-white">
              <svg className="h-6 w-6" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path d="M12 20l9-5-9-5-9 5 9 5zm0-10l9-5-9-5-9 5 9 5z"/></svg>
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Education</h2>
              <p className="text-sm text-gray-500 dark:text-gray-400">Academic background</p>
            </div>
          </div>
          <div className="space-y-4">
            {resume.education?.map((edu, index) => (
              <div key={index} className="border-l-4 border-green-200 dark:border-green-700 pl-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm py-2">
                <p className="font-semibold text-green-700 dark:text-green-400">{edu.degree}</p>
                <p className="text-gray-700 dark:text-gray-300">{edu.institution}</p>
                <p className="text-gray-500 dark:text-gray-400">{edu.year}</p>
              </div>
            ))}
          </div>
        </section>

        {/* Experience */}
        <section>
          <div className="flex items-center gap-4 mb-6">
            <div className="rounded-xl bg-gradient-to-br from-yellow-500 to-orange-500 p-3 text-white">
              <svg className="h-6 w-6" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/><circle cx="8.5" cy="7" r="4"/><line x1="20" y1="8" x2="20" y2="14"/><line x1="23" y1="11" x2="17" y2="11"/></svg>
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Experience</h2>
              <p className="text-sm text-gray-500 dark:text-gray-400">Work history</p>
            </div>
          </div>
          <div className="space-y-6">
            {resume.experience?.map((exp, index) => (
              <div key={index} className="border-l-4 border-yellow-200 dark:border-yellow-700 pl-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm py-2">
                <p className="font-semibold text-yellow-700 dark:text-yellow-400">{exp.title}</p>
                <p className="text-gray-700 dark:text-gray-300">{exp.company}</p>
                <p className="text-gray-500 dark:text-gray-400">{exp.duration}</p>
                <ul className="list-disc ml-5 mt-2 text-gray-600 dark:text-gray-400">
                  {exp.responsibilities?.map((resp, i) => (
                    <li key={i}>{resp}</li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </section>

        {/* Skills */}
        <section>
          <div className="flex items-center gap-4 mb-6">
            <div className="rounded-xl bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/50 dark:to-purple-900/50 p-3">
              <svg className="h-6 w-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path d="M9.75 17L9 21l3-1.5L15 21l-.75-4M12 3v8m0 0l3.5 3.5M12 11l-3.5 3.5"/></svg>
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Skills</h2>
              <p className="text-sm text-gray-500 dark:text-gray-400">Technical & soft skills</p>
            </div>
          </div>
          <div className="flex flex-wrap gap-3">
            {(Array.isArray(resume.skills?.all_skills)
              ? resume.skills.all_skills
              : []
            ).map((skill, index) => (
              <span key={index} className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-full font-medium shadow-sm text-sm">
                {skill}
              </span>
            ))}
          </div>
        </section>

        {/* Projects */}
        {resume.projects?.length > 0 && (
          <section>
            <div className="flex items-center gap-4 mb-6">
              <div className="rounded-xl bg-gradient-to-br from-purple-600 to-pink-600 p-3 text-white">
                <svg className="h-6 w-6" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"/></svg>
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Projects</h2>
                <p className="text-sm text-gray-500 dark:text-gray-400">Portfolio & achievements</p>
              </div>
            </div>
            <div className="space-y-6">
              {resume.projects.map((project, index) => (
                <div key={index} className="border-l-4 border-purple-200 dark:border-purple-700 pl-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm py-2">
                  <p className="font-semibold text-purple-700 dark:text-purple-400">{project.title}</p>
                  <p className="text-gray-700 dark:text-gray-300 mt-2">{project.description}</p>
                  {project.technologies && (
                    <div className="flex flex-wrap gap-2 mt-3">
                      {project.technologies.map((tech, techIndex) => (
                        <span key={techIndex} className="bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 px-2 py-1 rounded-md text-xs font-medium">
                          {tech}
                        </span>
                      ))}
                    </div>
                  )}
                  {project.gitHubLink && (
                    <a 
                      href={project.gitHubLink} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="inline-flex items-center gap-1 mt-2 text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 text-sm font-medium"
                    >
                      <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 24 24">
                        <path fillRule="evenodd" d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z" clipRule="evenodd"/>
                      </svg>
                      GitHub
                    </a>
                  )}
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Certifications */}
        {resume.certifications?.length > 0 && (
          <section>
            <div className="flex items-center gap-4 mb-6">
              <div className="rounded-xl bg-gradient-to-br from-emerald-100 to-green-100 dark:from-emerald-900/50 dark:to-green-900/50 p-3">
                <svg className="h-6 w-6 text-emerald-600 dark:text-emerald-400" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg>
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Certifications</h2>
                <p className="text-sm text-gray-500 dark:text-gray-400">Achievements & credentials</p>
              </div>
            </div>
            <ul className="list-disc ml-8 text-gray-700 dark:text-gray-300">
              {resume.certifications.map((cert, index) => (
                <li key={index}>{cert}</li>
              ))}
            </ul>
          </section>
        )}
      </div>
    </div>
  )
}

export default ViewResume
