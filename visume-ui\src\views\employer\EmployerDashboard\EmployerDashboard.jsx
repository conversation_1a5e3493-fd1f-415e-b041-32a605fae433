import Cookies from "js-cookie";
import JobDescriptionList from "./JobDescriptionList";
import React, { useEffect, useState, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { HiOutlineSparkles, HiOutlineBriefcase, HiOutlineTrash, HiOutlinePlus } from "react-icons/hi";
import { Bot, MessageCircle, CreditCard,ArrowUpCircle } from "lucide-react";
import {
  Header,
  StatsOverview,
  JobList,
  ProfileSkelLoader,
  JobDescriptionModal,
} from "./index";
import StatCard from "../components/StatCard";
import PositionsCard from "../components/PositionsCard";
import ProfileCard from "../ProfilesUI/ProfileCard";
import toast from "react-hot-toast";
import avatar from "assets/img/avatars/avatar4.png";
import { getFormattedEmployerMembershipStatus } from "services/employerMembershipService";
import UniversalMembershipModal from "components/shared/UniversalMembershipModal";


const EmployerDashboard = () => {
  const jobDescListRef = useRef();
  const emp_id = Cookies.get("employerId");

  const handleJobDescriptionChange = () => {
    if (jobDescListRef.current && jobDescListRef.current.refresh) {
      jobDescListRef.current.refresh();
    }
  };
  const jstoken = Cookies.get("jstoken");
  
  const navigate = useNavigate();
  const [shortListedCandidatesCount, setShortListedCandidatesCount] =
    useState(0);
  const [unlockedCandidatesCount, setUnlockedCandidatesCount] = useState(0);
  const [InterviewedCandidatesCount, setInterviewedCandidatesCount] =
    useState(0);
  const [offeredCandidatesCount, setOfferedCandidatesCount] = useState(0);
  const [empData, setEmpData] = useState({
    name: "Default User1",
    plan_name: "PRO",
    creditsLeft: 100,
  });

  const [isModalOpen, setModalOpen] = useState(false);
  const [jobData, setJobData] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  // Job Description state
  const [dashboardJobDescription, setDashboardJobDescription] = useState(null);
  const [jdLoading, setJdLoading] = useState(false);
  // 🎯 EMPLOYER MEMBERSHIP: Modal state for credit limit handling
  const [showMembershipModal, setShowMembershipModal] = useState(false);
  const [membershipStatus, setMembershipStatus] = useState({});
  const handleShortlist = async (id) => {
    if (loadingId === id) return;
    setLoadingId(id);

    try {
      const emp_id = Cookies.get("employerId");
      if (!emp_id) {
        toast.error("You need to be an employer to shortlist profiles");
        return;
      }

      const response = await fetch(
        `${
          import.meta.env.VITE_APP_HOST
        }/api/v1/employer-profiles/shortlist-candidate`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ emp_id, cand_id: id }),
        }
      );

      if (!response.ok) {
        const msg = await response.json();
        toast.error(msg.message);
        throw new Error(`HTTP error! status: ${msg.message}`);
      }

      const data = await response.json();
      toast.success(data.message);
      // Remove the shortlisted profile from jobData
      setJobData((prev) =>
        prev.filter((profile) => profile.video_profile_id !== id)
      );
    } catch (error) {
      console.error("Error during shortlist operation:", error);
    } finally {
      setLoadingId(null);
    }
  };
  const [loadingId, setLoadingId] = useState(null);

  useEffect(() => {
    let isMounted = true;

    const filterMatchingProfiles = (profiles, jobDescription) => {
      if (!profiles || !jobDescription) return [];

      return profiles
        .filter((profile) => {
          // Check role match (case-insensitive)
          const roleMatches =
            profile.role.toLowerCase() === jobDescription.role.toLowerCase();

          // Check skills match
          const profileSkills = profile.skills
            .toLowerCase()
            .split(",")
            .map((s) => s.trim());
          const jdSkills = Array.isArray(jobDescription.skills)
            ? jobDescription.skills.map((s) => s.toLowerCase())
            : jobDescription.skills
                .toLowerCase()
                .split(",")
                .map((s) => s.trim());

          const skillsMatch = jdSkills.some((skill) =>
            profileSkills.includes(skill)
          );

          return roleMatches && skillsMatch;
        })
        .sort((a, b) => {
          // Sort by number of matching skills
          const aSkills = a.skills
            .toLowerCase()
            .split(",")
            .map((s) => s.trim());
          const bSkills = b.skills
            .toLowerCase()
            .split(",")
            .map((s) => s.trim());
          const jdSkills = Array.isArray(jobDescription.skills)
            ? jobDescription.skills.map((s) => s.toLowerCase())
            : jobDescription.skills
                .toLowerCase()
                .split(",")
                .map((s) => s.trim());

          const aMatches = jdSkills.filter((skill) =>
            aSkills.includes(skill)
          ).length;
          const bMatches = jdSkills.filter((skill) =>
            bSkills.includes(skill)
          ).length;

          return bMatches - aMatches;
        });
    };

    const getAllProfiles = async () => {
      try {
        setIsLoading(true);

        if (!dashboardJobDescription) {
          setJobData([]);
          setIsLoading(false);
          return;
        }

        const data = await fetch(
          `${
            import.meta.env.VITE_APP_HOST
          }/api/v1/getSuggestedCandidates?emp_id=${emp_id}`
        );
        let res = await data.json();

        if (isMounted && res.candidateProfiles?.length) {
          const matchingProfiles = filterMatchingProfiles(
            res.candidateProfiles,
            dashboardJobDescription
          );
          setJobData(matchingProfiles);
        } else {
          setJobData([]);
        }
        setIsLoading(false);
      } catch (err) {
        console.error(`Error fetching profiles:`, err);
        if (isMounted) {
          setIsLoading(false);
          setJobData([]);
        }
      }
    };

    if (dashboardJobDescription) {
      getAllProfiles();
    }

    return () => {
      isMounted = false;
    };
  }, [emp_id, dashboardJobDescription]);

  useEffect(() => {
    const fetchCandidates = async () => {
      try {
        const profileData = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/employerProfilesData`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              Authorization: emp_id,
            },
          }
        );
        const profileJson = await profileData.json();

        if (profileJson.data) {
          setShortListedCandidatesCount(
            profileJson?.data?.candidate_counts?.shortlisted_count || 0
          );
          setUnlockedCandidatesCount(
            profileJson?.data?.candidate_counts?.unlocked_count || 0
          );
          setInterviewedCandidatesCount(
            profileJson?.data?.candidate_counts?.interviewed_count || 0
          );
          setOfferedCandidatesCount(
            profileJson?.data?.candidate_counts?.offered_count || 0
          );
          setEmpData({
            name: profileJson?.data?.emp_name || "Default User",
            plan_name: profileJson?.data?.plan_name || "PRO",
            creditsLeft: profileJson?.data?.creditsLeft,
            profile_picture: profileJson?.data?.profile_picture || "",
            company_logo: profileJson?.data?.company_logo || "",
            company_name: profileJson?.data?.company_name || "",
          });
        }
      } catch (err) {
        console.error("Error fetching shortlisted profiles:", err);
      }
    };

    // Fetch job description for dashboard
    const fetchDashboardJobDescription = async () => {
      setJdLoading(true);
      try {
        const response = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/job-description/${emp_id}`
        );
        if (response.ok) {
          const data = await response.json();
          setDashboardJobDescription(
            Array.isArray(data.jobDescriptions)
              ? data.jobDescriptions[0] || null
              : data.jobDescription || null
          );
        } else if (response.status === 404) {
          // No job description found, do not log error
          setDashboardJobDescription(null);
        } else {
          // Only log unexpected errors
          console.error(
            "Failed to fetch job description:",
            response.statusText
          );
          setDashboardJobDescription(null);
        }
      } catch (err) {
        console.error("Failed to fetch job description:", err);
        setDashboardJobDescription(null);
      }
      setJdLoading(false);
    };

    fetchCandidates();
    fetchDashboardJobDescription();
  }, [emp_id, isModalOpen]);

  // Listen for company logo updates
  useEffect(() => {
    const handleLogoUpdate = (event) => {
      // Refresh employer data when logo is updated
      const fetchCandidates = async () => {
        try {
          const profileData = await fetch(
            `${import.meta.env.VITE_APP_HOST}/api/v1/employerProfilesData`,
            {
              method: "GET",
              headers: {
                "Content-Type": "application/json",
                Authorization: emp_id,
              },
            }
          );
          const profileJson = await profileData.json();

          if (profileJson.data) {
            setEmpData({
              name: profileJson?.data?.emp_name || "Default User",
              plan_name: profileJson?.data?.plan_name || "PRO",
              creditsLeft: profileJson?.data?.creditsLeft,
              profile_picture: profileJson?.data?.profile_picture || "",
              company_logo: profileJson?.data?.company_logo || "",
              company_name: profileJson?.data?.company_name || "",
            });
          }
        } catch (err) {
          console.error("Error fetching updated employer data:", err);
        }
      };

      fetchCandidates();
    };

    window.addEventListener('companyLogoUpdated', handleLogoUpdate);

    return () => {
      window.removeEventListener('companyLogoUpdated', handleLogoUpdate);
    };
  }, [emp_id]);

  // Delete handler for dashboard JD
  const handleDashboardDeleteJD = async () => {
    if (!dashboardJobDescription?._id) return;
    setJdLoading(true);
    try {
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/job-description/${
          dashboardJobDescription._id
        }`,
        { method: "DELETE" }
      );
      if (response.ok) {
        toast.success("Job description deleted");
        setDashboardJobDescription(null);
      } else {
        toast.error("Failed to delete job description");
      }
    } catch {
      toast.error("Failed to delete job description");
    }
    setJdLoading(false);
  };

  return (
    <>
      {jstoken ? (
        <div className="h-screen  dark:bg-gray-900 px-4 sm:px-6 lg:px-8 overflow-hidden">
          <div className="max-w-7xl mx-auto h-full flex flex-col space-y-4">
            {/* Header Section - Enhanced Modern Design */}
            <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 rounded-xl shadow-lg overflow-hidden flex-shrink-0">
              <div className="p-2 mx-4">
                <div className="flex flex-col xl:flex-row xl:items-center xl:justify-between gap-4">
                  {/* Left: Profile Section */}
                  <div className="flex items-center gap-4 p-3 ">
                    <div className="relative">
                      <div className="absolute rounded-full opacity-20 "></div>
                      <img
                        src={
                          empData?.company_logo
                            ? empData.company_logo.startsWith("http")
                                ? empData.company_logo
                                : `${import.meta.env.VITE_APP_HOST}/${empData.company_logo}`
                            : empData?.profile_picture
                            ? empData.profile_picture.startsWith("http")
                                ? empData.profile_picture
                                : `${import.meta.env.VITE_APP_HOST}/${empData.profile_picture}`
                            : avatar
                        }
                        alt={empData?.company_logo ? "Company Logo" : "User Avatar"}
                        className="relative h-14 w-14 rounded-full shadow-lg object-cover border-2 border-white dark:border-gray-700"
                        onError={(e) => {
                          if (e.target.src !== avatar) {
                            e.target.onerror = null;
                            e.target.src = avatar;
                          }
                        }}
                      />
                    </div>
                    <div className="space-y-2 flex-1">
                      <div className="space-y-1">
                        <h1 className="text-lg font-bold text-gray-900 dark:text-white font-['Sora',sans-serif] tracking-tight">
                          {empData?.name || "Loading..."}
                        </h1>
                        <div className="inline-flex items-center px-1 py-0.5 rounded-full text-[10px] font-medium bg-gradient-to-r from-blue-50 to-blue-100 text-blue-700 dark:from-blue-900/30 dark:to-blue-800/30 dark:text-blue-300 border border-blue-200 dark:border-blue-700">
                          {empData?.plan_name || "Free Employer Plan"}
                        </div>
                      </div>

                      {/* Enhanced credit display with usage bar */}
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-500 dark:text-gray-400 font-medium">Credits Used </span>
                          <span className="font-semibold text-gray-900 dark:text-white ml-2">
                            {Math.max(0, 10 - (empData?.creditsLeft || 0))} / 10
                          </span>
                        </div>

                        {/* Credit usage progress bar */}
         

                        <div className="flex items-center justify-between">
                          {/* Show upgrade button when credits are low */}
                          {(empData?.creditsLeft || 0) <= 2 && (
                            <button
                              onClick={() => {
                                const formattedStatus = getFormattedEmployerMembershipStatus({
                                  currentCredits: empData?.creditsLeft || 0,
                                  planName: empData?.plan_name || "Free Employer Plan",
                                  canUseCredits: (empData?.creditsLeft || 0) > 0,
                                  needsUpgrade: (empData?.creditsLeft || 0) <= 2
                                });
                                setMembershipStatus(formattedStatus);
                                setShowMembershipModal(true);
                              }}
                              className="inline-flex items-center gap-2 rounded-full bg-gradient-to-r from-green-600 to-emerald-600 px-3 py-1.5 text-sm font-medium text-white hover:from-green-700 hover:to-emerald-700 transform hover:scale-105 transition-all duration-200 shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                            >
                              <ArrowUpCircle className="w-3 h-3" />
                              Upgrade Plan
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Right: Enhanced Stats Cards */}
                  <div className="hidden xl:flex items-center gap-4">
                    <div
                      className="bg-white border border-gray-200 border-l-4 border-l-cyan-500 rounded-lg px-4 py-4 min-w-[130px] cursor-pointer transition-all duration-200 shadow-sm hover:shadow-md"
                      onClick={() => navigate("/employer/track-candidates?tab=Shortlisted")}
                    >
                      <div className="flex justify-between items-center">
                        <div className="flex-1">
                          <div className="text-sm font-medium text-gray-600 mb-2">Shortlisted</div>
                          <div className="text-2xl font-bold text-gray-900 font-['Sora',sans-serif]">{shortListedCandidatesCount}</div>
                        </div>
                        <div className="flex-shrink-0 ml-3 p-2 bg-cyan-100 rounded-lg flex items-center justify-center">
                          <svg className="w-4 h-4 text-cyan-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                          </svg>
                        </div>
                      </div>
                    </div>
                    <div
                      className="bg-white border border-gray-200 border-l-4 border-l-orange-500 rounded-lg px-4 py-4 min-w-[130px] cursor-pointer transition-all duration-200 shadow-sm hover:shadow-md"
                      onClick={() => navigate("/employer/track-candidates?tab=Unlocked")}
                    >
                      <div className="flex justify-between items-center">
                        <div className="flex-1">
                          <div className="text-sm font-medium text-gray-600 mb-2">Unlocked</div>
                          <div className="text-2xl font-bold text-gray-900 font-['Sora',sans-serif]">{unlockedCandidatesCount}</div>
                        </div>
                        <div className="flex-shrink-0 ml-3 p-2 bg-orange-100 rounded-lg flex items-center justify-center">
                          <svg className="w-4 h-4 text-orange-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                          </svg>
                        </div>
                      </div>
                    </div>
                    <div
                      className="bg-white border border-gray-200 border-l-4 border-l-green-500 rounded-lg px-4 py-4 min-w-[130px] cursor-pointer transition-all duration-200 shadow-sm hover:shadow-md"
                      onClick={() => navigate("/employer/track-candidates?tab=Interviews")}
                    >
                      <div className="flex justify-between items-center">
                        <div className="flex-1">
                          <div className="text-sm font-medium text-gray-600 mb-2">Interviews</div>
                          <div className="text-2xl font-bold text-gray-900 font-['Sora',sans-serif]">{InterviewedCandidatesCount}</div>
                        </div>
                        <div className="flex-shrink-0 ml-3 p-2 bg-green-100 rounded-lg flex items-center justify-center">
                          <svg className="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clipRule="evenodd" />
                          </svg>
                        </div>
                      </div>
                    </div>
                    <div
                      className="bg-white border border-gray-200 border-l-4 border-l-blue-500 rounded-lg px-4 py-4 min-w-[130px] cursor-pointer transition-all duration-200 shadow-sm hover:shadow-md"
                      onClick={() => navigate("/employer/track-candidates?tab=Offers")}
                    >
                      <div className="flex justify-between items-center">
                        <div className="flex-1">
                          <div className="text-sm font-medium text-gray-600 mb-2">Offers</div>
                          <div className="text-2xl font-bold text-gray-900 font-['Sora',sans-serif]">{offeredCandidatesCount}</div>
                        </div>
                        <div className="flex-shrink-0 ml-3 p-2 bg-blue-100 rounded-lg flex items-center justify-center">
                          <svg className="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Mobile & Tablet Stats */}
                <div className="xl:hidden mt-4 pt-4 border-t border-gray-100 dark:border-gray-800">
                  <div className="grid grid-cols-2 lg:grid-cols-4 gap-3">
                    <div
                      className="bg-white border border-gray-200 border-l-4 border-l-cyan-500 rounded-lg p-4 cursor-pointer transition-all duration-200 shadow-sm hover:shadow-md"
                      onClick={() => navigate("/employer/track-candidates?tab=Shortlisted")}
                    >
                      <div className="flex justify-between items-center">
                        <div className="flex-1">
                          <div className="text-xs font-medium text-gray-600 mb-1">Shortlisted</div>
                          <div className="text-xl font-bold text-gray-900 font-['Sora',sans-serif]">{shortListedCandidatesCount}</div>
                        </div>
                        <div className="flex-shrink-0 ml-2 p-1.5 bg-cyan-100 rounded-lg flex items-center justify-center">
                          <svg className="w-3 h-3 text-cyan-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                          </svg>
                        </div>
                      </div>
                    </div>
                    <div
                      className="bg-white border border-gray-200 border-l-4 border-l-orange-500 rounded-lg p-4 cursor-pointer transition-all duration-200 shadow-sm hover:shadow-md"
                      onClick={() => navigate("/employer/track-candidates?tab=Unlocked")}
                    >
                      <div className="flex justify-between items-center">
                        <div className="flex-1">
                          <div className="text-xs font-medium text-gray-600 mb-1">Unlocked</div>
                          <div className="text-xl font-bold text-gray-900 font-['Sora',sans-serif]">{unlockedCandidatesCount}</div>
                        </div>
                        <div className="flex-shrink-0 ml-2 p-1.5 bg-orange-100 rounded-lg flex items-center justify-center">
                          <svg className="w-3 h-3 text-orange-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                          </svg>
                        </div>
                      </div>
                    </div>
                    <div
                      className="bg-white border border-gray-200 border-l-4 border-l-green-500 rounded-lg p-4 cursor-pointer transition-all duration-200 shadow-sm hover:shadow-md"
                      onClick={() => navigate("/employer/track-candidates?tab=Interviews")}
                    >
                      <div className="flex justify-between items-center">
                        <div className="flex-1">
                          <div className="text-xs font-medium text-gray-600 mb-1">Interviews</div>
                          <div className="text-xl font-bold text-gray-900 font-['Sora',sans-serif]">{InterviewedCandidatesCount}</div>
                        </div>
                        <div className="flex-shrink-0 ml-2 p-1.5 bg-green-100 rounded-lg flex items-center justify-center">
                          <svg className="w-3 h-3 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clipRule="evenodd" />
                          </svg>
                        </div>
                      </div>
                    </div>
                    <div
                      className="bg-white border border-gray-200 border-l-4 border-l-blue-500 rounded-lg p-4 cursor-pointer transition-all duration-200 shadow-sm hover:shadow-md"
                      onClick={() => navigate("/employer/track-candidates?tab=Offers")}
                    >
                      <div className="flex justify-between items-center">
                        <div className="flex-1">
                          <div className="text-xs font-medium text-gray-600 mb-1">Offers</div>
                          <div className="text-xl font-bold text-gray-900 font-['Sora',sans-serif]">{offeredCandidatesCount}</div>
                        </div>
                        <div className="flex-shrink-0 ml-2 p-1.5 bg-blue-100 rounded-lg flex items-center justify-center">
                          <svg className="w-3 h-3 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* AI Candidate Matching Section - Compact */}
            <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 rounded-xl shadow-lg ">
              <div className="flex items-center gap-3 p-4 border-b border-gray-100 dark:border-gray-800 bg-gradient-to-r from-blue-50/50 to-purple-50/50 dark:from-blue-900/10 dark:to-purple-900/10">
                <span className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-xl">
                  <Bot className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                </span>
                <div>
                  <h2 className="text-base font-semibold text-gray-900 dark:text-white font-['Sora',sans-serif]">
                    AI Candidate Matching
                  </h2>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Click a job description to view matches.
                  </p>
                </div>
              </div>
              <div className="p-3 flex-1 overflow-hidden">
                <JobDescriptionList emp_id={emp_id} ref={jobDescListRef} onUploadClick={() => setModalOpen(true)} />
              </div>
            </div>

            <JobDescriptionModal
              isOpen={isModalOpen}
              onClose={() => setModalOpen(false)}
              onJobDescriptionChange={handleJobDescriptionChange}
            />
          </div>
        </div>
      ) : (
        <div className="flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-900 px-4">
          <div className="text-center bg-white dark:bg-gray-900 p-8 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-800 max-w-md w-full">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white font-['Sora',sans-serif] mb-3">
              Please sign in to continue
            </h2>
            <p className="text-sm text-gray-500 dark:text-gray-400 font-medium">
              You need to be authenticated to access the dashboard
            </p>
          </div>
        </div>
      )}

      {/* 🎯 EMPLOYER MEMBERSHIP: Universal membership modal for credit limits */}
      <UniversalMembershipModal
        isOpen={showMembershipModal}
        onClose={() => setShowMembershipModal(false)}
        userType="employer"
        membershipStatus={membershipStatus}
        whatsappNumber="[WHATSAPP_NUMBER_PLACEHOLDER]"
      />
    </>
  );
};

export default EmployerDashboard;