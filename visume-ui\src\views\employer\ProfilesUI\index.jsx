// index.jsx
import React, { useEffect, useState } from "react";
import { HiOutlineUserGroup, HiOutlineVideoCamera } from "react-icons/hi";
import Cookies from "js-cookie";
import ProfileSkelLoader from "../components/ProfileSkelLoader";
import { useNavigate } from "react-router-dom";
import toast from "react-hot-toast";
import ProfileCard from "./ProfileCard";
import SearchBar from "./SearchBar";

const ProfileSearchUI = () => {
  const navigate = useNavigate();
  const [pageSize] = useState(10);
  const [showLoadMore, setSetShowLoadMore] = useState(false);
  const [loading, setLoading] = useState(false);
  const [totalProfiles, setTotalProfiles] = useState(500);
  const [selectedProfiles, setSelectedProfiles] = useState(new Set());
  const [profiles, setProfiles] = useState([]);
  const [loadingId, setLoadingId] = useState(null);
  const [shortListedProfiles, setShortListedProfiles] = useState([]);
  const [oldProfiles, setOldProfiles] = useState([]);
  const [viewInteraction, setViewInteraction] = useState(0);

  const handleShortlist = async (id, cand_id) => {
    if (loadingId === id) return;
    setLoadingId(id);

    try {
      const emp_id = Cookies.get("employerId");
      if (!emp_id) {
        return toast.error("You need to be an employer to shortlist profiles");
      }

      const response = await fetch(
        `${
          import.meta.env.VITE_APP_HOST
        }/api/v1/employer-profiles/shortlist-candidate`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ emp_id, cand_id: id }),
        }
      );

      if (!response.ok) {
        const msg = await response.json();
        toast.error(msg.message);
        throw new Error(`HTTP error! status: ${msg.message}`);
      }

      const data = await response.json();
      toast((t) => (
        <span className="flex items-center">
          <div className="mr-[10px]">
            <i className="fas fa-check-circle text-[#28a745]"></i>
          </div>
          <div className="flex items-start justify-start gap-2">
            {data.message}
            <button
              onClick={() => {
                toast.dismiss(t.id);
                navigate("/employer/track-candidates");
              }}
              className="ml-[10px] cursor-pointer rounded-[4px] border-0 bg-[#28a745] px-2 py-1 text-[#fff]"
            >
              Shortlisted Candidates
            </button>
          </div>
        </span>
      ));
      setSelectedProfiles((prev) => {
        const newSelectedProfiles = new Set(prev);
        if (newSelectedProfiles.has(id)) {
          newSelectedProfiles.delete(id);
        } else {
          newSelectedProfiles.add(id);
        }
        return newSelectedProfiles;
      });
    } catch (error) {
      console.error("Error during shortlist operation:", error);
    } finally {
      setLoadingId(null);
    }
  };

  const getAllProfiles = async (pageLength) => {
    if (profiles.length < totalProfiles) {
      try {
        const dummyProfiles = Array.from({ length: 10 }, (_, index) => ({
          name: `Loading... ${index}`,
        }));

        setProfiles((prev) => [...prev, ...dummyProfiles]);

        const page = Math.ceil(profiles.length / 10);
        const data = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/getAllCandidates?page=${
            pageLength ? pageLength : page + 1
          }&pageSize=${pageSize}`
        );
        const res = await data.json();

        setTotalProfiles(res.pagination.totalCandidates);
        setProfiles((prev) => {
          const newProfiles = prev.slice(0, prev.length - 10);
          return [...newProfiles, ...res.candidateProfiles];
        });

        setSetShowLoadMore(true);
      } catch (err) {
        console.log(`Error`, err);
      }
    }
  };

  const getShortListedProfiles = async () => {
    try {
      const emp_id = Cookies.get("employerId");
      if (!emp_id) {
        toast.error("You are not an employeer");
        navigate("/");
        return;
      }
      fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/shortlisted-profiles/${emp_id}`
      )
        .then((res) => res.json())
        .then((data) => {
          if (data.data && data.data.length) {
            const shortIds = [];
            data.data.map((e) => shortIds.push(e.video_profile_id));
            setShortListedProfiles(shortIds);
          }
        });
    } catch (err) {
      console.log(`Error`, err);
    }
  };

  return (
    <section className="relative min-h-screen overflow-hidden bg-white dark:bg-gray-900">
      {/* Google Fonts Import */}
      <link
        href="https://fonts.googleapis.com/css2?family=Sora:wght@400;600;700&family=Manrope:wght@400;500;600;700&display=swap"
        rel="stylesheet"
      />

      <div className="relative z-10 mx-auto max-w-7xl px-6">
        <div
          className={`${
            !viewInteraction &&
            "flex min-h-[70vh] flex-col items-center justify-center gap-8"
          }`}
        >
          {!viewInteraction && (
            <div className="mb-12 text-center">
              <div
                className="mb-6 inline-flex items-center rounded-full border border-blue-200/50 dark:border-blue-800/50 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 px-6 py-2.5 text-sm font-medium text-blue-700 dark:text-blue-300 shadow-sm"
                style={{ fontFamily: "Sora, sans-serif" }}
              >
                <HiOutlineVideoCamera className="mr-2 h-4 w-4" />
                AI-Powered Talent Discovery
              </div>
              <h1
                className="text-slate-900 dark:text-white mb-6 text-3xl font-bold leading-tight sm:text-4xl lg:text-5xl"
                style={{ fontFamily: "Manrope, sans-serif" }}
              >
                Search Video Resumes with{" "}
                <span className="text-transparent bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text">
                  Smart Filters
                </span>
              </h1>
              <p
                className="text-slate-600 dark:text-slate-300 mx-auto max-w-2xl text-lg leading-relaxed"
                style={{ fontFamily: "Manrope, sans-serif" }}
              >
                Discover talented candidates by exploring their AI-powered video
                resumes. Use advanced search tools to find the perfect match for
                your team.
              </p>
            </div>
          )}

          <SearchBar
            setLoading={setLoading}
            setOldProfiles={setOldProfiles}
            oldProfiles={oldProfiles}
            loading={loading}
            setProfiles={setProfiles}
            setSetShowLoadMore={setSetShowLoadMore}
            getAllProfiles={getAllProfiles}
            setViewInteraction={setViewInteraction}
          />

          {viewInteraction ? (
            <>
              {/* Profile Grid */}
              <div className="mt-12 w-full">
                <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                  {profiles && profiles.length > 0 ? (
                    profiles.map((profile) =>
                      profile.id ? (
                        <ProfileCard
                          shortListedProfiles={shortListedProfiles}
                          key={profile.id}
                          keyValue={profile.id}
                          {...profile}
                          isShortlisted={selectedProfiles.has(profile.id)}
                          onShortlist={handleShortlist}
                          isLoading={loadingId === profile.id}
                          className="group relative transform overflow-hidden rounded-2xl border-2 border-blue-200/60 dark:border-blue-700/60 bg-white dark:bg-gray-800 shadow-xl shadow-blue-200/25 dark:shadow-blue-900/25 transition-all duration-500 hover:scale-105 hover:border-blue-300/80 dark:hover:border-blue-500/80 hover:shadow-2xl hover:shadow-blue-300/35 dark:hover:shadow-blue-900/35"
                        />
                      ) : (
                        <ProfileSkelLoader key={`skeleton-${Math.random()}`} />
                      )
                    )
                  ) : (
                    <div className="col-span-full flex flex-col items-center justify-center py-20">
                      <div className="relative">
                        <div className="absolute inset-0 translate-x-2 translate-y-2 transform rounded-3xl bg-gradient-to-br from-blue-500/20 via-indigo-500/10 to-purple-500/20 blur-2xl"></div>
                        <div className="relative rounded-2xl border-2 border-blue-200/60 dark:border-blue-800/60 bg-white dark:bg-gray-800 p-8 shadow-xl shadow-blue-200/25 dark:shadow-blue-900/25">
                          <HiOutlineUserGroup className="mx-auto mb-6 text-5xl text-blue-400 dark:text-blue-300" />
                          <h3
                            className="text-slate-800 dark:text-white mb-2 text-center text-xl font-bold"
                            style={{ fontFamily: "Manrope, sans-serif" }}
                          >
                            No profiles found
                          </h3>
                          <p
                            className="text-slate-600 dark:text-slate-300 text-center text-base"
                            style={{ fontFamily: "Sora, sans-serif" }}
                          >
                            Try different search parameters to find candidates.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
                {/* Load More Button */}
                {profiles &&
                  profiles.length < totalProfiles &&
                  showLoadMore && (
                    <div className="mt-12 flex justify-center">
                      <button
                        onClick={() => getAllProfiles()}
                        className="flex items-center justify-center gap-2 rounded-xl bg-blue-500 dark:bg-blue-600 px-10 py-5 font-semibold text-white shadow-lg shadow-blue-500/25 dark:shadow-blue-900/25 transition-all duration-300 hover:scale-105 hover:bg-blue-600 dark:hover:bg-blue-700 hover:shadow-xl hover:shadow-blue-500/40 dark:hover:shadow-blue-900/40"
                        style={{ fontFamily: "Manrope, sans-serif" }}
                      >
                        <HiOutlineUserGroup className="h-5 w-5" />
                        <span>Load More Profiles</span>
                      </button>
                    </div>
                  )}
              </div>
            </>
          ) : null}
        </div>
      </div>
    </section>
  );
};

export default ProfileSearchUI;
export { default as ProfileCard } from "./ProfileCard";
export { default as SearchBar } from "./SearchBar";
