import React, { useEffect, useState } from "react";
import { FcGoogle } from "react-icons/fc";
import { googleOAuthPopup } from "../../utils/googleAuth";
import Checkbox from "components/checkbox";
import Cookies from "js-cookie";
import Loader from "components/Loader";
import { useNavigate } from "react-router-dom";
import toast from "react-hot-toast";
import FixedPlugin from "components/fixedPlugin/FixedPlugin";
import { motion, AnimatePresence } from "framer-motion";

// Feature Cards Component - Hiring Manager
const FeatureCards = () => {
  const features = [
    {
      title: "Smart Candidate Matching in Seconds",
      description: "AI-powered matching system that instantly identifies the best candidates based on skills, experience, and cultural fit.",
      image: "/features/smart-matching.png",
    },
    {
      title: "Access Pre-Screened Video Interviews",
      description: "Browse through high-quality video interviews from candidates who have already been vetted and assessed by our AI.",
      image: "/features/video-interviews.png",
    },
    {
      title: "Advanced Analytics & Hiring Insights",
      description: "Get detailed analytics on candidate performance, hiring trends, and team fit predictions to make data-driven decisions.",
      image: "/features/analytics.png",
    },
  ];

  const [currentFeature, setCurrentFeature] = useState(0);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentFeature((prev) => (prev + 1) % features.length);
    }, 4000);
    return () => clearInterval(timer);
  }, [features.length]);

  return (
    <div className="relative h-full flex items-center justify-center p-8 overflow-hidden">
      <div className="w-full max-w-sm">
        {/* Simple Top-to-Bottom Sliding Cards */}
        <div className="relative h-[480px] flex items-center justify-center">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentFeature}
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              exit={{ y: -50, opacity: 0 }}
              transition={{ duration: 0.6, ease: "easeInOut" }}
              className="absolute"
            >
              <motion.div
                className="w-full max-w-sm bg-white/95 backdrop-blur-md rounded-3xl p-8 lg:p-10 shadow-2xl border border-white/30"
                whileHover={{ scale: 1.02, y: -5 }}
                transition={{ type: "spring", stiffness: 300, damping: 25 }}
              >
                {/* Header Section */}
                <div className="text-center mb-8">
                  <h3 
                    className="text-slate-900 text-xl font-bold leading-tight" 
                    style={{ fontFamily: "Manrope, sans-serif" }}
                  >
                    {features[currentFeature].title}
                  </h3>
                </div>

                {/* Feature Image */}
                <div className="relative mb-8 flex justify-center">
                  <motion.div
                    className="flex h-44 w-44 items-center justify-center"
                    whileHover={{ scale: 1.1, rotate: 2 }}
                    transition={{ type: "spring", stiffness: 400, damping: 15 }}
                  >
                    <img
                      src={features[currentFeature].image || "/placeholder.svg"}
                      alt={features[currentFeature].title}
                      className="h-full w-full object-contain drop-shadow-xl"
                    />
                  </motion.div>
                </div>

                {/* Description */}
                <div className="text-center">
                  <p 
                    className="text-slate-600 text-sm leading-relaxed" 
                    style={{ fontFamily: "Sora, sans-serif" }}
                  >
                    {features[currentFeature].description}
                  </p>
                </div>

                {/* Subtle Gradient Overlay */}
                <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-indigo-50/20 via-transparent to-purple-50/20 opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
              </motion.div>
            </motion.div>
          </AnimatePresence>
        </div>

        {/* Dots Indicator */}
        <div className="flex justify-center space-x-3 mt-6">
          {features.map((_, index) => (
            <motion.button
              key={index}
              onClick={() => setCurrentFeature(index)}
              className={`h-2 rounded-full transition-all duration-500 ${
                index === currentFeature 
                  ? 'w-8 bg-white shadow-lg shadow-white/50' 
                  : 'w-2 bg-white/60 hover:bg-white/80'
              }`}
              whileHover={{ scale: 1.2 }}
              whileTap={{ scale: 0.9 }}
            />
          ))}
        </div>
      </div>
      
      {/* Enhanced Custom Styles */}
      <style>{`
        @import url('https://fonts.googleapis.com/css2?family=Manrope:wght@200;300;400;500;600;700;800&family=Sora:wght@100;200;300;400;500;600;700;800&display=swap');
      `}</style>
    </div>
  );
};

export default function EmpSignIn() {
  // State to store email and password
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const GOOGLE_CLIENT_ID = "YOUR_GOOGLE_CLIENT_ID.apps.googleusercontent.com";

  const loginData = {
    email: email,
    password: password,
  };
  const saveloginresDataToCookie = (loginresData) => {
    const allCookies = Cookies.get(); // Get all cookies
    for (const cookieName in allCookies) {
      Cookies.remove(cookieName); // Remove each cookie
    }
    localStorage.clear();
    if (loginresData.role === "jobseeker") {
      // store only 'candId'
      Cookies.set("candId", loginresData.cand_id, { expires: 7 }); // Expires in 7 days
    } else {
      // store only 'candId'
      Cookies.set("empId", loginresData.emp_id, { expires: 7 }); // Expires in 7 days
      // store only 'candId'
      Cookies.set("employerId", loginresData.employerId, { expires: 7 }); // Expires in 7 days
    }
    // store only 'token'
    Cookies.set("jstoken", loginresData.token, { expires: 7 }); // Expires in 7 days
    // store only 'role'
    Cookies.set("role", loginresData.role, { expires: 7 }); // Expires in 7 days
  };

  // Google Sign-In handler
  const handleGoogleSignIn = () => {
    googleOAuthPopup(async ({ accessToken, idToken }) => {
      let googleUser = {};
      try {
        // Optionally fetch user info from Google
        const userInfoRes = await fetch(
          "https://www.googleapis.com/oauth2/v3/userinfo",
          {
            headers: { Authorization: `Bearer ${accessToken}` },
          }
        );
        const userInfo = await userInfoRes.json();
        googleUser = {
          email: userInfo.email,
          name: userInfo.name,
          picture: userInfo.picture,
        };

        // Send idToken to backend for verification and login
        const response = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/login-google`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ idToken }),
          }
        );
        const responseData = await response.json();
        if (!response.ok) {
          const msg = responseData.message
            ? responseData.message.toLowerCase()
            : "";
          if (
            msg.includes("not an employer account") ||
            msg.includes("user not found") ||
            msg.includes("register first") ||
            msg.includes("employer profile not found")
          ) {
            toast.error("Account not registered. Redirecting to registration...");
            setTimeout(() => {
              navigate("/create-account/employer", {
                state: {
                  email: googleUser.email || "",
                  name: googleUser.name || "",
                  picture: googleUser.picture || ""
                }
              });
            }, 1500);
            return;
          }
          toast.error(responseData.message || "Google login failed");
          return;
        }
        toast.success(
          googleUser.email
            ? `Signed in as ${googleUser.email}`
            : `Google login successful`
        );
        saveloginresDataToCookie(responseData);
        if (
          responseData.role &&
          responseData.role.toLowerCase() === "employer"
        ) {
          window.location.href = "/employer/dashboard";
        } else if (
          responseData.role &&
          responseData.role.toLowerCase() === "jobseeker"
        ) {
          window.location.href = "/candidate/dashboard";
        } else {
          window.location.href = "/";
        }
      } catch (err) {
        toast.error(err.message || "Google Sign-In failed");
      }
    });
  };

  // Handle form submission
  const handleSubmit = async (event) => {
    event.preventDefault(); // Prevent default form submission behavior

    // Log the loginData to ensure it's being updated
    console.log("Login Data:", loginData);
    if (loginData.email && loginData.password) {
      setIsLoading(true);
      try {
        // Replace the URL with your actual endpoint
        const response = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/login-employer`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(loginData), // Send JSON
          }
        );

        // Log the response
        const responseData = await response.json();
        if (!response.ok) {
          toast.error(responseData.message);
        } else {
          console.log("Response Data:", responseData);
          saveloginresDataToCookie(responseData);
          if (responseData.role.toLowerCase() === "jobseeker") {
            window.location.href = "/candidate/dashboard";
          } else {
            window.location.href = "/employer/dashboard";
          }
        }

        // Handle the response as needed
      } catch (error) {
        const errorMessage =
          error.response?.data?.message || "An unexpected error occurred.";
        toast.error(errorMessage); // Display the JSON error message
        console.error("Error:", error);
      }
      setIsLoading(false);
    } else {
      toast.error("enter email and password");
    }
  };

  return (
    <div className="min-h-screen flex flex-col lg:flex-row overflow-hidden" style={{backgroundColor: '#f0f7ff'}}>
      <FixedPlugin />
      
      {/* Logo - Responsive positioning */}
      <div className="absolute top-4 left-4 lg:top-6 lg:left-6 z-50">
        <img 
          src="/visume-logo-new.png" 
          alt="Visume Logo" 
          className="w-10 h-10 lg:w-12 lg:h-12"
        />
      </div>
      
      {/* Loader */}
      {isLoading && <Loader text={"Signing in..."} />}
      
      {/* Left Column - Form */}
      <div className="w-full lg:w-1/2 flex items-center justify-center p-4 sm:p-6 lg:p-8 bg-white min-h-screen lg:min-h-auto">
        <div className="w-full max-w-md mx-auto">

          {/* User Type Selection */}
          <div className="mb-6 lg:mb-8 text-center">
            <div className="flex flex-col sm:flex-row gap-2 mb-6">
              <button
                onClick={() => navigate('/candidate/sign-in')}
                className="flex-1 px-4 py-3 rounded-lg font-medium border border-gray-300 text-gray-600 hover:bg-gray-50 transition-all text-sm sm:text-base"
                style={{ fontFamily: 'Sora, sans-serif' }}
              >
                Job Seeker
              </button>
              <button
                onClick={() => navigate('/employer/sign-in')}
                className="flex-1 px-4 py-3 rounded-lg font-medium text-white transition-all text-sm sm:text-base"
                style={{
                  background: 'linear-gradient(135deg, #6366f1 0%, #4f46e5 100%)',
                  fontFamily: 'Sora, sans-serif'
                }}
              >
                Employer
              </button>
            </div>
          </div>

          {/* Header */}
          <div className="mb-6 lg:mb-8">
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2" style={{ fontFamily: 'Manrope, sans-serif' }}>Sign In as Employer</h1>
            <p className="text-gray-500 text-base sm:text-lg" style={{ fontFamily: 'Sora, sans-serif' }}>Welcome back to Visume</p>
          </div>

          {/* Google Sign In */}
          <button
            type="button"
            className="mb-4 lg:mb-6 flex h-11 sm:h-12 w-full cursor-pointer items-center justify-center gap-2 rounded-xl bg-gray-50 border border-gray-200 transition hover:bg-gray-100 hover:border-gray-300"
            onClick={handleGoogleSignIn}
            title="Sign in with Google"
          >
            <FcGoogle className="h-4 w-4 sm:h-5 sm:w-5" />
            <span className="text-sm font-medium text-gray-700" style={{ fontFamily: 'Sora, sans-serif' }}>Sign In with Google</span>
          </button>

          {/* Divider */}
          <div className="mb-4 lg:mb-6 flex items-center gap-4">
            <div className="h-px flex-1 bg-gray-200" />
            <span className="text-sm text-gray-500" style={{ fontFamily: 'Sora, sans-serif' }}>or</span>
            <div className="h-px flex-1 bg-gray-200" />
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} onKeyDown={(e) => e.key === "Enter" && handleSubmit(e)}>
            <div className="mb-3 lg:mb-4">
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2" style={{ fontFamily: 'Manrope, sans-serif' }}>
                Email
              </label>
              <input
                type="email"
                id="email"
                className="block w-full rounded-xl border border-gray-300 px-3 sm:px-4 py-2.5 sm:py-3 text-sm focus:outline-none focus:ring-2 focus:ring-opacity-20 transition-colors"
                style={{
                  '--tw-ring-color': '#6366f1'
                }}
                placeholder="<EMAIL>"
                onChange={(event) => setEmail(event.target.value)}
              />
            </div>

            <div className="mb-3 lg:mb-4">
              <div className="flex items-center justify-between mb-2">
                <label htmlFor="password" className="block text-sm font-medium text-gray-700" style={{ fontFamily: 'Manrope, sans-serif' }}>
                  Password
                </label>
                <a href=" " className="text-sm font-medium hover:underline" style={{color: '#6366f1', fontFamily: 'Sora, sans-serif'}}>
                  Forgot?
                </a>
              </div>
              <input
                type="password"
                id="password"
                className="block w-full rounded-xl border border-gray-300 px-3 sm:px-4 py-2.5 sm:py-3 text-sm focus:outline-none focus:ring-2 focus:ring-opacity-20 transition-colors"
                style={{
                  '--tw-ring-color': '#6366f1'
                }}
                placeholder="Enter your password"
                onChange={(event) => setPassword(event.target.value)}
              />
            </div>

            <div className="mb-4 lg:mb-6 flex items-center">
              <Checkbox />
              <span className="ml-2 text-sm text-gray-600" style={{ fontFamily: 'Sora, sans-serif' }}>Keep me logged In</span>
            </div>

            <button
              type="submit"
              className="w-full rounded-xl py-2.5 sm:py-3 font-medium text-white shadow-sm transition-all hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2"
              style={{
                background: 'linear-gradient(135deg, #6366f1 0%, #4f46e5 100%)',
                '--tw-ring-color': '#6366f1',
                fontFamily: 'Manrope, sans-serif'
              }}
            >
              Sign In
            </button>
          </form>

          <div className="mt-4 text-center">
            <span className="text-sm text-gray-600" style={{ fontFamily: 'Sora, sans-serif' }}>Don't have an account? </span>
            <button
              onClick={() => navigate("/create-account/employer")}
              className="text-sm font-medium hover:underline"
              style={{color: '#6366f1', fontFamily: 'Sora, sans-serif'}}
            >
              Register
            </button>
          </div>
        </div>
      </div>

      {/* Right Column - Feature Cards - Hidden on mobile */}
      <div className="hidden lg:block lg:w-1/2" style={{
        background: 'linear-gradient(135deg, #3b82f6 0%, #1e40af 50%, #1d4ed8 100%)'
      }}>
        <FeatureCards />
      </div>
    </div>
  );
}