import React from "react";
import { Routes, Route, Navigate, useLocation } from "react-router-dom";
import Navbar from "components/navbar";
import Sidebar from "components/sidebar";
import Footer from "components/footer/Footer";
import HeaderNav from "components/header/HeaderNav";
import routes from "routes";
import { FiAlignJustify } from "react-icons/fi";

export default function Candidate(props) {
  const { ...rest } = props;

  const location = useLocation();
  const [open, setOpen] = React.useState(window.innerWidth >= 1280);
  const [currentRoute, setCurrentRoute] = React.useState("Home");

  

  React.useEffect(() => {
    const handleResize = () => {
      setOpen(window.innerWidth >= 1280);
    };

    window.addEventListener('resize', handleResize);
    
    // Cleanup on unmount
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);
  

  React.useEffect(() => {
    getActiveRoute(routes);
  }, [location.pathname]);

  const getActiveRoute = (routes) => {

    let activeRoute = "Home";
    
    for (let i = 0; i < routes.length; i++) {       
    if (window.location.href.indexOf(routes[i].layout + "/" + routes[i].path)!== -1){
        setCurrentRoute(routes[i].name);
      }
    }
    return activeRoute;
  };

  const getActiveNavbar = (routes) => {
    let activeNavbar = false;
    for (let i = 0; i < routes.length; i++) {
      if (window.location.href.indexOf(routes[i].layout + routes[i].path) !== -1) {
        return routes[i].secondary;
      }
    }
    return activeNavbar;
  };

  const getRoutes = (routes) => {
    return routes.map((prop, key) => {
      if (prop.layout === "/candidate") {
        return (
          <Route path={`/${prop.path}`}  element={prop.component} key={key} />
        );
      } else {
        return null;
      } 
    });
  };

  document.documentElement.dir = "ltr";
  return (
    <div className="flex h-screen w-full bg-lightPrimary dark:bg-navy-900">
      <Sidebar open={open} onClose={() => {if(window.innerWidth < 1280){setOpen(false);} }}/>
      
      {/* Main Content Container */}
      <div className={`flex-1 flex flex-col transition-all duration-300 ease-in-out ${
        open ? 'xl:ml-64' : 'xl:ml-0'
      }`}>
        {/* Header Navigation */}
        <HeaderNav />
        
        {/* Mobile Sidebar Toggle - Only visible on mobile when sidebar is closed */}
        {!open && (
          <div className="xl:hidden fixed top-4 left-4 z-40">
            <button
              className="p-2 rounded-lg bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50 shadow-lg hover:bg-white dark:hover:bg-gray-800 transition-colors"
              onClick={() => setOpen(true)}
            >
              <FiAlignJustify className="h-4 w-4 text-gray-600 dark:text-gray-400" />
            </button>
          </div>
        )}
        
        {/* Main Content Area */}
        <main className="flex-1 overflow-auto bg-blue-50/30 dark:bg-gray-900">
          <div className="h-full px-6 py-4">
            <Routes>
              {getRoutes(routes)}
              <Route path="/" element={<Navigate to="/candidate/dashboard" replace />}/>
            </Routes>
          </div>
        </main>
      </div>
    </div>
  );
}
