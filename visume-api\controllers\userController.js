const bcrypt = require("bcryptjs");
const fs = require('fs');
const jwt = require("../utils/jwt");
const { generateRandomId } = require("../utils/helpers");
const axios = require("axios");
const prisma = require("../config/prisma");
const pdfParse = require('pdf-parse');
const {resumeStripper} = require('../utils/helpers');
const { OAuth2Client } = require('google-auth-library');
const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID;

// Process resume synchronously
const processResume = async (resumePath, cand_id) => {
  try {
    // Read and parse PDF
    const pdfBuffer = fs.readFileSync(resumePath);
    const pdfData = await pdfParse(pdfBuffer);
    const pdfText = pdfData.text;

    if (!pdfText) {
      throw new Error("Failed to extract text from PDF");
    }

    // Extract information using resumeStripper
    const strippedData = await resumeStripper(pdfText);
    if (!strippedData) {
      throw new Error("Failed to process resume content");
    }

    // Ensure we have valid JSON
    const strippedContent = JSON.stringify(strippedData);
    if (!strippedContent) {
      throw new Error("Failed to stringify resume data");
    }

    // Update database
    await prisma.jobseeker.update({
      where: {
        cand_id: cand_id,
      },
      data: {
        stripped_resume: strippedContent,
      },
    });

    console.log(`Successfully processed resume for candidate ID: ${cand_id}`);
    return true;
  } catch (err) {
    console.error("Error processing resume:", err);
    
    // Update database with error status
    await prisma.jobseeker.update({
      where: {
        cand_id: cand_id,
      },
      data: {
        stripped_resume: JSON.stringify({ error: "Failed to process resume" }),
      },
    });
    
    throw err;
  }
};

// Export all controller functions
const userController = {
  suggestedJobs: async (req, res) => {
    try {
      const jobs = await prisma.suggestedjobs.findMany();
      if (jobs.length === 0) {
        return res.status(404).json({ message: "No suggested jobs found" });
      }
      return res.status(200).json(jobs);
    } catch (err) {
      console.error("Error fetching suggested jobs:", err);
      return res.status(500).json({ error: err.message });
    }
  },

  loginJobseeker: async (req, res) => {
    console.log("login-jobseeker request body:", req.body);
    const { email, password, invite_context } = req.body;
    if (invite_context) {
      console.log("Received invite_context in login-jobseeker:", invite_context);
    }

    try {
      const user = await prisma.user.findUnique({
        where: { email },
        select: {
          email: true,
          password: true,
          role: true,
        },
      });

      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      const isPasswordValid = await bcrypt.compare(password, user.password);
      if (!isPasswordValid) {
        return res.status(401).json({ message: "Invalid password" });
      }

      if (user.role.toLowerCase() === "jobseeker") {
        const jobseeker = await prisma.jobseeker.findFirst({
          where: { cand_email: email },
          select: { cand_id: true },
        });

        if (!jobseeker) {
          return res.status(404).json({ message: "Candidate ID not found" });
        }

        // If invite_context is present, update candidate record
        if (invite_context) {
          try {
            await prisma.jobseeker.update({
              where: { cand_id: jobseeker.cand_id },
              data: { invite_context }
            });
            console.log("invite_context updated for candidate:", jobseeker.cand_id);
          } catch (err) {
            console.error("Failed to update invite_context for candidate:", jobseeker.cand_id, err);
          }
        }
        const token = jwt.generateToken(user);
        return res.json({ token, role: user.role, cand_id: jobseeker.cand_id });
      } else {
        return res.status(401).json({
          message: "You are not a Jobseeker, please login with the correct role.",
        });
      }
    } catch (err) {
      console.error("Error during login:", err);
      return res.status(500).json({ error: err.message });
    }
  },

  loginEmployer: async (req, res) => {
    const { email, password } = req.body;

    try {
      const user = await prisma.user.findUnique({
        where: { email },
        select: {
          email: true,
          password: true,
          role: true,
        },
      });

      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      const isPasswordValid = await bcrypt.compare(password, user.password);
      if (!isPasswordValid) {
        return res.status(401).json({ message: "Invalid password" });
      }

      if (user.role.toLowerCase() === "employer") {
        const employer = await prisma.employer.findFirst({
          where: { emp_email: email },
          select: { emp_id: true, id: true },
        });

        if (!employer) {
          return res.status(404).json({ message: "Employer ID not found" });
        }

        const token = jwt.generateToken(user);
        return res.json({
          token,
          role: user.role,
          emp_id: employer.emp_id,
          employerId: employer.id,
        });
      } else {
        return res.status(401).json({
          message: "You are not an Employer, please login with the correct role.",
        });
      }
    } catch (err) {
      console.error("Error during login:", err);
      return res.status(500).json({ error: err.message });
    }
  },

  registerJobseeker: async (req, res) => {
    try {
      const {
        email,
        password,
        cand_name,
        cand_mobile,
        gender,
        languages_known,
        preferred_location,
        invite_context // Accept invite context from request body
      } = req.body;

      const resumeFile = req.files?.resume?.[0];
      const profilePicFile = req.files?.profile_picture?.[0];

      if (!resumeFile) {
        const errorMsg = 'Resume file is required';
        console.error(`[400] Candidate registration failed: ${errorMsg}`);
        return res.status(400).json({ message: errorMsg, field: 'resume' });
      }

      // Validate profile picture (should be handled by multer, but double-check)
      let profilePicPath = null;
      if (profilePicFile) {
        const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg'];
        if (!allowedTypes.includes(profilePicFile.mimetype)) {
          const errorMsg = 'Invalid profile picture type';
          console.error(`[400] Candidate registration failed: ${errorMsg} (${profilePicFile.mimetype})`);
          return res.status(400).json({ message: errorMsg, field: 'profile_picture', receivedType: profilePicFile.mimetype });
        }
        if (profilePicFile.size > 5 * 1024 * 1024) {
          const errorMsg = 'Profile picture must be less than 5MB';
          console.error(`[400] Candidate registration failed: ${errorMsg} (size: ${profilePicFile.size})`);
          return res.status(400).json({ message: errorMsg, field: 'profile_picture', receivedSize: profilePicFile.size });
        }
        profilePicPath = profilePicFile.path.replace(/\\/g, '/');
      } else if (req.body.profile_pic_url) {
        // Accept Google profile picture URL if no file uploaded
        profilePicPath = req.body.profile_pic_url;
      }

      const existingUser = await prisma.user.findUnique({
        where: { email },
      });
      if (existingUser) {
        const errorMsg = 'Email already exists';
        console.error(`[409] Candidate registration failed: ${errorMsg} (${email})`);
        return res.status(409).json({ message: errorMsg, field: 'email' });
      }

      const hashedPassword = await bcrypt.hash(password, 10);
      const cand_id = generateRandomId();

      // Create or get default candidate plan
      let defaultCandidatePlan = await prisma.plans.findFirst({
        where: {
          role: 'js',
          plan_name: 'Free Candidate Plan'
        }
      });

      if (!defaultCandidatePlan) {
        defaultCandidatePlan = await prisma.plans.create({
          data: {
            plan_name: 'Free Candidate Plan',
            plan_description: 'Default free plan for new candidates - includes 1 free Visume',
            plan_price: 0,
            plan_duration_days: 365,
            role: 'js',
            credits_assigned: 1,
            features: '["1 Free Visume", "Basic profile access", "Standard support"]'
          }
        });
      }

      const result = await prisma.$transaction(async (prisma) => {
        const user = await prisma.user.create({
          data: {
            email,
            password: hashedPassword,
            role: 'jobseeker',
          },
        });

        const jobseeker = await prisma.jobseeker.create({
          data: {
            cand_id,
            cand_name,
            cand_mobile,
            cand_email: email,
            gender,
            languages_known,
            preferred_location,
            profile_picture: profilePicPath,
            invite_context: invite_context || null // Save invite context if present
          },
        });

        // Assign default plan to the new candidate
        await prisma.jobseekerplans.create({
          data: {
            cand_id: jobseeker.id,
            plan_id: defaultCandidatePlan.id,
            start_date: new Date(),
            end_date: new Date(Date.now() + defaultCandidatePlan.plan_duration_days * 24 * 60 * 60 * 1000),
            credits: defaultCandidatePlan.credits_assigned,
          },
        });

        return { user, jobseeker };
      });

      // Process resume after registration
      try {
        await processResume(resumeFile.path, cand_id);
      } catch (error) {
        console.error("Error processing resume:", error);
        // Continue with registration even if resume processing fails
        // The error status is already saved in the database by processResume
      }

      const token = jwt.generateToken({ email, role: 'jobseeker', cand_id });

      return res.status(200).json({
        message: 'Jobseeker registered successfully',
        token,
        role: 'jobseeker',
        cand_id,
        profile_picture: profilePicPath,
      });
    } catch (err) {
      console.error('Error during jobseeker registration:', err);
      return res.status(500).json({ error: 'Internal server error' });
    }
  },

  // Check email uniqueness endpoint
  checkEmailUniqueness: async (req, res) => {
    try {
      const { email } = req.body;

      if (!email) {
        return res.status(400).json({ message: 'Email is required' });
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return res.status(400).json({ message: 'Invalid email format' });
      }

      const existingUser = await prisma.user.findUnique({
        where: { email },
      });

      if (existingUser) {
        return res.status(409).json({
          message: 'Email already exists. Please log in.',
          field: 'email',
          available: false
        });
      }

      return res.status(200).json({
        message: 'Email is available',
        available: true
      });
    } catch (err) {
      console.error('Error checking email uniqueness:', err);
      return res.status(500).json({ error: 'Internal server error' });
    }
  },

  registerEmployeer: async (req, res) => {
    const {
      emp_email,
      password,
      emp_name,
      emp_mobile,
      designation,
      company_id, // Keep for backward compatibility
      company_name, // Keep for backward compatibility
      company_website, // Keep for backward compatibility
      organization, // Accept organization from frontend
    } = req.body;

    // Map organization to company_name if present
    let finalCompanyName = company_name;
    if (organization && typeof organization === "string" && organization.trim()) {
      finalCompanyName = organization.trim();
    }

    try {
      // Handle profile picture upload (similar to jobseeker)
      const profilePicFile = req.files?.profile_picture?.[0];
      let profilePicPath = null;
      if (profilePicFile) {
        const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg'];
        if (!allowedTypes.includes(profilePicFile.mimetype)) {
          const errorMsg = 'Invalid profile picture type';
          console.error(`[400] Employer registration failed: ${errorMsg} (${profilePicFile.mimetype})`);
          return res.status(400).json({ message: errorMsg, field: 'profile_picture', receivedType: profilePicFile.mimetype });
        }
        if (profilePicFile.size > 5 * 1024 * 1024) {
          const errorMsg = 'Profile picture must be less than 5MB';
          console.error(`[400] Employer registration failed: ${errorMsg} (size: ${profilePicFile.size})`);
          return res.status(400).json({ message: errorMsg, field: 'profile_picture', receivedSize: profilePicFile.size });
        }
        profilePicPath = profilePicFile.path.replace(/\\/g, '/');
      } else if (req.body.profile_picture_url && typeof req.body.profile_picture_url === "string" && req.body.profile_picture_url.startsWith("http")) {
        // Accept Google profile picture URL if no file uploaded
        profilePicPath = req.body.profile_picture_url;
      }

      // If company_id is provided, verify it exists first
      if (company_id) {
        const company = await prisma.company.findUnique({
          where: { id: parseInt(company_id) }
        });
        if (!company) {
          return res.status(400).json({ message: "Invalid company ID provided" });
        }
      }

      const existingUser = await prisma.user.findUnique({
        where: { email: emp_email },
      });
      if (existingUser) {
        return res.status(409).json({ message: "Email already exists" });
      }

      const hashedPassword = await bcrypt.hash(password, 10);
      
      // 🎯 EMPLOYER MEMBERSHIP: Create or get default employer plan with 10 free credits
      let defaultPlan = await prisma.plans.findFirst({
        where: {
          role: 'emp',
          plan_name: 'Free Employer Plan'
        }
      });

      if (!defaultPlan) {
        defaultPlan = await prisma.plans.create({
          data: {
            plan_name: 'Free Employer Plan',
            plan_description: 'Free plan with 10 credits for new employers',
            plan_price: 0,
            plan_duration_days: 365,
            role: 'emp',
            credits_assigned: 10,
            features: '["10 free credits", "Basic profile access", "Standard support"]'
          }
        });
      }

      const result = await prisma.$transaction(async (prisma) => {
        const user = await prisma.user.create({
          data: {
            email: emp_email,
            password: hashedPassword,
            role: 'employer',
          },
        });

        const employerData = {
          emp_id: user.id,
          emp_name,
          emp_email,
          emp_mobile: BigInt(emp_mobile),
          designation,
          profile_picture: profilePicPath,
        };

        // Create company if company information is provided
        if (finalCompanyName && finalCompanyName.trim()) {
          const companyData = {
            company_name: finalCompanyName,
          };

          if (company_website && company_website.trim()) {
            companyData.company_website = company_website.trim();
          }

          const company = await prisma.company.create({
            data: companyData
          });

          employerData.company_id = company.id;
        } else if (company_id) {
          // Use existing company_id if provided (for backward compatibility)
          employerData.company_id = parseInt(company_id);
        }

        const employer = await prisma.employer.create({
          data: employerData
        });

        await prisma.employerplans.create({
          data: {
            emp_id: employer.id,
            plan_id: defaultPlan.id,
            start_date: new Date(),
            end_date: new Date(Date.now() + defaultPlan.plan_duration_days * 24 * 60 * 60 * 1000),
            creditsLeft: defaultPlan.credits_assigned,
          },
        });

        return { emp_id: user.id, employerId: employer.id, profile_picture: profilePicPath };
      });

      const token = jwt.generateToken({
        email: emp_email,
        role: "employer",
      });

      return res.status(200).json({
        message: "Employer registered successfully",
        token,
        role: "employer",
        ...result,
      });
    } catch (err) {
      console.error("Error during employer registration:", err);
      return res.status(500).json({ error: err.message });
    }
  },

  getRoles: async (req, res) => {
    try {
      const roles = await prisma.roles.findMany({
        select: {
          id: true,
          role_name: true
        }
      });

      if (roles.length === 0) {
        return res.status(404).json({ message: "No roles found" });
      }

      return res.status(200).json({ roles });
    } catch (err) {
      console.error("Error fetching roles:", err);
      return res.status(500).json({ error: err.message });
    }
  },

  getSkills: async (req, res) => {
    try {
      const skills = await prisma.skills.findMany({
        select: {
          id: true,
          skill_name: true
        }
      });

      if (skills.length === 0) {
        return res.status(404).json({ message: "No skills found" });
      }

      return res.status(200).json({ skills });
    } catch (err) {
      console.error("Error fetching skills:", err);
      return res.status(500).json({ error: err.message });
    }
  },

  recommendSkills: async (req, res) => {
    try {
      const { jobRole } = req.body;

      if (!jobRole || typeof jobRole !== 'string' || jobRole.trim().length === 0) {
        return res.status(400).json({
          error: "Job role is required for skill recommendations"
        });
      }

      const { generateSkillRecommendations } = require('../utils/helpers');
      const recommendedSkills = await generateSkillRecommendations(jobRole.trim());

      return res.status(200).json({
        skills: recommendedSkills,
        jobRole: jobRole.trim()
      });
    } catch (err) {
      console.error("Error generating skill recommendations:", err);
      return res.status(500).json({
        error: "Failed to generate skill recommendations",
        details: err.message
      });
    }
  },

  changePassword: async (req, res) => {
    const { cand_id, emp_id, password, newPassword } = req.body;

    try {
      let email;
      if (emp_id) {
        const employer = await prisma.employer.findUnique({
          where: { id: emp_id },
          select: { emp_email: true },
        });
        if (!employer) return res.status(404).json({ message: "Employer not found" });
        email = employer.emp_email;
      } else {
        const jobseeker = await prisma.jobseeker.findUnique({
          where: { cand_id },
          select: { cand_email: true },
        });
        if (!jobseeker) return res.status(404).json({ message: "Jobseeker not found" });
        email = jobseeker.cand_email;
      }

      const user = await prisma.user.findUnique({
        where: { email },
        select: { password: true },
      });

      if (!user) return res.status(404).json({ message: "User not found" });

      const isPasswordValid = await bcrypt.compare(password, user.password);
      if (!isPasswordValid) {
        return res.status(401).json({ message: "Invalid password" });
      }

      if (password === newPassword) {
        return res.status(400).json({
          message: "New password cannot be the same as the old password",
        });
      }

      const hashedNewPassword = await bcrypt.hash(newPassword, 10);
      await prisma.user.update({
        where: { email },
        data: { password: hashedNewPassword },
      });

      return res.json({ message: "Password changed successfully" });
    } catch (err) {
      console.error("Error during password change:", err);
      return res.status(500).json({ error: err.message });
    }
  },

uploadResume: async (req, res) => {
  try {
    const cand_id = req.body.cand_id;
    const resumeFile = req.files?.resume?.[0];
    if (!resumeFile || !cand_id) {
      return res.status(400).json({ message: 'Resume file and candidate ID are required' });
    }
    await processResume(resumeFile.path, cand_id);
    return res.status(200).json({ message: 'Resume uploaded and parsed successfully' });
  } catch (err) {
    console.error('Error uploading resume:', err);
    return res.status(500).json({ error: 'Internal server error' });
  }
},

loginGoogle: async (req, res) => {
  const { idToken } = req.body;
  if (!idToken) {
    return res.status(400).json({ message: "Missing Google ID token" });
  }
  try {
    const client = new OAuth2Client(GOOGLE_CLIENT_ID);
    const ticket = await client.verifyIdToken({
      idToken,
      audience: GOOGLE_CLIENT_ID,
    });
    const payload = ticket.getPayload();
    const email = payload.email;
    const name = payload.name || "";
    // Find user
    let user = await prisma.user.findUnique({ where: { email } });
    let role = "jobseeker";
    let cand_id = null;
    // Only allow login for existing users
    if (!user) {
      return res.status(404).json({ message: "User not found. Please register first." });
    } else if (user.role.toLowerCase() === "jobseeker") {
      const jobseeker = await prisma.jobseeker.findFirst({
        where: { cand_email: email },
        select: { cand_id: true },
      });
      if (!jobseeker) {
        return res.status(404).json({ message: "Jobseeker profile not found. Please register first." });
      }
      cand_id = jobseeker.cand_id;
    } else {
      // Not a jobseeker
      return res.status(401).json({ message: "Not a jobseeker account" });
    }
    const token = jwt.generateToken({ email, role, cand_id });
    return res.json({ token, role, cand_id });
  } catch (err) {
    console.error("Google login error:", err);
    return res.status(401).json({ message: "Invalid Google token" });
  }
},

// Upload candidate profile image
uploadCandidateProfileImage: async (req, res) => {
  try {
    const cand_id = req.body.cand_id;
    const profilePicFile = req.files?.profile_picture?.[0];

    if (!profilePicFile || !cand_id) {
      return res.status(400).json({ message: "Profile image and candidate ID are required" });
    }

    // Validate file type and size (already handled by multer, but double-check)
    const allowedTypes = ["image/jpeg", "image/png", "image/jpg"];
    if (!allowedTypes.includes(profilePicFile.mimetype)) {
      return res.status(400).json({ message: "Invalid profile image type" });
    }
    if (profilePicFile.size > 5 * 1024 * 1024) {
      return res.status(400).json({ message: "Profile image must be less than 5MB" });
    }

    const profilePicPath = profilePicFile.path.replace(/\\/g, "/");

    // Update candidate profile picture in DB
    await prisma.jobseeker.update({
      where: { cand_id },
      data: { profile_picture: profilePicPath }
    });

    return res.status(200).json({
      message: "Profile image uploaded successfully",
      profile_picture_url: profilePicPath
    });
  } catch (err) {
    console.error("Error uploading candidate profile image:", err);
    return res.status(500).json({ message: "Failed to upload profile image" });
  }
},

// Update candidate profile
updateCandidateProfile: async (req, res) => {
  try {
    const cand_id = req.params.cand_id;
    const {
      cand_name,
      cand_email,
      cand_mobile,
      languages_known,
      preferred_location,
      profile_picture,
      gender
    } = req.body;

    // Validate candidate existence
    const candidate = await prisma.jobseeker.findUnique({
      where: { cand_id }
    });
    if (!candidate) {
      return res.status(404).json({ message: "Candidate not found" });
    }

    // Update candidate profile
    await prisma.jobseeker.update({
      where: { cand_id },
      data: {
        cand_name,
        cand_email,
        cand_mobile,
        languages_known,
        preferred_location,
        profile_picture,
        gender
      }
    });

    return res.status(200).json({ message: "Profile updated successfully" });
  } catch (err) {
    console.error("Error updating candidate profile:", err);
    return res.status(500).json({ message: "Failed to update profile" });
  }
},

// Update candidate notification settings
updateCandidateNotifications: async (req, res) => {
  try {
    const cand_id = req.params.cand_id;
    const notificationSettings = req.body;

    // Validate candidate existence
    const candidate = await prisma.jobseeker.findUnique({
      where: { cand_id }
    });
    if (!candidate) {
      return res.status(404).json({ message: "Candidate not found" });
    }

    // For now, we'll just return success since notification settings
    // might be stored in a separate table or handled differently
    // You can extend this to actually store the settings in the database

    return res.status(200).json({ message: "Notification preferences updated successfully" });
  } catch (err) {
    console.error("Error updating notification settings:", err);
    return res.status(500).json({ message: "Failed to update notification settings" });
  }
},

// Update candidate privacy settings
updateCandidatePrivacy: async (req, res) => {
  try {
    const cand_id = req.params.cand_id;
    const privacySettings = req.body;

    // Validate candidate existence
    const candidate = await prisma.jobseeker.findUnique({
      where: { cand_id }
    });
    if (!candidate) {
      return res.status(404).json({ message: "Candidate not found" });
    }

    // For now, we'll just return success since privacy settings
    // might be stored in a separate table or handled differently
    // You can extend this to actually store the settings in the database

    return res.status(200).json({ message: "Privacy settings updated successfully" });
  } catch (err) {
    console.error("Error updating privacy settings:", err);
    return res.status(500).json({ message: "Failed to update privacy settings" });
  }
},

// Upload candidate profile image
uploadCandidateProfileImage: async (req, res) => {
  try {
    const cand_id = req.body.cand_id;
    const profilePicFile = req.files?.profile_picture?.[0];

    if (!profilePicFile || !cand_id) {
      return res.status(400).json({ message: "Profile image and candidate ID are required" });
    }

    // Validate file type and size (already handled by multer, but double-check)
    const allowedTypes = ["image/jpeg", "image/png", "image/jpg"];
    if (!allowedTypes.includes(profilePicFile.mimetype)) {
      return res.status(400).json({ message: "Invalid profile image type" });
    }
    if (profilePicFile.size > 5 * 1024 * 1024) {
      return res.status(400).json({ message: "Profile image must be less than 5MB" });
    }

    const profilePicPath = profilePicFile.path.replace(/\\/g, "/");

    // Update candidate profile picture in DB
    await prisma.jobseeker.update({
      where: { cand_id },
      data: { profile_picture: profilePicPath }
    });

    return res.status(200).json({
      message: "Profile image uploaded successfully",
      profile_picture_url: profilePicPath
    });
  } catch (err) {
    console.error("Error uploading candidate profile image:", err);
    return res.status(500).json({ message: "Failed to upload profile image" });
  }
},

// Check phone number uniqueness for job seekers
checkPhoneUniqueness: async (req, res) => {
  try {
    const { phone } = req.body;
    console.log('Checking phone uniqueness for:', phone);

    if (!phone) {
      return res.status(400).json({ message: 'Phone number is required' });
    }

    // Validate phone format (10 digits)
    const phoneRegex = /^[0-9]{10}$/;
    if (!phoneRegex.test(phone)) {
      return res.status(400).json({ message: 'Invalid phone number format. Must be 10 digits.' });
    }

    const existingJobseeker = await prisma.jobseeker.findFirst({
      where: { cand_mobile: phone },
    });

    console.log('Existing jobseeker found:', existingJobseeker ? 'Yes' : 'No');

    if (existingJobseeker) {
      return res.status(409).json({
        message: 'Phone number already exists. Please use a different number.',
        available: false,
      });
    }

    return res.status(200).json({
      message: 'Phone number is available',
      available: true,
    });
  } catch (err) {
    console.error('Error checking phone uniqueness:', err);
    return res.status(500).json({ 
      message: 'Error checking phone availability',
      error: err.message 
    });
  }
},
};

// Export the controller object
module.exports = userController;