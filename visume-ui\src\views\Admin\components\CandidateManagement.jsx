import React, { useState, useEffect } from "react";
import { 
  Search, 
  Filter, 
  Users, 
  Crown, 
  Edit3, 
  MoreVertical,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Video,
  FileText,
  Star
} from "lucide-react";
import { HiOutlineSparkles } from "react-icons/hi";
import { AdminSearchBar, StatusBadge, FilterDropdown, ActionButton } from "./AdminComponents";
import UnifiedManagementModal from "./UnifiedManagementModal";
import avatar from "assets/img/avatars/avatar4.png";
import toast from "react-hot-toast";

const CandidateManagement = () => {
  const [candidates, setCandidates] = useState([]);
  const [filteredCandidates, setFilteredCandidates] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [planFilter, setPlanFilter] = useState("");
  const [selectedCandidate, setSelectedCandidate] = useState(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editVisumeLimit, setEditVisumeLimit] = useState("");

  // API base URL
  const API_BASE_URL = import.meta.env.VITE_APP_HOST;

  useEffect(() => {
    const fetchCandidates = async () => {
      setLoading(true);
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/admin/candidates`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.message || `HTTP error! status: ${response.status}`);
        }

        // Transform the API data to match the component's expected format
        const transformedCandidates = data.data.candidates.map(candidate => ({
          id: candidate.id,
          cand_id: candidate.cand_id,
          name: candidate.name,
          email: candidate.email,
          phone: candidate.phone || 'N/A',
          profile_picture: candidate.profile_picture,
          currentVisumeCount: candidate.currentVisumeCount,
          allowedVisumes: candidate.allowedVisumes,
          planName: candidate.planName,
          membershipStatus: candidate.membershipStatus,
          joinDate: candidate.joinDate,
          skills: candidate.skills || 'No skills listed',
          location: candidate.location || 'Location not specified',
          // Add some default values for display
          experience: 'N/A', // This could be calculated or added to API later
          profileViews: 0, // This could be added to API later
          shortlisted: 0 // This could be added to API later
        }));

        setCandidates(transformedCandidates);
        setFilteredCandidates(transformedCandidates);
      } catch (error) {
        console.error("Error fetching candidates:", error);
        toast.error("Failed to load candidates: " + error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchCandidates();
  }, []);

  // Filter candidates based on search and filters
  useEffect(() => {
    let filtered = candidates;

    if (searchTerm) {
      filtered = filtered.filter(candidate => 
        candidate.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        candidate.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        candidate.skills.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter) {
      filtered = filtered.filter(candidate => candidate.membershipStatus === statusFilter);
    }

    if (planFilter) {
      filtered = filtered.filter(candidate => candidate.planName === planFilter);
    }

    setFilteredCandidates(filtered);
  }, [searchTerm, statusFilter, planFilter, candidates]);

  const handleEditVisumeLimit = (candidate) => {
    setSelectedCandidate(candidate);
    setEditVisumeLimit(candidate.allowedVisumes.toString());
    setShowEditModal(true);
  };

  const handleSaveVisumeLimit = async () => {
    if (!selectedCandidate) return;

    try {
      const newLimit = parseInt(editVisumeLimit);
      if (isNaN(newLimit) || newLimit < 0) {
        toast.error("Please enter a valid Visume limit");
        return;
      }

      // Call the API to update the candidate's limit
      const response = await fetch(`${API_BASE_URL}/api/v1/admin/candidate/${selectedCandidate.cand_id}/limit`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          allowedVisumes: newLimit,
          planName: selectedCandidate.planName
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      // Update local state with the response data
      const updatedCandidates = candidates.map(candidate =>
        candidate.cand_id === selectedCandidate.cand_id
          ? {
              ...candidate,
              allowedVisumes: data.data.allowedVisumes,
              currentVisumeCount: data.data.currentVisumeCount,
              planName: data.data.planName,
              membershipStatus: data.data.canCreateVisume ? 'active' : 'warning'
            }
          : candidate
      );

      setCandidates(updatedCandidates);
      setFilteredCandidates(prev =>
        prev.map(candidate =>
          candidate.cand_id === selectedCandidate.cand_id
            ? updatedCandidates.find(c => c.cand_id === selectedCandidate.cand_id)
            : candidate
        )
      );

      setShowEditModal(false);
      setSelectedCandidate(null);
      setEditVisumeLimit("");

      toast.success(data.message || `Visume limit updated successfully for ${selectedCandidate.name}`);
    } catch (error) {
      console.error("Error updating Visume limit:", error);
      toast.error("Failed to update Visume limit: " + error.message);
    }
  };

  const CandidateCard = ({ candidate }) => (
    <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-200">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center gap-4">
          <div className="relative">
            <img
              src={candidate.profile_picture || avatar}
              alt={candidate.name}
              className="w-12 h-12 rounded-full object-cover border-2 border-gray-200 dark:border-gray-600"
            />
            <div className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white dark:border-gray-800 ${
              candidate.membershipStatus === 'active' ? 'bg-green-500' :
              candidate.membershipStatus === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
            }`}></div>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {candidate.name}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {candidate.experience} experience
            </p>
            <div className="flex items-center gap-4 mt-1 text-xs text-gray-500 dark:text-gray-400">
              <span className="flex items-center gap-1">
                <Mail className="w-3 h-3" />
                {candidate.email}
              </span>
              <span className="flex items-center gap-1">
                <MapPin className="w-3 h-3" />
                {candidate.location}
              </span>
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <StatusBadge status={candidate.membershipStatus} type="membership" />
          <ActionButton
            onClick={() => handleEditVisumeLimit(candidate)}
            variant="secondary"
            size="xs"
            icon={Edit3}
          >
            Edit
          </ActionButton>
        </div>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
        <div className="text-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
          <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
            {candidate.currentVisumeCount}
          </div>
          <div className="text-xs text-purple-600 dark:text-purple-400 font-medium">
            Visumes Created
          </div>
        </div>
        <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
            {candidate.allowedVisumes}
          </div>
          <div className="text-xs text-blue-600 dark:text-blue-400 font-medium">
            Visume Limit
          </div>
        </div>
        <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <div className="text-2xl font-bold text-green-600 dark:text-green-400">
            {candidate.profileViews}
          </div>
          <div className="text-xs text-green-600 dark:text-green-400 font-medium">
            Profile Views
          </div>
        </div>
        <div className="text-center p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
          <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
            {candidate.shortlisted}
          </div>
          <div className="text-xs text-orange-600 dark:text-orange-400 font-medium">
            Shortlisted
          </div>
        </div>
      </div>

      {/* Visume Usage Progress Bar */}
      <div className="mb-4">
        <div className="flex justify-between text-sm mb-1">
          <span className="text-gray-600 dark:text-gray-400">Visume Usage</span>
          <span className="text-gray-900 dark:text-white font-medium">
            {candidate.currentVisumeCount} / {candidate.allowedVisumes}
          </span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${
              candidate.currentVisumeCount < candidate.allowedVisumes ? 'bg-green-500' :
              candidate.currentVisumeCount === candidate.allowedVisumes ? 'bg-yellow-500' : 'bg-red-500'
            }`}
            style={{
              width: `${Math.min(100, (candidate.currentVisumeCount / candidate.allowedVisumes) * 100)}%`
            }}
          ></div>
        </div>
      </div>

      {/* Skills */}
      <div className="mb-4">
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Skills</h4>
        <div className="flex flex-wrap gap-2">
          {candidate.skills.split(", ").slice(0, 3).map((skill, index) => (
            <span
              key={index}
              className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs rounded-full"
            >
              {skill}
            </span>
          ))}
          {candidate.skills.split(", ").length > 3 && (
            <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs rounded-full">
              +{candidate.skills.split(", ").length - 3} more
            </span>
          )}
        </div>
      </div>

      {/* Plan Information */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Crown className="w-4 h-4 text-purple-500" />
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            {candidate.planName}
          </span>
        </div>
        <div className="text-xs text-gray-500 dark:text-gray-400">
          Joined {new Date(candidate.joinDate).toLocaleDateString()}
        </div>
      </div>
    </div>
  );

  return (
    <div className="p-6">
      {/* Header and Search */}
      <div className="flex flex-col lg:flex-row gap-4 mb-6">
        <AdminSearchBar
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          placeholder="Search candidates by name, email, or skills..."
          loading={loading}
        />
        <div className="flex gap-2">
          <FilterDropdown
            label="Status"
            options={[
              { label: "Active", value: "active" },
              { label: "Warning", value: "warning" },
              { label: "Expired", value: "expired" }
            ]}
            value={statusFilter}
            onChange={setStatusFilter}
            icon={Filter}
          />
          <FilterDropdown
            label="Plan"
            options={[
              { label: "Free Plan", value: "Free Plan" },
              { label: "Pro Plan", value: "Pro Plan" },
              { label: "Premium Plan", value: "Premium Plan" }
            ]}
            value={planFilter}
            onChange={setPlanFilter}
            icon={Crown}
          />
        </div>
      </div>

      {/* Results Summary */}
      <div className="mb-6">
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Showing {filteredCandidates.length} of {candidates.length} candidates
        </p>
      </div>

      {/* Candidates Grid */}
      {loading ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {[...Array(6)].map((_, index) => (
            <div key={index} className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 animate-pulse">
              <div className="flex items-center gap-4 mb-4">
                <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3"></div>
                </div>
              </div>
              <div className="grid grid-cols-4 gap-4">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="h-16 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
                ))}
              </div>
            </div>
          ))}
        </div>
      ) : filteredCandidates.length > 0 ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {filteredCandidates.map((candidate) => (
            <CandidateCard key={candidate.id} candidate={candidate} />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <Users className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No candidates found
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            Try adjusting your search criteria or filters
          </p>
        </div>
      )}

      {/* Edit Visume Limit Modal */}
      <UnifiedManagementModal
        isOpen={showEditModal}
        onClose={() => {
          setShowEditModal(false);
          setSelectedCandidate(null);
          setEditVisumeLimit("");
        }}
        onSave={handleSaveVisumeLimit}
        selectedEntity={selectedCandidate}
        entityType="candidate"
        value={editVisumeLimit}
        setValue={setEditVisumeLimit}
        isUpdating={false}
        StatusBadge={StatusBadge}
      />
    </div>
  );
};

export default CandidateManagement;
