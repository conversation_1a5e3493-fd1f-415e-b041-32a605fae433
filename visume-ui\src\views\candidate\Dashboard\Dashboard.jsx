// Dashboard.jsx
import React, { useEffect, useState } from "react";
import Cookies from "js-cookie";
import { useNavigate } from "react-router-dom";
import DashboardContent from "./DashboardContent";
import avatar from "assets/img/avatars/avatar4.png";
import { Eye, ClipboardCheck, MousePointerClick, Unlock } from "lucide-react";
import { getFormattedMembershipStatus } from "../../../services/membershipService";

const Dashboard = () => {
  console.log(
    "Dashboard initial window.location.search:",
    window.location.search
  );
  const jstoken = Cookies.get("jstoken");
  const candId = Cookies.get("candId");
  const [profile_picture, setProfilePicture] = useState(avatar);
  const [imageError, setImageError] = useState(false);

  const [videoProfiles, setVideoProfiles] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [userStats, setUserStats] = useState([
    {
      count: 15,
      label: "Shortlists",
      Icon: ClipboardCheck,
    },
    {
      count: 23,
      label: "Views",
      Icon: Eye,
    },
    {
      count: 23,
      label: "Clicks",
      Icon: MousePointerClick,
    },
    {
      count: 3,
      label: "Unlocks",
      Icon: Unlock,
    },
  ]);
  const [candData, setCandData] = useState("");
  const [loadingInfo, setLoadingInfo] = useState(false);
  const [jobData, setJobData] = useState([]);
  const [membershipStatus, setMembershipStatus] = useState(null);
  const [membershipLoading, setMembershipLoading] = useState(false);

  // Invite data state
  const [inviteData, setInviteData] = useState(null);

  useEffect(() => {
    const fetchJobData = async () => {
      try {
        const resjobdata = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/suggestedJobs`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
          }
        );
        if (resjobdata.ok) {
          const data = await resjobdata.json();
          setJobData(Array.isArray(data) ? data : []);
        }
      } catch (error) {
        // handle error
      }
    };
    fetchJobData();
  }, []);

  // 🎯 MEMBERSHIP STATUS: Fetch candidate's membership status and limits
  useEffect(() => {
    const fetchDashboardData = async () => {
      if (!candId) return;
      setLoadingInfo(true);
      setMembershipLoading(true);
      try {
        const response = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/candidate-dashboard/${candId}`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
          }
        );
        if (response.ok) {
          const data = await response.json();
          // Candidate profile
          setCandData(data.candidateProfile[0]);
          // Video profiles
          const newProfiles = data.videoProfiles.map((profile) => ({
            vpid: profile.video_profile_id,
            role: profile.role,
            skills: profile.skills.split(",").map((skill) => skill.trim()),
            status: profile.status,
          }));
          setVideoProfiles(newProfiles);
          // Membership status
          setMembershipStatus(data.membershipStatus);
          // User stats
          setUserStats([
            {
              count: data.candidateProfile[0]?.statusCounts?.shortlisted ?? 0,
              label: "Shortlists",
              Icon: ClipboardCheck,
            },
            {
              count: data.candidateProfile[0]?.interactions?.view ?? 0,
              label: "Views",
              Icon: Eye,
            },
            {
              count: data.candidateProfile[0]?.interactions?.click ?? 0,
              label: "Clicks",
              Icon: MousePointerClick,
            },
            {
              count: data.candidateProfile[0]?.statusCounts?.unlocked ?? 0,
              label: "Unlocks",
              Icon: Unlock,
            },
          ]);
          // Profile picture
          if (data.candidateProfile[0]?.profile_picture) {
            const pic = data.candidateProfile[0].profile_picture;
            if (pic.startsWith("http")) {
              setProfilePicture(pic);
            } else {
              setProfilePicture(`${import.meta.env.VITE_APP_HOST}/${pic}`);
            }
          }
        }
      } catch (error) {
        // handle error
      }
      setLoadingInfo(false);
      setMembershipLoading(false);
    };
    fetchDashboardData();
  }, [candId]);

  const [createVRpopup, setcreateVRpopup] = useState(false);
  const [showVideoProfilePopup, setShowVideoProfilePopup] = useState(false);

  const togglePopupVR = () => {
    setcreateVRpopup(!createVRpopup);
  };

  const toggleVideoProfilePopup = () => {
    setShowVideoProfilePopup(!showVideoProfilePopup);
  };

  // Check for invite data in query params after candidate info loads
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const inviteId = params.get("inviteId");
    const jobRole = params.get("jobRole");
    const skills = params.get("skills");
    const experience = params.get("experience");
    const locationParam = params.get("location");
    const companyType = params.get("companyType");
    const empId = params.get("empId");
    const jobId = params.get("jobId");

    if (jobRole) {
      const inviteObj = {
        jobRole,
        skills: skills ? skills.split(",") : [],
        experience,
        location: locationParam,
        companyType,
        empId,
        jobId,
      };
      setInviteData(inviteObj);
      setcreateVRpopup(true);
      window.history.replaceState({}, '', '/candidate/dashboard');
    } else if (inviteId) {
      // Fetch invite context from API if only inviteId is present
      fetch(`${import.meta.env.VITE_APP_HOST}/api/v1/invite/${inviteId}`)
        .then((res) => {
          if (!res.ok) throw new Error("Failed to fetch invite context");
          return res.json();
        })
        .then((data) => {
          setInviteData(data);
          setcreateVRpopup(true);
          window.history.replaceState({}, '', '/candidate/dashboard');
        })
        .catch((err) => {
          console.error("Error fetching invite context:", err);
        });
    }
  }, [candId, loadingInfo]);

  // Show CreateVR modal if invite data is present in localStorage
  useEffect(() => {
    let inviteDataLS = null;
    try {
      inviteDataLS = JSON.parse(localStorage.getItem("pendingInviteData"));
    } catch {
      inviteDataLS = null;
    }
    if (
      inviteDataLS &&
      typeof inviteDataLS === "object" &&
      Object.keys(inviteDataLS).length > 0
    ) {
      setInviteData(inviteDataLS);
      setcreateVRpopup(true);
      // Optionally clear after use
      localStorage.removeItem("pendingInviteData");
    }
  }, []);

  return (
    <DashboardContent
      jstoken={jstoken}
      candData={candData}
      loadingInfo={loadingInfo}
      togglePopupVR={togglePopupVR}
      profile_picture={profile_picture}
      imageError={imageError}
      setImageError={setImageError}
      userStats={userStats}
      jobData={jobData}
      videoProfiles={videoProfiles}
      isLoading={isLoading}
      createVRpopup={createVRpopup}
      showVideoProfilePopup={showVideoProfilePopup}
      toggleVideoProfilePopup={toggleVideoProfilePopup}
      membershipStatus={membershipStatus}
      membershipLoading={membershipLoading}
      inviteData={inviteData}
    />
  );
};

export default Dashboard;
