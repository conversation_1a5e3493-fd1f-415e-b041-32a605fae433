const express = require('express');
const router = express.Router();
const r2Controller = require('../controllers/r2UploadController');

// Debug middleware for R2 routes
router.use((req, res, next) => {
    console.log('[DEBUG] R2 Route accessed:', req.method, req.path);
    next();
});

// Health check endpoint to validate R2 connectivity
router.get('/r2/health', (req, res, next) => {
    console.log('[DEBUG] R2 health check route hit');
    next();
}, r2Controller.checkR2Health);

// Generate presigned URL for direct upload to R2
router.get('/r2-upload-url', (req, res, next) => {
    console.log('[DEBUG] R2 upload URL route hit with query:', req.query);
    next();
}, r2Controller.generateUploadUrl);

module.exports = router;