import React from "react";

// Modern StatCard Component matching dashboard design
const StatCard = ({ title, value, borderColor, icon, onClick }) => (
  <div 
    className={`bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer w-full border-l-4 ${borderColor}`}
    onClick={onClick}
  >
    <div className="flex justify-between items-start">
      <div className="flex flex-col">
        <p className="text-sm font-medium text-gray-600 mb-2">
          {title}
        </p>
        <p className="text-2xl font-bold text-gray-900">
          {value || 0}
        </p>
      </div>
      <div className="flex-shrink-0 ml-4">
        {icon && (
          <div className={`p-2 rounded-lg ${borderColor.replace('border-', 'bg-').replace('-500', '-100')}`}>
            {icon}
          </div>
        )}
      </div>
    </div>
  </div>
);

const StatsOverview = ({
  shortListedCandidatesCount,
  unlockedCandidatesCount,
  InterviewedCandidatesCount,
  offeredCandidatesCount,
  navigate,
}) => (
  <div className="col-span-full lg:col-span-9">
    <div className="grid grid-cols-2 md:grid-cols-4 gap-6 w-full">
      <StatCard
        title="Shortlisted"
        value={shortListedCandidatesCount}
        borderColor="border-cyan-500"
        icon={
          <svg className="w-5 h-5 text-cyan-500" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
          </svg>
        }
        onClick={() => navigate("/employer/track-candidates?tab=Shortlisted")}
      />
      <StatCard
        title="Unlocked"
        value={unlockedCandidatesCount}
        borderColor="border-orange-500"
        icon={
          <svg className="w-5 h-5 text-orange-500" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
          </svg>
        }
        onClick={() => navigate("/employer/track-candidates?tab=Unlocked")}
      />
      <StatCard
        title="Interviews"
        value={InterviewedCandidatesCount}
        borderColor="border-green-500"
        icon={
          <svg className="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clipRule="evenodd" />
          </svg>
        }
        onClick={() => navigate("/employer/track-candidates?tab=Interview")}
      />
      <StatCard
        title="Offers"
        value={offeredCandidatesCount}
        borderColor="border-blue-500"
        icon={
          <svg className="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
        }
        onClick={() => navigate("/employer/track-candidates?tab=Offers")}
      />
    </div>
  </div>
);

export default StatsOverview;