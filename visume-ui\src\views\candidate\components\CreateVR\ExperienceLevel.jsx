import React from "react";
import { HiStar, HiAcademicCap, HiBriefcase, HiOutlineSparkles } from "react-icons/hi";
import { CheckCircle } from "lucide-react";

const ExperienceLevel = ({
  inviteDataToUse,
  formData,
  handleInputChange,
  salaryOptions,
  setFormData,
}) => (
  <div className="space-y-6">
    <div className="text-center">
      <div className="mx-auto mb-4 flex h-14 w-14 items-center justify-center rounded-lg bg-blue-50 border border-blue-100">
        <HiStar className="h-7 w-7 text-blue-600" />
      </div>
      <h1 className="mb-2 text-2xl font-semibold text-gray-900">
        Experience Level
      </h1>
      <p className="text-gray-600">
        {inviteDataToUse
          ? "Experience level from employer invitation"
          : "Select your professional experience level"}
      </p>
      {inviteDataToUse && (
        <div className="mt-4 rounded-lg border border-blue-200 bg-blue-50 p-4">
          <div className="flex items-center justify-center gap-3">
            <HiOutlineSparkles className="h-5 w-5 text-blue-600" />
            <span className="font-medium text-blue-800">
              Pre-selected: {inviteDataToUse.experience}
            </span>
          </div>
        </div>
      )}
    </div>

    <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
      {/* Fresher */}
      <label
        className={`relative cursor-pointer rounded-lg border-2 p-4 transition-all duration-200 ${
          formData.experience === "0-1"
            ? "border-blue-500 bg-blue-50 shadow-sm"
            : "border-gray-200 bg-white hover:border-blue-300 hover:shadow-sm"
        }`}
      >
        <input
          type="radio"
          name="experience"
          value="0-1"
          checked={formData.experience === "0-1"}
          onChange={handleInputChange}
          className="sr-only"
        />
        <div className="text-center">
          <div
            className={`mx-auto mb-3 flex h-10 w-10 items-center justify-center rounded-lg ${
              formData.experience === "0-1"
                ? "bg-blue-100"
                : "bg-gray-100"
            }`}
          >
            <HiAcademicCap
              className={`h-6 w-6 ${
                formData.experience === "0-1"
                  ? "text-blue-600"
                  : "text-gray-500"
              }`}
            />
          </div>
          <h3 className="mb-2 font-semibold text-gray-900">
            Fresher
          </h3>
          <p className="text-sm text-gray-600">
            0-1 years
          </p>
        </div>
        {formData.experience === "0-1" && (
          <div className="absolute right-3 top-3">
            <CheckCircle className="h-5 w-5 text-blue-600" />
          </div>
        )}
      </label>

      {/* Intermediate */}
      <label
        className={`relative cursor-pointer rounded-lg border-2 p-4 transition-all duration-200 ${
          formData.experience === "2-3"
            ? "border-blue-500 bg-blue-50 shadow-sm"
            : "border-gray-200 bg-white hover:border-blue-300 hover:shadow-sm"
        }`}
      >
        <input
          type="radio"
          name="experience"
          value="2-3"
          checked={formData.experience === "2-3"}
          onChange={handleInputChange}
          className="sr-only"
        />
        <div className="text-center">
          <div
            className={`mx-auto mb-3 flex h-10 w-10 items-center justify-center rounded-lg ${
              formData.experience === "2-3"
                ? "bg-blue-100"
                : "bg-gray-100"
            }`}
          >
            <HiBriefcase
              className={`h-6 w-6 ${
                formData.experience === "2-3"
                  ? "text-blue-600"
                  : "text-gray-500"
              }`}
            />
          </div>
          <h3 className="mb-2 font-semibold text-gray-900">
            Intermediate
          </h3>
          <p className="text-sm text-gray-600">
            2-3 years
          </p>
        </div>
        {formData.experience === "2-3" && (
          <div className="absolute right-3 top-3">
            <CheckCircle className="h-5 w-5 text-blue-600" />
          </div>
        )}
      </label>

      {/* Experienced */}
      <label
        className={`relative cursor-pointer rounded-lg border-2 p-4 transition-all duration-200 ${
          formData.experience === "3-5"
            ? "border-blue-500 bg-blue-50 shadow-sm"
            : "border-gray-200 bg-white hover:border-blue-300 hover:shadow-sm"
        }`}
      >
        <input
          type="radio"
          name="experience"
          value="3-5"
          checked={formData.experience === "3-5"}
          onChange={handleInputChange}
          className="sr-only"
        />
        <div className="text-center">
          <div
            className={`mx-auto mb-3 flex h-10 w-10 items-center justify-center rounded-lg ${
              formData.experience === "3-5"
                ? "bg-blue-100"
                : "bg-gray-100"
            }`}
          >
            <HiStar
              className={`h-6 w-6 ${
                formData.experience === "3-5"
                  ? "text-blue-600"
                  : "text-gray-500"
              }`}
            />
          </div>
          <h3 className="mb-2 font-semibold text-gray-900">
            Experienced
          </h3>
          <p className="text-sm text-gray-600">
            3-5 years
          </p>
        </div>
        {formData.experience === "3-5" && (
          <div className="absolute right-3 top-3">
            <CheckCircle className="h-5 w-5 text-blue-600" />
          </div>
        )}
      </label>
    </div>

    {/* Conditional Salary Fields */}
    {(formData.experience === "2-3" || formData.experience === "3-5") && (
      <div className="mt-6 space-y-4">
        {/* Current Salary */}
        <div>
          <label className="mb-2 block text-sm font-semibold text-gray-900">
            Current Salary
          </label>
          <select
            value={formData.salary.current}
            onChange={(e) =>
              setFormData({
                ...formData,
                salary: {
                  ...formData.salary,
                  current: e.target.value,
                  expected: formData.salary.expected,
                },
              })
            }
            className="w-full rounded-lg border border-gray-300 bg-white px-4 py-3 text-gray-900 focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-100"
          >
            <option value="">Select current salary</option>
            {salaryOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* Expected Salary */}
        <div>
          <label className="mb-2 block text-sm font-semibold text-gray-900">
            Expected Salary
          </label>
          <select
            value={formData.salary.expected}
            onChange={(e) =>
              setFormData({
                ...formData,
                salary: {
                  ...formData.salary,
                  current: formData.salary.current,
                  expected: e.target.value,
                },
              })
            }
            className="w-full rounded-lg border border-gray-300 bg-white px-4 py-3 text-gray-900 focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-100"
          >
            <option value="">Select expected salary</option>
            {salaryOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>
    )}
  </div>
);

export default ExperienceLevel;