import React, { useState, useRef, useEffect } from "react";
import LogoImage from "assets/img/Visume-logo-icon.png";
import videoRes from "assets/img/videores-illustration.png";
import { useNavigate } from "react-router-dom";
import toast from "react-hot-toast";
import Cookies from "js-cookie";
import useEmailValidation from "../../../hooks/useEmailValidation";
import CreateAccountForm from "./CreateAccountForm";

import { useLocation } from "react-router-dom";

const CreateAccount = () => {
  const location = useLocation();
  const googleData = location.state || {};
  const [isLoading, setIsLoading] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [uploadedFile, setUploadedFile] = useState(null);

  // Profile picture state
  const [profilePic, setProfilePic] = useState(null);
  const [profilePicPreview, setProfilePicPreview] = useState(null);
  const [profilePicError, setProfilePicError] = useState("");
  const profilePicInputRef = useRef(null);
  const [imageError, setImageError] = useState(false);

  // Remove profile picture handler
  const handleRemoveProfilePic = () => {
    setProfilePic(null);
    setProfilePicPreview(null);
    setProfilePicError("");
    if (profilePicInputRef.current) {
      profilePicInputRef.current.value = "";
    }
  };

  const jsregcookie = (data) => {
    const allCookies = Cookies.get(); // Get all cookies
    for (const cookieName in allCookies) {
      Cookies.remove(cookieName); // Remove each cookie
    }
    // Only clear localStorage except pendingInviteData
    const pendingInvite = localStorage.getItem("pendingInviteData");
    Object.keys(localStorage).forEach((key) => {
      if (key !== "pendingInviteData") localStorage.removeItem(key);
    });
    Cookies.set("candId", data.cand_id, { expires: 7 });
    Cookies.set("jstoken", data.token, { expires: 7 });
    Cookies.set("role", data.role, { expires: 7 });
  };

  const handleFileUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      const isPdf = file.type === "application/pdf";
      if (isPdf) {
        setFormData({ ...formData, resume: file });
        setUploadedFile(file);
        setErrors({ ...errors, resume: "" });
      } else {
        setErrors({ ...errors, resume: "Please upload a valid PDF file." });
        setUploadedFile(null); 
      }
    }
  };

  // Profile picture handler
  const handleProfilePicChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      const validTypes = ["image/jpeg", "image/png", "image/jpg"];
      if (!validTypes.includes(file.type)) {
        setProfilePicError("Only JPG, JPEG, or PNG files are allowed.");
        setProfilePic(null);
        setProfilePicPreview(null);
        return;
      }
      if (file.size > 5 * 1024 * 1024) {
        setProfilePicError("File size must be less than 5MB.");
        setProfilePic(null);
        setProfilePicPreview(null);
        return;
      }
      setProfilePic(file);
      setProfilePicPreview(URL.createObjectURL(file));
      setProfilePicError("");
    }
  };

  const [formData, setFormData] = useState({
    fullName: googleData.name || "",
    email: googleData.email || "",
    password: "",
    mobile: "",
    gender: "",
    languages: [],
    locations: [],
    resume: null,
  });

  const [errors, setErrors] = useState({});
  const [selectedLanguages, setSelectedLanguages] = useState([]);
  const [selectedLocations, setSelectedLocations] = useState([]);
  const [isLanguageOpen, setIsLanguageOpen] = useState(false);
  const [isLocationOpen, setIsLocationOpen] = useState(false);

  // Email validation using custom hook
  const { emailValidation, validateEmail, resetValidation } =
    useEmailValidation();

  const languageDropdownRef = useRef(null);
  const locationDropdownRef = useRef(null);

  const [showSendOTPButton, setShowSendOTPButton] = useState(false);
  const [showOTPModal, setShowOTPModal] = useState(false);
  const [otp, setOTP] = useState(["", "", "", ""]);
  const [generatedOTP, setGeneratedOTP] = useState("");
  const [otpVerified, setOtpVerified] = useState(false);
  const [otpError, setOtpError] = useState("");

  // Phone validation states
  const [phoneAvailable, setPhoneAvailable] = useState(null);
  const [phoneCheckLoading, setPhoneCheckLoading] = useState(false);
  const [phoneCheckMsg, setPhoneCheckMsg] = useState("");

  const languageOptions = [
    "English",
    "Hindi",
    "Tamil",
    "Telugu",
    "Malayalam",
    "Kannada",
  ];
  const locationOptions = ["Bangalore", "Mumbai", "Delhi", "Hyderabad"];

  const [profilePicPrefilled, setProfilePicPrefilled] = useState(false);

  useEffect(() => {
    // Prefill profile picture preview from Google only once
    if (
      googleData.picture &&
      !profilePicPreview &&
      !profilePic &&
      !profilePicPrefilled
    ) {
      setProfilePicPreview(googleData.picture);
      setProfilePicPrefilled(true);
    }

    // Only prefill name/email if empty (first mount)
    // Prevent infinite update loop by checking if values actually need to change
    setFormData((prev) => {
      const updated = {
        ...prev,
        fullName: prev.fullName || googleData.name || "",
        email: prev.email || googleData.email || "",
      };
      if (prev.fullName === updated.fullName && prev.email === updated.email) {
        return prev;
      }
      return updated;
    });

    const handleClickOutside = (event) => {
      if (
        languageDropdownRef.current &&
        !languageDropdownRef.current.contains(event.target)
      ) {
        setIsLanguageOpen(false);
      }
      if (
        locationDropdownRef.current &&
        !locationDropdownRef.current.contains(event.target)
      ) {
        setIsLocationOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [
    googleData,
    googleData.picture,
    profilePicPreview,
    profilePic,
    profilePicPrefilled,
  ]);

  // Phone validation useEffect
  useEffect(() => {
    console.log("Phone useEffect triggered with:", formData.mobile, "length:", formData.mobile?.length);
    
    const checkPhone = async () => {
      if (!formData.mobile || formData.mobile.length !== 10) {
        console.log("Phone validation skipped - empty or wrong length");
        setPhoneAvailable(null);
        setPhoneCheckMsg("");
        return;
      }
      console.log("Starting phone check for:", formData.mobile);
      setPhoneCheckLoading(true);
      try {
        const url = `${import.meta.env.VITE_APP_HOST}/api/v1/check-phone`;
        console.log("Making request to:", url);
        const response = await fetch(url, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ phone: formData.mobile }),
        });
        const data = await response.json();
        console.log("Phone check response:", { status: response.status, data });
        if (response.ok && data.available) {
          setPhoneAvailable(true);
          setPhoneCheckMsg("");
        } else if (response.status === 409) {
          setPhoneAvailable(false);
          setPhoneCheckMsg(
            data.message || "Phone number already exists. Please use a different number."
          );
        } else {
          setPhoneAvailable(null);
          setPhoneCheckMsg(data.message || "Error checking phone number.");
        }
      } catch (err) {
        console.error("Phone check error:", err);
        setPhoneAvailable(null);
        setPhoneCheckMsg("Error checking phone number.");
      } finally {
        setPhoneCheckLoading(false);
      }
    };
    checkPhone();
  }, [formData.mobile]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
    setErrors({ ...errors, [name]: "" });

    // Debounced email validation
    if (name === "email") {
      validateEmail(value);
    }
  };

  const handlePhoneNumberChange = (e) => {
    const value = e.target.value.replace(/\D/g, "").slice(0, 10);
    if (e.target.value !== value) {
      toast.error("Please enter only numbers.");
    }
    setFormData({ ...formData, mobile: value });
    setShowSendOTPButton(value.length === 10);
    if (value.length !== 10) {
      setOtpVerified(false);
      setOtpError("");
    }
    setErrors({ ...errors, mobile: "" });
  };

  const handleSendOTPClick = (e) => {
    e.preventDefault();
    const newOTP = Math.floor(1000 + Math.random() * 9000).toString();
    setGeneratedOTP(newOTP);
    alert(`Your OTP is: ${newOTP}`); // In a real app, this would be sent via SMS
    setShowOTPModal(true);
    setOTP(["", "", "", ""]);
    setOtpVerified(false);
    setOtpError("");
  };

  const handleOTPChange = (index, value) => {
    const newOTP = [...otp];
    newOTP[index] = value.replace(/\D/g, "").slice(0, 1);
    setOTP(newOTP);

    if (value && index < 3) {
      const nextInput = document.getElementById(`otp-${index + 1}`);
      if (nextInput) nextInput.focus();
    }
  };

  const handleVerifyOTP = () => {
    const enteredOTP = otp.join("");
    if (enteredOTP === generatedOTP) {
      setOtpVerified(true);
      setOtpError("");
      setShowOTPModal(false);
    } else {
      setOtpVerified(false);
      setOtpError("Wrong OTP entered. Please try again.");
    }
  };

  const toggleLanguage = (language) => {
    const updatedLanguages = selectedLanguages.includes(language)
      ? selectedLanguages.filter((lang) => lang !== language)
      : [...selectedLanguages, language];
    setSelectedLanguages(updatedLanguages);
    setFormData({ ...formData, languages: updatedLanguages });
    setErrors({ ...errors, languages: "" });
  };

  const toggleLocation = (location) => {
    const updatedLocations = selectedLocations.includes(location)
      ? selectedLocations.filter((loc) => loc !== location)
      : [...selectedLocations, location];
    setSelectedLocations(updatedLocations);
    setFormData({ ...formData, locations: updatedLocations });
    setErrors({ ...errors, locations: "" });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const newErrors = {};

    // Validate required fields
    if (!formData.fullName) newErrors.fullName = "Full name is required";
    if (!formData.email) newErrors.email = "Email is required";
    if (!formData.password) newErrors.password = "Password is required";
    if (!formData.mobile) newErrors.mobile = "Mobile number is required";
    if (!formData.gender) newErrors.gender = "Gender is required";
    if (selectedLanguages.length === 0)
      newErrors.languages = "Please select at least one language";
    if (selectedLocations.length === 0)
      newErrors.locations = "Please select at least one location";
    if (!formData.resume) newErrors.resume = "Please upload your resume";
    // Profile picture is optional, but validate if present
    if (profilePic) {
      const validTypes = ["image/jpeg", "image/png", "image/jpg"];
      if (!validTypes.includes(profilePic.type)) {
        newErrors.profilePic = "Only JPG, JPEG, or PNG files are allowed.";
      }
      if (profilePic.size > 5 * 1024 * 1024) {
        newErrors.profilePic = "File size must be less than 5MB.";
      }
    }

    // Validate phone number verification
    if (phoneAvailable === false) {
      newErrors.mobile = "Phone number already exists. Please use a different number.";
    } else if (!otpVerified) {
      newErrors.mobile = "Please verify your phone number";
    }

    // Validate email uniqueness
    if (emailValidation.isChecking) {
      newErrors.email = "Please wait while we check email availability";
    } else if (emailValidation.isValid === false) {
      newErrors.email = emailValidation.message || "Email validation failed";
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
    } else {
      // Form is valid, proceed with submission
      console.log("Form submitted:", formData);
      const finalFormData = {
        email: formData.email,
        password: formData.password,
        cand_name: formData.fullName,
        cand_mobile: formData.mobile,
        gender: formData.gender,
        languages_known: JSON.stringify(formData.languages),
        preferred_location: JSON.stringify(formData.locations),
        resume: formData.resume ? formData.resume : null,
        profile_pic: profilePic ? profilePic : null,
        profile_pic_url:
          !profilePic &&
          profilePicPreview &&
          typeof profilePicPreview === "string"
            ? profilePicPreview
            : null,
      };
      registerJobseeker(finalFormData);
    }
  };

  async function registerJobseeker(formData) {
    setIsProcessing(true);
    // Create a new FormData object
    const formPayload = new FormData();

    // Append the fields to the FormData object
    formPayload.append("email", formData.email);
    formPayload.append("password", formData.password);
    formPayload.append("cand_name", formData.cand_name);
    formPayload.append("cand_mobile", formData.cand_mobile);
    formPayload.append("gender", formData.gender);
    formPayload.append("languages_known", formData.languages_known);
    formPayload.append("preferred_location", formData.preferred_location);

    // Append the marks object as a JSON string
    formPayload.append("marks", JSON.stringify(formData.marks || {}));

    // Append files if they exist
    if (formData.resume) {
      formPayload.append("resume", formData.resume);
    }
    if (formData.profile_pic) {
      formPayload.append("profile_picture", formData.profile_pic);
    }
    if (formData.profile_pic_url) {
      formPayload.append("profile_pic_url", formData.profile_pic_url);
    }

    try {
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/register-jobseeker`,
        {
          method: "POST",
          body: formPayload,
        }
      );

      const resdata = await response.json();
      console.log(resdata);

      if (response.ok) {
        jsregcookie(resdata);
        toast.success("Registered Successfully");

        // Preserve invite data in localStorage after registration
        const params = new URLSearchParams(location.search);
        let inviteData = null;
        if (params.get("jobRole")) {
          inviteData = {
            jobRole: params.get("jobRole"),
            skills: params.get("skills") ? params.get("skills").split(",") : [],
            experience: params.get("experience"),
            location: params.get("location"),
            companyType: params.get("companyType"),
            empId: params.get("empId"),
            jobId: params.get("jobId"),
          };
        }
        if (inviteData) {
          localStorage.setItem("pendingInviteData", JSON.stringify(inviteData));
        }
        
        navigate("/candidate/dashboard");
      } else if (response.status === 400) {
        throw new Error(resdata.message || "Invalid request");
      } else if (response.status === 409) {
        throw new Error(resdata.message || "Email already exists");
      } else if (response.status === 500) {
        throw new Error(resdata.message || "Server error");
      } else {
        throw new Error(resdata.message || "An unexpected error occurred");
      }
    } catch (error) {
      console.error("Registration error:", error);
      toast.error(error.message || "Something went wrong");
    } finally {
      setIsProcessing(false);
    }

  }
  const navigate = useNavigate();

  return (
    <CreateAccountForm
      isProcessing={isProcessing}
      navigate={navigate}
      LogoImage={LogoImage}
      videoRes={videoRes}
      profilePicPreview={profilePicPreview}
      imageError={imageError}
      setImageError={setImageError}
      profilePic={profilePic}
      profilePicError={profilePicError}
      errors={errors}
      handleProfilePicChange={handleProfilePicChange}
      profilePicInputRef={profilePicInputRef}
      handleRemoveProfilePic={handleRemoveProfilePic}
      formData={formData}
      setFormData={setFormData}
      handleInputChange={handleInputChange}
      emailValidation={emailValidation}
      selectedLanguages={selectedLanguages}
      isLanguageOpen={isLanguageOpen}
      setIsLanguageOpen={setIsLanguageOpen}
      languageOptions={languageOptions}
      languageDropdownRef={languageDropdownRef}
      toggleLanguage={toggleLanguage}
      selectedLocations={selectedLocations}
      isLocationOpen={isLocationOpen}
      setIsLocationOpen={setIsLocationOpen}
      locationOptions={locationOptions}
      locationDropdownRef={locationDropdownRef}
      toggleLocation={toggleLocation}
      handlePhoneNumberChange={handlePhoneNumberChange}
      showSendOTPButton={showSendOTPButton}
      otpVerified={otpVerified}
      handleSendOTPClick={handleSendOTPClick}
      otp={otp}
      otpError={otpError}
      handleOTPChange={handleOTPChange}
      handleVerifyOTP={handleVerifyOTP}
      handleFileUpload={handleFileUpload}
      uploadedFile={uploadedFile}
      showOTPModal={showOTPModal}
      setShowOTPModal={setShowOTPModal}
      phoneAvailable={phoneAvailable}
      phoneCheckLoading={phoneCheckLoading}
      phoneCheckMsg={phoneCheckMsg}
      handleSubmit={handleSubmit}
    />
  );
};

export default CreateAccount;
