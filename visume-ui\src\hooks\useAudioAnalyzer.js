import { useState, useEffect, useRef } from 'react';

export const useAudioAnalyzer = (isListening) => {
  const [amplitudeData, setAmplitudeData] = useState(new Array(5).fill(0));
  const audioContextRef = useRef(null);
  const analyzerRef = useRef(null);
  const microphoneRef = useRef(null);
  const animationFrameRef = useRef(null);
  const smoothingDataRef = useRef(new Array(5).fill(0));

  useEffect(() => {
    if (isListening) {
      startAudioAnalysis();
    } else {
      stopAudioAnalysis();
    }

    return () => {
      stopAudioAnalysis();
    };
  }, [isListening]);

  const startAudioAnalysis = async () => {
    try {
      // Request microphone access
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        } 
      });

      // Create audio context and analyzer
      audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
      analyzerRef.current = audioContextRef.current.createAnalyser();
      microphoneRef.current = audioContextRef.current.createMediaStreamSource(stream);

      // Configure analyzer
      analyzerRef.current.fftSize = 512;
      analyzerRef.current.smoothingTimeConstant = 0.3;
      
      // Connect microphone to analyzer
      microphoneRef.current.connect(analyzerRef.current);

      // Start analyzing
      analyzeAudio();
    } catch (error) {
      console.error('Error accessing microphone:', error);
      // Fallback to static animation
      setAmplitudeData(new Array(5).fill(0.3));
    }
  };

  const analyzeAudio = () => {
    if (!analyzerRef.current) return;

    const bufferLength = analyzerRef.current.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);
    
    const processAudio = () => {
      if (!analyzerRef.current) return;

      analyzerRef.current.getByteFrequencyData(dataArray);
      
      // Create 5 frequency bands for the waveform bars
      const bands = [];
      const bandSize = Math.floor(bufferLength / 5);
      
      for (let i = 0; i < 5; i++) {
        const startIndex = i * bandSize;
        const endIndex = startIndex + bandSize;
        let sum = 0;
        
        for (let j = startIndex; j < endIndex; j++) {
          sum += dataArray[j];
        }
        
        // Normalize to 0-1 range and apply some scaling for better visual effect
        const average = sum / bandSize;
        const normalized = Math.min(average / 255, 1);
        const scaled = Math.max(normalized * 2, 0.1); // Minimum height for visual consistency
        
        bands.push(scaled);
      }
      
      // Apply smoothing to reduce jitter
      const smoothingFactor = 0.7;
      const smoothedBands = bands.map((band, i) => {
        const previousValue = smoothingDataRef.current[i];
        const smoothedValue = previousValue * smoothingFactor + band * (1 - smoothingFactor);
        smoothingDataRef.current[i] = smoothedValue;
        return smoothedValue;
      });
      
      setAmplitudeData(smoothedBands);
      animationFrameRef.current = requestAnimationFrame(processAudio);
    };

    processAudio();
  };

  const stopAudioAnalysis = () => {
    // Cancel animation frame
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }

    // Close audio context
    if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }

    // Stop microphone stream
    if (microphoneRef.current && microphoneRef.current.mediaStream) {
      microphoneRef.current.mediaStream.getTracks().forEach(track => track.stop());
      microphoneRef.current = null;
    }

    // Reset analyzer
    analyzerRef.current = null;

    // Reset amplitude data and smoothing
    setAmplitudeData(new Array(5).fill(0));
    smoothingDataRef.current = new Array(5).fill(0);
  };

  return amplitudeData;
};