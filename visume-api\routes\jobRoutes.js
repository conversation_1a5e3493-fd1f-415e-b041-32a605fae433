// Invite context API
const { getInviteContext } = require('../controllers/inviteController');
const express = require('express');
const router = express.Router();
const jobController = require('../controllers/jobController');

// Debug middleware for job routes
router.use((req, res, next) => {
  console.log(`[DEBUG] Job route middleware: ${req.method} ${req.path}`);
  next();
});

// Get suggested jobs
router.get('/suggestedJobs', (req, res, next) => {
  console.log('[DEBUG] Reached suggestedJobs route handler');
  jobController.getSuggestedJobs(req, res, next);
});

// GET invitation context by inviteId
router.get('/invite/:inviteId', getInviteContext);

module.exports = router;
