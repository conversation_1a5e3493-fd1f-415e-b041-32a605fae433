require('dotenv').config({ path: '.env' });
const express = require("express");
const bodyParser = require("body-parser");
const userRoutes = require("./routes/authRoutes");
const r2Routes = require("./routes/r2Routes");
const jobRoutes = require("./routes/jobRoutes");
const questionRoutes = require("./routes/questionRoutes");
const transcriptionRoutes = require("./routes/transcriptionRoutes");
const path = require("path");
const geminiRoutes = require("./routes/geminiRoutes");
const waitlistRoutes = require("./routes/waitlistRoutes");
const cors = require("cors");

const app = express();

// Configure CORS with specific options
app.use(cors({
  origin: function(origin, callback) {
    // Allow requests with no origin (like mobile apps, curl, etc)
    if (!origin) return callback(null, true);

    const allowedOrigins = [
      'http://localhost:3000',
      'http://localhost:5173', // Vite default port
      'https://visume.co.in',
    ];

    if (allowedOrigins.indexOf(origin) === -1) {
      console.warn('CORS blocked request from:', origin);
      return callback(null, false);
    }
    return callback(null, true);
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'x-session-token'],
  // Expose ETag header for multipart upload functionality
  exposedHeaders: ['ETag', 'Content-Length', 'Content-Type']
}));
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(
  "/utils/files/profile_pics",
  express.static(path.join(__dirname, "utils/files/profile_pics"))
);

// Enhanced request logging middleware
app.use((req, res, next) => {
  const start = Date.now();
  // Log request details
  // console.log(`[DEBUG] ${req.method} ${req.url}`);
  // console.log('[DEBUG] Origin:', req.get('origin'));
  // console.log('[DEBUG] Headers:', JSON.stringify(req.headers, null, 2));

  // Log response time on finish
  res.on('finish', () => {
    const duration = Date.now() - start;
    console.log(`[DEBUG] ${req.method} ${req.url} completed in ${duration}ms with status ${res.statusCode}`);
  });

  next();
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(err.status || 500).json({
    error: 'Internal Server Error',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  });
});

app.get("/", (req, res) => {
  console.log("Server running");
  res.send("Server running");
});

// Add debug middleware to check if routes are mounted
app.use("/api/v1", (req, res, next) => {
  console.log(`[DEBUG] Reached /api/v1 middleware for ${req.method} ${req.url}`);
  next();
});

// Mount routes
app.use("/api/v1", jobRoutes);  // Mount job routes first
app.use("/api/v1", userRoutes);
app.use("/api/v1", r2Routes);
app.use("/api/v1", questionRoutes);
app.use("/api/v1", transcriptionRoutes);

app.use("/api/gemini-assist", geminiRoutes);

app.use(waitlistRoutes);

const PORT = process.env.PORT;
app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});
