"use client"
import { motion } from "framer-motion"
import { ArrowR<PERSON>, CheckCircle, Eye, Building2, Users, Briefcase } from "lucide-react"
import { useState, useEffect } from "react"
import { AnimatePresence } from "framer-motion"
import WaitlistModal from "./WaitlistModal"

const Hero = ({ activeView, setActiveView }) => {
  const [waitlisted, setWaitlisted] = useState(false)
  const [waitlistOpen, setWaitlistOpen] = useState(false)

  const handleWaitlist = () => {
    setWaitlistOpen(true)
  }

  const closeWaitlistModal = () => {
    setWaitlistOpen(false)
  }

  const onWaitlisted = () => {
    setWaitlisted(true)
  }

  const handleEmployerClick = () => {
    setActiveView("hiringmanager")
  }

  return (
    <>
      <WaitlistModal open={waitlistOpen} onClose={closeWaitlistModal} onWaitlisted={onWaitlisted} />
      <style>
        {`
          @import url('https://fonts.googleapis.com/css2?family=Manrope:wght@400;500;600;700;800&family=Sora:wght@400;500;600;700;800&display=swap');
          .font-sora {
            font-family: 'Sora', system-ui, sans-serif;
          }
          .font-manrope {
            font-family: 'Manrope', system-ui, sans-serif;
          }
          
          .gradient-text {
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #1e40af 100%);
            background-size: 200% 200%;
            animation: gradientShift 6s ease infinite;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
          }
          
          @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
          }
          
          .hero-bottom-gradient {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 200px;
            background: linear-gradient(to top, rgba(59, 130, 246, 0.1) 0%, rgba(59, 130, 246, 0.05) 50%, transparent 100%);
            pointer-events: none;
          }
          @media (max-width: 768px) {
            .hero-bottom-gradient {
              height: 100px;
            }
          }
          
          .text-shadow {
            text-shadow: 0 2px 4px rgba(0,0,0,0.05);
          }
          .premium-button {
            position: relative;
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 
              0 8px 32px rgba(59, 130, 246, 0.3),
              0 1px 0 rgba(255, 255, 255, 0.1) inset,
              0 -1px 0 rgba(0, 0, 0, 0.1) inset;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          }
          .premium-button:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            box-shadow: 
              0 12px 40px rgba(59, 130, 246, 0.4),
              0 1px 0 rgba(255, 255, 255, 0.15) inset,
              0 -1px 0 rgba(0, 0, 0, 0.1) inset;
            transform: translateY(-2px);
          }
          .premium-button::before {
            content: '';
            position: absolute;
            inset: 0;
            border-radius: inherit;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
            opacity: 0;
            transition: opacity 0.3s ease;
          }
          .premium-button:hover::before {
            opacity: 1;
          }
          .premium-button-green {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 
              0 8px 32px rgba(16, 185, 129, 0.3),
              0 1px 0 rgba(255, 255, 255, 0.1) inset,
              0 -1px 0 rgba(0, 0, 0, 0.1) inset;
          }
          .premium-button-secondary {
            position: relative;
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid rgba(59, 130, 246, 0.3);
            color: #3b82f6;
            box-shadow: 
              0 4px 16px rgba(59, 130, 246, 0.15),
              0 1px 0 rgba(255, 255, 255, 0.8) inset;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          }
          .premium-button-secondary:hover {
            background: rgba(59, 130, 246, 0.05);
            border-color: rgba(59, 130, 246, 0.5);
            color: #2563eb;
            box-shadow: 
              0 8px 24px rgba(59, 130, 246, 0.25),
              0 1px 0 rgba(255, 255, 255, 0.9) inset;
            transform: translateY(-2px);
          }
          .premium-card {
            position: relative;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(148, 163, 184, 0.4);
            border-radius: 16px;
            box-shadow:
              0 20px 25px -5px rgba(0, 0, 0, 0.15),
              0 10px 10px -5px rgba(0, 0, 0, 0.08),
              0 0 0 1px rgba(255, 255, 255, 0.1) inset;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          }
          .premium-card::before {
            content: '';
            position: absolute;
            inset: -40px;
            background: radial-gradient(ellipse at center, rgba(30, 58, 138, 0.08) 0%, rgba(59, 130, 246, 0.05) 50%, transparent 70%);
            border-radius: 50%;
            z-index: -1;
            opacity: 1;
            transition: opacity 0.4s ease;
          }
          .premium-card:hover::before {
            opacity: 1;
          }
          .premium-card:hover {
            transform: translateY(-8px);
            box-shadow: 
              0 32px 64px -12px rgba(30, 58, 138, 0.2),
              0 20px 25px -5px rgba(0, 0, 0, 0.15),
              0 0 0 1px rgba(255, 255, 255, 0.15) inset;
          }
          .smooth-transition {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          }
          .illustration-float {
            animation: float 6s ease-in-out infinite;
          }
          @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
          }
          @media (max-width: 768px) {
            .illustration-float {
              animation: none;
            }
          }
          .trust-indicator {
            position: relative;
            overflow: hidden;
          }
          .trust-indicator::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            animation: shimmer 3s infinite;
          }
          @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
          }
          .enhanced-shadow {
            filter: drop-shadow(0 25px 50px rgba(0, 0, 0, 0.15));
          }
          .grid-cols-2 {
            grid-template-columns: 1fr 1fr;
          }
          @media (max-width: 1023px) {
            .grid-cols-2 {
              grid-template-columns: 1fr;
              gap: 2rem;
            }
          }
        `}
      </style>
      <section className="relative min-h-screen overflow-hidden bg-white">
        <div className="relative z-10 mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 pt-16 sm:pt-20 lg:pt-28 pb-2 sm:pb-4">
          {/* Hero Content */}
          <div className="max-w-6xl mx-auto">
            {/* Desktop Layout */}
            <div className="hidden lg:block mb-4 sm:mb-6">
              {/* Centered Hero Title */}
              <motion.div
                className="text-center mb-16"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1, duration: 0.8 }}
              >
                <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight text-slate-900 font-sora text-shadow">
                  Land your dream job with
                  <br />
                  <span className="gradient-text">video resumes that work</span>
                </h1>
                {/* CTA Buttons */}
                <motion.div
                  className="flex items-center gap-4 justify-center flex-wrap mt-12"
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3, duration: 0.8 }}
                >
                  <motion.button
                    className={`group relative flex items-center justify-center gap-2 rounded-lg px-6 py-3 font-semibold whitespace-nowrap font-manrope text-sm overflow-hidden shadow-md ${
                      waitlisted
                        ? "premium-button-green text-white cursor-not-allowed"
                        : "premium-button text-white border-0"
                    }`}
                    whileHover={waitlisted ? {} : { scale: 1.02 }}
                    whileTap={waitlisted ? {} : { scale: 0.98 }}
                    onClick={waitlisted ? undefined : handleWaitlist}
                    disabled={waitlisted}
                  >
                    <span className="relative z-10 flex items-center gap-3">
                      {waitlisted ? (
                        <>
                          Waitlisted
                          <CheckCircle className="h-4 w-4" />
                        </>
                      ) : (
                        <>
                          Join Waitlist
                          <ArrowRight className="h-4 w-4 group-hover:translate-x-0.5 transition-transform duration-200" />
                        </>
                      )}
                    </span>
                  </motion.button>
                  <motion.button
                    className="group relative flex items-center justify-center gap-2 rounded-lg px-6 py-3 font-semibold whitespace-nowrap font-manrope text-sm overflow-hidden premium-button-secondary"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={handleEmployerClick}
                  >
                    <span className="relative z-10 flex items-center gap-3">
                      <Briefcase className="h-4 w-4" />
                      For Employers
                    </span>
                  </motion.button>
                </motion.div>
                {/* Trust signals */}
                <motion.div
                  className="flex items-center justify-center gap-6 text-sm text-gray-500 font-manrope trust-indicator mt-8"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5, duration: 0.8 }}
                >
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse shadow-sm"></div>
                    <span className="font-medium">10,000+ job seekers</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse shadow-sm"></div>
                    <span className="font-medium">3x more interviews</span>
                  </div>
                </motion.div>
              </motion.div>
              
              {/* Hero Visual Composition */}
              <div className="flex items-center justify-center gap-16 relative z-20">
                {/* Left - Illustration */}
                <motion.div
                  className="flex justify-center"
                  initial={{ opacity: 0, x: -30 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.4, duration: 0.8 }}
                >
                  <div className="relative illustration-float">
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5 rounded-full blur-2xl scale-110"></div>
                    <img
                      src="/devhero.png"
                      alt="Job seeker creating video resume"
                      className="relative z-10 w-full max-w-sm lg:max-w-md h-auto"
                      style={{ objectFit: "contain" }}
                    />
                  </div>
                </motion.div>

                {/* Right - Animated Card */}
                <motion.div
                  className="flex justify-center"
                  initial={{ opacity: 0, x: 30 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.6, duration: 0.8 }}
                >
                  <JobSeekerCard />
                </motion.div>
              </div>
            </div>
            {/* Mobile Layout */}
            <div className="lg:hidden mb-4 sm:mb-6">
              {/* Centered Hero Title */}
              <motion.div
                className="text-center mb-12"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1, duration: 0.8 }}
              >
                <h1 className="text-2xl xs:text-3xl sm:text-4xl font-bold leading-tight text-slate-900 font-sora text-shadow mb-4 sm:mb-6 px-2">
                  Land your dream job with <span className="gradient-text">video resumes that work</span>
                </h1>
                {/* CTA Buttons */}
                <motion.div
                  className="flex flex-col sm:flex-row items-center justify-center gap-3 sm:gap-4 mb-4 sm:mb-6 px-4"
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3, duration: 0.8 }}
                >
                  <motion.button
                    className={`group relative flex items-center justify-center gap-2 rounded-lg px-5 sm:px-6 py-2.5 sm:py-3 font-semibold whitespace-nowrap font-manrope text-sm overflow-hidden w-full sm:w-auto max-w-xs sm:max-w-none ${
                      waitlisted
                        ? "premium-button-green text-white cursor-not-allowed"
                        : "premium-button text-white border-0"
                    }`}
                    whileHover={waitlisted ? {} : { scale: 1.02 }}
                    whileTap={waitlisted ? {} : { scale: 0.98 }}
                    onClick={waitlisted ? undefined : handleWaitlist}
                    disabled={waitlisted}
                  >
                    <span className="relative z-10 flex items-center gap-2">
                      {waitlisted ? (
                        <>
                          Waitlisted
                          <CheckCircle className="h-4 w-4" />
                        </>
                      ) : (
                        <>
                          Join Waitlist
                          <ArrowRight className="h-4 w-4 group-hover:translate-x-0.5 transition-transform duration-200" />
                        </>
                      )}
                    </span>
                  </motion.button>
                  <motion.button
                    className="group relative flex items-center justify-center gap-2 rounded-lg px-5 sm:px-6 py-2.5 sm:py-3 font-semibold whitespace-nowrap font-manrope text-sm overflow-hidden premium-button-secondary w-full sm:w-auto max-w-xs sm:max-w-none"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={handleEmployerClick}
                  >
                    <span className="relative z-10 flex items-center gap-2">
                      <Briefcase className="h-4 w-4" />
                      For Employers
                    </span>
                  </motion.button>
                </motion.div>
                {/* Trust signals */}
                <motion.div
                  className="flex flex-col xs:flex-row items-center justify-center gap-3 xs:gap-4 text-xs sm:text-sm text-gray-500 font-manrope trust-indicator px-4"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5, duration: 0.8 }}
                >
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse shadow-sm"></div>
                    <span className="font-medium whitespace-nowrap">10,000+ job seekers</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse shadow-sm"></div>
                    <span className="font-medium whitespace-nowrap">3x more interviews</span>
                  </div>
                </motion.div>
              </motion.div>

              {/* Mobile Visual Composition - Stacked */}
              <div className="space-y-4 sm:space-y-6 px-4">
                {/* Hero Illustration */}
                <motion.div
                  className="relative flex justify-center"
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4, duration: 0.8 }}
                >
                  <div className="relative illustration-float">
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-500/3 to-purple-500/3 rounded-full blur-xl scale-105"></div>
                    <img
                      src="/devhero.png"
                      alt="Job seeker creating video resume"
                      className="relative z-10 w-full max-w-64 sm:max-w-xs h-auto"
                      style={{ objectFit: "contain" }}
                    />
                  </div>
                </motion.div>

                {/* Animated Card */}
                <motion.div
                  className="flex justify-center"
                  initial={{ opacity: 0, y: 50 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6, duration: 0.8 }}
                >
                  <JobSeekerCard />
                </motion.div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  )
}

const JobSeekerCard = () => {
  const [currentViews, setCurrentViews] = useState(247)
  const [currentCompany, setCurrentCompany] = useState(0)

  const companies = [
    { name: "Google", logo: "🔍", color: "#4285f4" },
    { name: "Microsoft", logo: "🪟", color: "#00a1f1" },
    { name: "Apple", logo: "🍎", color: "#007aff" },
    { name: "Meta", logo: "📘", color: "#1877f2" },
    { name: "Netflix", logo: "🎬", color: "#e50914" },
    { name: "Spotify", logo: "🎵", color: "#1db954" },
  ]

  const notifications = [
    "Sarah from TechCorp viewed your Visume",
    "New message from StartupXYZ recruiter",
    "Your Visume was shared 3 times today",
    "5 companies bookmarked your profile",
    "Interview request from InnovateLab",
  ]

  const [currentNotification, setCurrentNotification] = useState(0)

  useEffect(() => {
    const viewsInterval = setInterval(() => {
      setCurrentViews((prev) => prev + Math.floor(Math.random() * 3) + 1)
    }, 3000)

    const companyInterval = setInterval(() => {
      setCurrentCompany((prev) => (prev + 1) % companies.length)
    }, 4000)

    const notificationInterval = setInterval(() => {
      setCurrentNotification((prev) => (prev + 1) % notifications.length)
    }, 5000)

    return () => {
      clearInterval(viewsInterval)
      clearInterval(companyInterval)
      clearInterval(notificationInterval)
    }
  }, [companies.length, notifications.length])

  return (
    <div className="relative group w-full max-w-sm sm:max-w-lg mx-auto">
      <motion.div
        className="premium-card relative overflow-hidden p-3 sm:p-4 lg:p-5 border-2 border-blue-400 transition-colors duration-300"
        style={{ 
          height: "400px",
          minHeight: "400px",
          transform: "translateY(-8px)",
          boxShadow: "0 20px 40px -12px rgba(59, 130, 246, 0.25), 0 8px 16px -4px rgba(59, 130, 246, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.15) inset"
        }}
        whileHover={{
          transition: {
            type: "spring",
            stiffness: 300,
            damping: 30,
          },
        }}
      >
        <div className="h-full flex flex-col">
          {/* Header */}
          <div className="mb-3">
            <div className="flex items-center gap-2 mb-2">
              <div>
                <h3 className="text-base sm:text-lg lg:text-xl font-bold text-slate-900 font-sora">For Job Seekers</h3>
                <p className="text-xs sm:text-sm text-slate-500 font-manrope">Your Visume is getting noticed</p>
              </div>
            </div>
          </div>

          {/* Views Counter */}
          <div className="flex items-center gap-2 p-2 sm:p-3 rounded-lg sm:rounded-xl border border-blue-200/60 mb-2 sm:mb-3 bg-blue-50/50 backdrop-blur-sm">
            <div className="relative">
              <div
                className="w-4 sm:w-5 h-4 sm:h-5 rounded-full flex items-center justify-center shadow-sm"
                style={{
                  background: "linear-gradient(135deg, #3b82f6, #1e40af)",
                }}
              >
                <Eye className="w-2 sm:w-2.5 h-2 sm:h-2.5 text-white" />
              </div>
            </div>
            <span className="text-slate-700 font-semibold font-manrope text-xs leading-tight">
              <AnimatePresence mode="wait">
                <motion.span
                  key={currentViews}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.3 }}
                >
                  {currentViews} people viewed your Visume
                </motion.span>
              </AnimatePresence>
            </span>
          </div>

          {/* Company Interest */}
          <div className="flex items-center gap-2 p-2 sm:p-3 rounded-lg sm:rounded-xl border border-emerald-200/60 mb-2 sm:mb-3 bg-emerald-50/50 backdrop-blur-sm">
            <div className="relative">
              <div
                className="w-4 sm:w-5 h-4 sm:h-5 rounded-full flex items-center justify-center shadow-sm"
                style={{
                  background: "linear-gradient(135deg, #10b981, #059669)",
                }}
              >
                <Building2 className="w-2 sm:w-2.5 h-2 sm:h-2.5 text-white" />
              </div>
            </div>
            <span className="text-slate-700 font-semibold font-manrope text-xs sm:text-sm">
              <AnimatePresence mode="wait">
                <motion.span
                  key={currentCompany}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.4 }}
                  className="flex items-center gap-1"
                >
                  <span className="text-sm">{companies[currentCompany].logo}</span>
                  {companies[currentCompany].name} is interested
                </motion.span>
              </AnimatePresence>
            </span>
          </div>

          {/* Visume Demo Image */}
          <div className="flex-1 flex items-center justify-center mb-3 min-h-0">
            <motion.div
              className="relative w-full max-w-xs h-full max-h-36 sm:max-h-48"
              whileHover={{ scale: 1.02 }}
              transition={{ type: "spring", stiffness: 300, damping: 30 }}
            >
              <img
                src="/jobseekervis.png"
                alt="Visume Demo"
                className="w-full h-full object-contain rounded-lg shadow-sm"
              />
            </motion.div>
          </div>

          {/* Live Notifications - Now properly contained */}
          <motion.div
            className="flex items-center gap-2 font-medium p-2 sm:p-3 rounded-lg sm:rounded-xl border border-indigo-200/60 font-manrope bg-indigo-50/50 backdrop-blur-sm mt-auto"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
          >
            <div
              className="w-5 sm:w-6 h-5 sm:h-6 rounded-xl flex items-center justify-center shadow-sm flex-shrink-0"
              style={{
                background: "linear-gradient(135deg, #6366f1, #8b5cf6)",
              }}
            >
              <Users className="h-3 sm:h-4 w-3 sm:w-4 text-white" />
            </div>
            <span className="text-indigo-700 font-semibold text-xs leading-tight flex-1 min-w-0">
              <AnimatePresence mode="wait">
                <motion.span
                  key={currentNotification}
                  initial={{ opacity: 0, y: 15 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -15 }}
                  transition={{ duration: 0.5 }}
                  className="block truncate"
                >
                  {notifications[currentNotification]}
                </motion.span>
              </AnimatePresence>
            </span>
          </motion.div>
        </div>
      </motion.div>
    </div>
  )
}

export default Hero
