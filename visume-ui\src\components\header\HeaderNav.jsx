import React, { useState, useEffect } from "react";
import { Ri<PERSON>oon<PERSON>ill, RiSunFill } from "react-icons/ri";
import { Bell, User, LogOut } from "lucide-react";
import Cookies from "js-cookie";
import { useNavigate } from "react-router-dom";
import Dropdown from "../dropdown";
import avatar from "assets/img/avatars/avatar4.png";

const HeaderNav = () => {
  const navigate = useNavigate();
  const [darkmode, setDarkmode] = useState(false);
  const [userData, setUserData] = useState(null);

  const role = Cookies.get("role");

  useEffect(() => {
    const fetchUserData = async () => {
      const userId = Cookies.get("candId") || Cookies.get("employerId");
      if (!userId) return;

      try {
        const endpoint = role === "jobseeker" 
          ? `${import.meta.env.VITE_APP_HOST}/api/v1/candidateProfilesData`
          : `${import.meta.env.VITE_APP_HOST}/api/v1/employerProfilesData`;
        
        const response = await fetch(endpoint, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: userId,
          },
        });

        if (response.ok) {
          const data = await response.json();
          setUserData(data.data);
        }
      } catch (error) {
        console.error("Error fetching user data:", error);
      }
    };

    fetchUserData();
  }, [role]);

  const avatarUrl = userData?.company_logo
    ? (userData.company_logo.startsWith("http")
        ? userData.company_logo
        : `${import.meta.env.VITE_APP_HOST}/${userData.company_logo}`)
    : userData?.profile_picture
    ? (userData.profile_picture.startsWith("http")
        ? userData.profile_picture
        : `${import.meta.env.VITE_APP_HOST}/${userData.profile_picture}`)
    : avatar;

  const handleLogout = () => {
    Cookies.remove('jstoken');
    Cookies.remove('role');
    Cookies.remove('candId');
    Cookies.remove('employerId');
    Cookies.remove('formData');
    Cookies.remove('questions');
    Cookies.remove('videoProfileId');
    Cookies.remove('skills');
    Cookies.remove('jobRole');
    navigate('/candidate/sign-in');
  };

  const toggleDarkMode = () => {
    document.body.classList.toggle("dark");
    setDarkmode(!darkmode);
  };

  return (
    <header className="h-16 bg-white dark:bg-gray-900">
      <div className="h-full flex items-center justify-end px-6">
        <div className="flex items-center space-x-2">
          {/* Notifications */}
          <button className="relative w-9 h-9 flex items-center justify-center text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 rounded-lg transition-all duration-200">
            <Bell size={18} />
            <span className="absolute top-2 right-2 w-1.5 h-1.5 bg-blue-500 rounded-full"></span>
          </button>

          {/* Dark Mode Toggle */}
          <button
            onClick={toggleDarkMode}
            className="w-9 h-9 flex items-center justify-center text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 rounded-lg transition-all duration-200"
          >
            {darkmode ? <RiSunFill size={18} /> : <RiMoonFill size={18} />}
          </button>

          {/* Profile Dropdown */}
          <Dropdown
            button={
              <div className="flex items-center space-x-2 h-9 px-2 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 rounded-lg transition-all duration-200 cursor-pointer ml-1">
                <img
                  className="w-7 h-7 rounded-full object-cover ring-2 ring-gray-200 dark:ring-gray-700"
                  src={avatarUrl}
                  alt="Profile"
                  onError={(e) => {
                    if (e.target.src !== avatar) {
                      e.target.onerror = null;
                      e.target.src = avatar;
                    }
                  }}
                />
                <div className="hidden sm:block text-left">
                  <p className="text-sm font-medium text-gray-700 dark:text-gray-300 leading-none">
                    {userData?.name || userData?.company_name || "User"}
                  </p>
                </div>
              </div>
            }
            children={
              <div className="w-52 rounded-xl bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 shadow-xl shadow-gray-900/5">
                <div className="p-3 border-b border-gray-100 dark:border-gray-700">
                  <div className="flex items-center space-x-3">
                    <img
                      className="w-9 h-9 rounded-full object-cover"
                      src={avatarUrl}
                      alt="Profile"
                      onError={(e) => {
                        if (e.target.src !== avatar) {
                          e.target.onerror = null;
                          e.target.src = avatar;
                        }
                      }}
                    />
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        {userData?.name || userData?.company_name || "User"}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400 capitalize">
                        {role}
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="p-1">
                  <button className="w-full flex items-center space-x-2 px-3 py-2.5 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 rounded-lg transition-colors">
                    <User size={16} />
                    <span>Profile Settings</span>
                  </button>
                </div>
                
                <div className="p-1 border-t border-gray-100 dark:border-gray-700">
                  <button 
                    onClick={handleLogout}
                    className="w-full flex items-center space-x-2 px-3 py-2.5 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
                  >
                    <LogOut size={16} />
                    <span>Log Out</span>
                  </button>
                </div>
              </div>
            }
            classNames="top-10 -right-2 w-max"
          />
        </div>
      </div>
    </header>
  );
};

export default HeaderNav;