import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { EyeIcon, EyeOffIcon, Lock, Check } from "lucide-react";
import Cookies from "js-cookie";
import toast from "react-hot-toast";

const EmployerPasswordSettings = () => {
  const navigate = useNavigate();
  const employerId = Cookies.get("employerId");

  // Password state
  const [passwordData, setPasswordData] = useState({
    oldPassword: "",
    newPassword: "",
    confirmPassword: "",
  });

  // Loading state
  const [loading, setLoading] = useState({ password: false });

  // Show/hide password states
  const [showPasswords, setShowPasswords] = useState({
    old: false,
    new: false,
    confirm: false,
  });

  // Handle password change
  const handlePasswordChange = async () => {
    if (
      !passwordData.oldPassword ||
      !passwordData.newPassword ||
      !passwordData.confirmPassword
    ) {
      toast.error("Please fill in all password fields.");
      return;
    }

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      toast.error("New passwords do not match.");
      return;
    }

    if (passwordData.newPassword.length < 8) {
      toast.error("Password must be at least 8 characters long.");
      return;
    }

    if (!employerId) {
      toast.error("No Token Found, Please Login Again");
      navigate("/employer/sign-in");
      return;
    }

    setLoading((prev) => ({ ...prev, password: true }));

    try {
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/changePassword`,
        {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            emp_id: parseInt(employerId),
            password: passwordData.oldPassword,
            newPassword: passwordData.newPassword,
          }),
        }
      );

      const responseData = await response.json();

      if (!response.ok) {
        toast.error(responseData.message);
      } else {
        setPasswordData({
          oldPassword: "",
          newPassword: "",
          confirmPassword: "",
        });
        toast.success("Password updated successfully!");
      }
    } catch (error) {
      toast.error("An unexpected error occurred.");
      console.error("Error:", error);
    } finally {
      setLoading((prev) => ({ ...prev, password: false }));
    }
  };

  // Toggle password visibility
  const togglePasswordVisibility = (field) => {
    setShowPasswords((prev) => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  return (
    <div>
      {/* Compact Section Header */}
      <div className="flex items-center gap-2 mb-4">
        <Lock className="w-4 h-4 text-red-600" />
        <h2 className="text-lg font-semibold text-slate-900 dark:text-white font-sora">
          Security Settings
        </h2>
      </div>

      <div className="max-w-2xl">
        {/* Compact Password Change Card */}
        <div className="bg-slate-50 dark:bg-slate-700/50 border border-slate-200 dark:border-slate-600 rounded-xl p-4">
          <div className="flex items-center gap-2 mb-4">
            <Lock className="w-4 h-4 text-blue-600" />
            <h3 className="font-semibold text-slate-900 dark:text-white font-sora">
              Change Password
            </h3>
          </div>

            {[
              { key: 'oldPassword', label: 'Current Password', placeholder: 'Enter your current password' },
              { key: 'newPassword', label: 'New Password', placeholder: 'Enter a new password' },
              { key: 'confirmPassword', label: 'Confirm New Password', placeholder: 'Confirm your new password' }
            ].map((field) => (
              <div key={field.key}>
                <label className="block text-sm font-medium text-slate-900 dark:text-white font-manrope mb-2">
                  {field.label}
                </label>
                <div className="relative">
                  <input
                    type={showPasswords[field.key.replace('Password', '')] ? "text" : "password"}
                    value={passwordData[field.key]}
                    onChange={(e) => setPasswordData(prev => ({ ...prev, [field.key]: e.target.value }))}
                    placeholder={field.placeholder}
                    className="w-full px-3 py-2 pr-10 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-900 dark:text-white font-manrope text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPasswords(prev => ({
                      ...prev,
                      [field.key.replace('Password', '')]: !prev[field.key.replace('Password', '')]
                    }))}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600 dark:hover:text-slate-300"
                  >
                    {showPasswords[field.key.replace('Password', '')] ?
                      <EyeOffIcon className="w-4 h-4" /> :
                      <EyeIcon className="w-4 h-4" />
                    }
                  </button>
                </div>
              </div>
            ))}

            <button
              onClick={handlePasswordChange}
              disabled={loading.password}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-all font-manrope ${
                loading.password
                  ? 'bg-slate-400 text-white cursor-not-allowed'
                  : 'bg-blue-600 hover:bg-blue-700 text-white'
              }`}
            >
              {loading.password ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  Updating...
                </>
              ) : (
                "Update Password"
              )}
            </button>
          </div>

          {/* Security Tips */}
          <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
            <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 font-manrope mb-3">
              Password Security Tips
            </h4>
            <ul className="space-y-2">
              {[
                'Use at least 8 characters with a mix of letters, numbers, and symbols',
                'Avoid using personal information like names or birthdays',
                'Don\'t reuse passwords from other accounts',
                'Consider using a password manager for better security'
              ].map((tip, index) => (
                <li key={index} className="flex items-start gap-2 text-sm text-blue-800 dark:text-blue-200 font-manrope">
                  <Check className="w-4 h-4 mt-0.5 flex-shrink-0 text-green-600" />
                  {tip}
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
  );
};

export default EmployerPasswordSettings;