// AiAssistance.jsx
import React, { useState, useRef, useEffect } from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { Loader2, Send, Sparkles, Trash2, MessageSquare, User, Zap, Video } from "lucide-react";

// Premium AI Assistant with Manrope and Sora fonts
const AiAssistance = ({
  messages,
  setMessages,
  input,
  setInput,
  context = {},
}) => {
  const [loading, setLoading] = useState(false);
  const chatRef = useRef(null);

  const GEMINI_API_URL = `${
    import.meta.env.VITE_APP_HOST
  }/api/gemini-assist/assist`;

  useEffect(() => {
    if (chatRef.current) {
      chatRef.current.scrollTo({
        top: chatRef.current.scrollHeight,
        behavior: "smooth",
      });
    }
  }, [messages, loading]);

  const handleSend = async () => {
    if (!input.trim() || loading) return;
    const newMessages = [...messages, { role: "user", text: input }];
    setMessages(newMessages);
    setInput("");
    setLoading(true);
    try {
      const res = await fetch(GEMINI_API_URL, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          messages: newMessages,
          context,
        }),
      });
      const data = await res.json();
      const aiText =
        data?.candidates?.[0]?.content?.parts?.[0]?.text ||
        "No response from Gemini API.";
      setMessages([...newMessages, { role: "model", text: aiText }]);
    } catch (err) {
      setMessages([
        ...newMessages,
        { role: "model", text: "Error connecting to Gemini API." },
      ]);
    }
    setLoading(false);
  };

  const handleClear = () => {
    setMessages([]);
    setInput("");
  };

  // Premium quick actions with enhanced styling
  const quickActions = [
    {
      label: "Candidate Overview",
      description: "Complete profile summary",
      icon: User,
      bgColor: "#6366f1", // indigo-500
      borderColor: "#c4b5fd", // purple-300
      action: async () => {
        if (loading) return;
        setMessages([{ role: "model", text: "AI is thinking..." }]);
        setLoading(true);
        let sysText = `Full Candidate Data:\n`;
        if (context?.candidateProf) {
          sysText += `Name: ${context.candidateProf.name || ""}\n`;
          sysText += `Email: ${context.candidateProf.email || ""}\n`;
          sysText += `Phone: ${context.candidateProf.phone || ""}\n`;
          sysText += `Location: ${context.candidateProf.location || ""}\n`;
        }
        if (context?.profileData) {
          sysText += `Profile Data: ${JSON.stringify(context.profileData)}\n`;
        }
        if (context?.strippedResumeJson) {
          sysText += `Summary: ${
            context.strippedResumeJson.summary ?? "Not available"
          }\n`;
          if (
            Array.isArray(context.strippedResumeJson.skills) &&
            context.strippedResumeJson.skills.length > 0
          ) {
            sysText += `Skills: ${context.strippedResumeJson.skills.join(
              ", "
            )}\n`;
          }
          if (
            Array.isArray(context.strippedResumeJson.experience) &&
            context.strippedResumeJson.experience.length > 0
          ) {
            sysText += `Experience:\n`;
            context.strippedResumeJson.experience.forEach((exp) => {
              sysText += `- ${exp.title || exp.position || "Role"} at ${
                exp.company || "Company"
              } (${exp.duration || "Duration"})\n`;
              if (exp.description) {
                sysText += `  - ${exp.description}\n`;
              }
            });
          }
          if (
            Array.isArray(context.strippedResumeJson.education) &&
            context.strippedResumeJson.education.length > 0
          ) {
            sysText += `Education:\n`;
            context.strippedResumeJson.education.forEach((edu) => {
              sysText += `- ${edu.degree || "Degree"} at ${
                edu.institution || "Institution"
              } (${edu.year || "Year"})\n`;
            });
          }
        }
        if (context?.questionsAndAnswers?.length > 0) {
          sysText += `Video Profile Q&A:\n`;
          context.questionsAndAnswers.forEach((qa, i) => {
            sysText += `Q${i + 1}: ${qa.question}\nA: ${qa.answer}\n`;
          });
        }
        try {
          const res = await fetch(GEMINI_API_URL, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              messages: [
                {
                  role: "user",
                  text:
                    sysText.trim() +
                    "\n\nSummarize this candidate's profile, background, skills, and video interview based on all provided data.",
                },
              ],
              context,
            }),
          });
          const data = await res.json();
          const aiText =
            data?.candidates?.[0]?.content?.parts?.[0]?.text ||
            "No response from Gemini API.";
          setMessages([{ role: "model", text: aiText }]);
        } catch (err) {
          setMessages([
            {
              role: "model",
              text: "Error connecting to Gemini API.",
            },
          ]);
        }
        setLoading(false);
      },
    },
    {
      label: "Technical Skills",
      description: "Skills & expertise analysis",
      icon: Zap,
      bgColor: "#10b981", // emerald-500
      borderColor: "#10b981", // emerald-500 (selected state)
      action: async () => {
        if (loading) return;
        setMessages([{ role: "model", text: "AI is thinking..." }]);
        setLoading(true);
        let sysText = `Candidate Technical Skills:\n`;
        if (context?.strippedResumeJson) {
          if (
            Array.isArray(context.strippedResumeJson.skills) &&
            context.strippedResumeJson.skills.length > 0
          ) {
            sysText += `Skills: ${context.strippedResumeJson.skills.join(
              ", "
            )}\n`;
          }
          if (
            Array.isArray(context.strippedResumeJson.experience) &&
            context.strippedResumeJson.experience.length > 0
          ) {
            sysText += `Experience:\n`;
            context.strippedResumeJson.experience.forEach((exp) => {
              sysText += `- ${exp.title || exp.position || "Role"} at ${
                exp.company || "Company"
              } (${exp.duration || "Duration"})\n`;
              if (exp.description) {
                sysText += `  - ${exp.description}\n`;
              }
            });
          }
        }
        try {
          const res = await fetch(GEMINI_API_URL, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              messages: [
                {
                  role: "user",
                  text:
                    sysText.trim() +
                    "\n\nSummarize this candidate's technical skills and relevant experience.",
                },
              ],
              context,
            }),
          });
          const data = await res.json();
          const aiText =
            data?.candidates?.[0]?.content?.parts?.[0]?.text ||
            "No response from Gemini API.";
          setMessages([{ role: "model", text: aiText }]);
        } catch (err) {
          setMessages([
            {
              role: "model",
              text: "Error connecting to Gemini API.",
            },
          ]);
        }
        setLoading(false);
      },
    },
    {
      label: "Interview Summary",
      description: "Video responses insight",
      icon: Video,
      bgColor: "#ef4444", // red-500
      borderColor: "#f3f4f6", // gray-100
      action: async () => {
        if (loading) return;
        setMessages([{ role: "model", text: "AI is thinking..." }]);
        setLoading(true);
        let sysText = `Candidate Video Resume Q&A:\n`;
        if (context?.questionsAndAnswers?.length > 0) {
          context.questionsAndAnswers.forEach((qa, i) => {
            sysText += `Q${i + 1}: ${qa.question}\nA: ${qa.answer}\n`;
          });
        } else {
          sysText += "No video Q&A available.\n";
        }
        try {
          const res = await fetch(GEMINI_API_URL, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              messages: [
                {
                  role: "user",
                  text:
                    sysText.trim() +
                    "\n\nSummarize this candidate's video resume and interview responses.",
                },
              ],
              context,
            }),
          });
          const data = await res.json();
          const aiText =
            data?.candidates?.[0]?.content?.parts?.[0]?.text ||
            "No response from Gemini API.";
          setMessages([{ role: "model", text: aiText }]);
        } catch (err) {
          setMessages([
            {
              role: "model",
              text: "Error connecting to Gemini API.",
            },
          ]);
        }
        setLoading(false);
      },
    },
  ];

  return (
    <div className="bg-white dark:bg-gray-900 border-2 border-purple-200 dark:border-purple-800 rounded-xl shadow-sm overflow-hidden">
        {/* Header */}
        <div className="bg-white dark:bg-gray-900 px-6 py-4 border-b border-gray-100 dark:border-gray-800">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 rounded-lg bg-blue-50 dark:bg-blue-900/20 flex items-center justify-center">
                <Sparkles className="w-4 h-4 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h1 className="text-lg font-semibold text-gray-900 dark:text-white">
                  AI Assistant
                </h1>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Ask questions about this candidate
                </p>
              </div>
            </div>
            <button
              className="flex items-center space-x-2 px-3 py-1.5 text-sm bg-gray-50 hover:bg-gray-100 dark:bg-gray-800 dark:hover:bg-gray-700 rounded-lg transition-colors text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={handleClear}
              disabled={loading || messages.length === 0}
            >
              <Trash2 className="w-4 h-4" />
              <span>Clear</span>
            </button>
          </div>
        </div>

        {/* Chat Area */}
        <div
          ref={chatRef}
          className="h-[400px] overflow-y-auto bg-gray-50 dark:bg-gray-800/50 p-4"
        >
          {messages.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-center px-4">
              {/* Message Icon */}
              <div className="w-12 h-12 rounded-lg bg-blue-50 dark:bg-blue-900/20 flex items-center justify-center mb-6">
                <MessageSquare className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              
              {/* Title and Description */}
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                How can I help you?
              </h2>
              <p className="text-gray-600 dark:text-gray-400 mb-8 max-w-sm text-sm leading-relaxed">
                Ask me anything about this candidate's profile, skills, or interview responses.
              </p>
              
              {/* Quick Action Buttons */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 w-full max-w-4xl">
                {quickActions.map((action, idx) => {
                  const IconComponent = action.icon;
                  const colors = {
                    0: { bg: 'bg-blue-500', light: 'bg-blue-50', text: 'text-blue-600', darkBg: 'dark:bg-blue-900/20', darkText: 'dark:text-blue-400' },
                    1: { bg: 'bg-green-500', light: 'bg-green-50', text: 'text-green-600', darkBg: 'dark:bg-green-900/20', darkText: 'dark:text-green-400' },
                    2: { bg: 'bg-purple-500', light: 'bg-purple-50', text: 'text-purple-600', darkBg: 'dark:bg-purple-900/20', darkText: 'dark:text-purple-400' }
                  };
                  const color = colors[idx];
                  
                  return (
                    <button
                      key={idx}
                      className="group bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 text-center transition-all duration-200 hover:shadow-md hover:border-purple-300 dark:hover:border-purple-600 hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                      onClick={action.action}
                      disabled={loading}
                    >
                      <div className={`w-12 h-12 rounded-lg ${color.bg} flex items-center justify-center mx-auto mb-4 shadow-sm group-hover:scale-105 transition-transform`}>
                        <IconComponent className="w-6 h-6 text-white" />
                      </div>
                      <h3 className="font-semibold text-gray-900 dark:text-white text-sm mb-2 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors">
                        {action.label}
                      </h3>
                      <p className="text-gray-500 dark:text-gray-400 text-xs leading-relaxed">
                        {action.description}
                      </p>
                    </button>
                  );
                })}
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {messages.map((msg, idx) => (
                <div
                  key={idx}
                  className={`flex ${
                    msg.role === "user" ? "justify-end" : "justify-start"
                  }`}
                >
                  <div
                    className={`max-w-[85%] rounded-lg px-4 py-3 text-sm ${
                      msg.role === "user"
                        ? "bg-blue-600 text-white"
                        : "border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                    }`}
                  >
                    <div className="leading-relaxed">
                      {msg.role === "model" ? (
                        <ReactMarkdown 
                          remarkPlugins={[remarkGfm]}
                          components={{
                            h1: ({node, ...props}) => <h1 className="text-lg font-semibold mb-2 text-gray-900 dark:text-white" {...props} />,
                            h2: ({node, ...props}) => <h2 className="text-base font-semibold mb-2 text-gray-800 dark:text-gray-200" {...props} />,
                            h3: ({node, ...props}) => <h3 className="text-sm font-semibold mb-1 text-gray-800 dark:text-gray-200" {...props} />,
                            p: ({node, ...props}) => <p className="mb-2 last:mb-0" {...props} />,
                            ul: ({node, ...props}) => <ul className="list-disc pl-4 mb-2 space-y-0.5" {...props} />,
                            ol: ({node, ...props}) => <ol className="list-decimal pl-4 mb-2 space-y-0.5" {...props} />,
                            li: ({node, ...props}) => <li className="text-sm" {...props} />,
                            strong: ({node, ...props}) => <strong className="font-medium text-gray-900 dark:text-white" {...props} />,
                            code: ({node, inline, ...props}) => 
                              inline ? 
                                <code className="bg-gray-100 dark:bg-gray-700 text-blue-600 dark:text-blue-400 px-1 py-0.5 rounded text-xs font-mono" {...props} /> :
                                <code className="block bg-gray-100 dark:bg-gray-700 p-2 rounded text-xs font-mono overflow-x-auto" {...props} />
                          }}
                        >
                          {msg.text}
                        </ReactMarkdown>
                      ) : (
                        <div className="whitespace-pre-wrap">{msg.text}</div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
              {loading && (
                <div className="flex justify-start">
                  <div className="flex items-center space-x-2 rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 px-4 py-3">
                    <Loader2 className="h-4 w-4 animate-spin text-blue-600 dark:text-blue-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-300">
                      AI is thinking...
                    </span>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Input Area */}
        <div className="bg-white dark:bg-gray-900 border-t border-gray-100 dark:border-gray-800 p-4">
          <div className="flex space-x-3">
            <div className="flex-1">
              <input
                type="text"
                className="w-full rounded-lg border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 px-4 py-3 text-sm text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-500 transition-colors focus:border-blue-500 dark:focus:border-blue-400 focus:outline-none focus:ring-2 focus:ring-blue-500/20 disabled:opacity-50 disabled:cursor-not-allowed"
                placeholder="Ask me anything about this candidate..."
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === "Enter" && !e.shiftKey) {
                    e.preventDefault();
                    handleSend();
                  }
                }}
                disabled={loading}
              />
            </div>
            <button
              className={`flex items-center space-x-2 rounded-lg px-4 py-3 text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                loading || !input.trim()
                  ? "cursor-not-allowed bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-500"
                  : "bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500"
              }`}
              onClick={handleSend}
              disabled={loading || !input.trim()}
            >
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Sending</span>
                </>
              ) : (
                <>
                  <Send className="h-4 w-4" />
                  <span>Send</span>
                </>
              )}
            </button>
          </div>
        </div>
    </div>
  );
};

export default AiAssistance;