import React, { useState, useRef, useEffect } from "react";
import {
  Briefcase,
  ChartBar,
  Trophy,
  Upload,
  Users,
  X,
  Check,
  Building2,
  Target,
  Zap,
  Crown,
} from "lucide-react";
import LogoImage from "assets/img/Visume-logo-icon.png";
import videoRes from "assets/img/videores-illustration.png";
import { useNavigate, useLocation } from "react-router-dom";
import toast from "react-hot-toast";
import Cookies from "js-cookie";

const EmpCreateAccountForm = ({
  isProcessing,
  navigate,
  LogoImage,
  videoRes,
  profilePicPreview,
  imageError,
  setImageError,
  profilePic,
  profilePicError,
  errors,
  handleProfilePicChange,
  profilePicInputRef,
  handleRemoveProfilePic,
  formData,
  setFormData,
  handleInputChange,
  emailValidation,
  handlePhoneNumberChange,
  showSendOTPButton,
  otpVerified,
  handleSendOTPClick,
  otp,
  otpError,
  handleOTPChange,
  handleVerifyOTP,
  showOTPModal,
  setShowOTPModal,
  handleSubmit,
  phoneAvailable,
  phoneCheckLoading,
  phoneCheckMsg,
}) => (
  <div className="relative min-h-screen bg-blue-50/30 px-6 py-6" style={{ fontFamily: 'Sora, sans-serif' }}>
    {/* Font imports */}
    <style>{`
      @import url('https://fonts.googleapis.com/css2?family=Manrope:wght@400;500;600;700;800&family=Sora:wght@300;400;500;600;700&display=swap');
    `}</style>
    {/* Loading Overlay */}
    {isProcessing && (
      <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black/60 backdrop-blur-sm">
        <div className="w-full max-w-md mx-4 rounded-3xl border border-white/20 bg-white/95 backdrop-blur-md p-8 shadow-2xl">
          <div className="text-center">
            <div className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-indigo-100">
              <div className="h-8 w-8 animate-spin rounded-full border-4 border-indigo-200 border-t-indigo-600"></div>
            </div>
            <h3 className="text-xl font-bold text-gray-800 mb-3" style={{ fontFamily: 'Manrope, sans-serif' }}>
              Creating your profile
            </h3>
            <p className="text-gray-600 text-sm" style={{ fontFamily: 'Sora, sans-serif' }}>
              Please wait while we process your information...
            </p>
          </div>
        </div>
      </div>
    )}
    <div className="mx-auto max-w-7xl">
      {/* Header */}
      <div className="mb-8 flex items-center justify-between">
        <div className="flex items-center gap-3">
          <img src="/visume-logo-new.png" alt="Visume logo" className="h-9 w-9" />
          <span className="text-xl font-semibold text-gray-900" style={{ fontFamily: 'Manrope, sans-serif' }}>
            Visume
          </span>
        </div>
        <div className="text-sm text-gray-600" style={{ fontFamily: 'Sora, sans-serif' }}>
          Already have an account?{" "}
          <button
            onClick={() => navigate("/employer/sign-in")}
            className="font-medium text-indigo-600 hover:text-indigo-700 transition-colors"
            style={{ fontFamily: 'Sora, sans-serif' }}
          >
            Sign in
          </button>
        </div>
      </div>

      <div className="grid gap-8 lg:grid-cols-[2fr,1fr]">
        {/* Main Form */}
        <div className="rounded-2xl border border-indigo-200 bg-white p-8 shadow-sm">
          <div className="mb-6">
            <h1 className="text-2xl font-semibold text-gray-900 mb-2" style={{ fontFamily: 'Manrope, sans-serif' }}>
              Create your employer profile
            </h1>
            <p className="text-gray-600 text-base" style={{ fontFamily: 'Sora, sans-serif' }}>
              Join thousands of companies hiring top talent through video resumes
            </p>
          </div>

          <form
            className="space-y-6"
            onSubmit={handleSubmit}
            autoComplete="off"
          >
            {/* Company Information Section */}
            <div className="space-y-4">
              <div className="flex items-center gap-2 pb-2 mb-3">
                <div className="w-6 h-6 bg-indigo-50 rounded-full flex items-center justify-center">
                  <span className="text-indigo-600 font-medium text-xs">1</span>
                </div>
                <h3 className="text-sm font-medium text-gray-700 uppercase tracking-wide" style={{ fontFamily: 'Manrope, sans-serif' }}>Company Information</h3>
              </div>
              {/* Profile Picture Upload */}
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-900" style={{ fontFamily: 'Manrope, sans-serif' }}>
                  Profile Picture{" "}
                  <span className="text-gray-500 font-normal" style={{ fontFamily: 'Sora, sans-serif' }}>(optional)</span>
                </label>
                <input
                  type="file"
                  accept="image/jpeg, image/png, image/jpg"
                  className="hidden"
                  id="profile-pic-upload-input"
                  onChange={handleProfilePicChange}
                  ref={profilePicInputRef}
                />
                <div
                  className="group cursor-pointer rounded-xl border-2 border-dashed border-gray-200 bg-gray-50 p-4 text-center transition-all hover:border-indigo-300 hover:bg-indigo-50"
                  onClick={() =>
                    document.getElementById("profile-pic-upload-input").click()
                  }
                >
                  {profilePicPreview && !imageError ? (
                    <div className="flex flex-col items-center">
                      <div className="relative">
                        <img
                          src={
                            typeof profilePicPreview === "string"
                              ? profilePicPreview
                              : URL.createObjectURL(profilePicPreview)
                          }
                          alt="Profile Preview"
                          className="mx-auto mb-3 h-20 w-20 rounded-full border-2 border-indigo-200 object-cover shadow-md"
                          onError={() => setImageError(true)}
                        />
                        <button
                          type="button"
                          aria-label="Remove profile picture"
                          className="absolute -right-1 -top-1 rounded-full bg-red-100 p-1.5 shadow-sm transition-colors hover:bg-red-200"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRemoveProfilePic();
                            setImageError(false);
                          }}
                        >
                          <X className="h-3 w-3 text-red-600" />
                        </button>
                      </div>
                      <p className="text-xs text-gray-600 font-medium" style={{ fontFamily: 'Sora, sans-serif' }}>
                        {profilePic?.name}
                      </p>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center">
                      <div className="mb-3 rounded-full bg-gray-100 p-4 group-hover:bg-indigo-100 transition-colors">
                        <svg
                          className="h-8 w-8 text-gray-400 group-hover:text-indigo-500 transition-colors"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                      </div>
                      <p className="text-sm font-medium text-gray-700 mb-1" style={{ fontFamily: 'Sora, sans-serif' }}>Add a profile photo</p>
                      <p className="text-xs text-gray-500" style={{ fontFamily: 'Sora, sans-serif' }}>
                        JPG, JPEG, PNG • Max 5MB
                      </p>
                    </div>
                  )}
                </div>
                {(profilePicError || errors.profilePic) && (
                  <p className="mt-2 text-xs text-red-500" style={{ fontFamily: 'Sora, sans-serif' }}>
                    {profilePicError || errors.profilePic}
                  </p>
                )}
              </div>
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-900" style={{ fontFamily: 'Manrope, sans-serif' }}>
                  Full name <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  name="fullName"
                  placeholder="Enter your full name"
                  className="w-full rounded-xl border border-gray-300 bg-gray-50/50 px-4 py-3 text-gray-900 placeholder-gray-500 transition-all focus:border-indigo-500 focus:bg-white focus:outline-none focus:ring-3 focus:ring-indigo-500/20 focus:shadow-sm"
                  value={formData.fullName}
                  onChange={handleInputChange}
                  required
                />
                {errors.fullName && (
                  <p className="mt-1 text-sm text-red-600" style={{ fontFamily: 'Sora, sans-serif' }}>
                    {errors.fullName}
                  </p>
                )}
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium text-gray-900" style={{ fontFamily: 'Manrope, sans-serif' }}>
                  Email address <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <input
                    type="email"
                    name="email"
                    placeholder="Enter your email address"
                    className={`w-full rounded-xl border bg-gray-50/50 px-4 py-3 text-gray-900 placeholder-gray-500 transition-all focus:bg-white focus:outline-none focus:ring-3 focus:shadow-sm ${
                      emailValidation.isChecking
                        ? "border-indigo-300 focus:border-indigo-500 focus:ring-indigo-500/20"
                        : emailValidation.isValid === null
                        ? "border-gray-300 focus:border-indigo-500 focus:ring-indigo-500/20"
                        : emailValidation.isValid
                        ? "border-green-400 focus:border-green-500 focus:ring-green-500/20"
                        : "border-red-400 focus:border-red-500 focus:ring-red-500/20"
                    }`}
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                  />
                  {emailValidation.isValid && (
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                      <Check className="h-4 w-4 text-green-500" />
                    </div>
                  )}
                </div>
                {/* Email validation feedback */}
                {emailValidation.message && (
                  <div className={`mt-1 flex items-center gap-1 text-sm ${
                    emailValidation.isChecking
                      ? "text-indigo-600"
                      : emailValidation.isValid
                      ? "text-green-600"
                      : "text-red-600"
                  }`} style={{ fontFamily: 'Sora, sans-serif' }}>
                    {emailValidation.isChecking ? (
                      <div className="h-3 w-3 animate-spin rounded-full border-2 border-indigo-600 border-t-transparent"></div>
                    ) : emailValidation.isValid ? (
                      <Check className="h-3 w-3" />
                    ) : (
                      <X className="h-3 w-3" />
                    )}
                    {emailValidation.message}
                  </div>
                )}
                {errors.email && (
                  <p className="mt-1 text-sm text-red-600" style={{ fontFamily: 'Sora, sans-serif' }}>
                    {errors.email}
                  </p>
                )}
              </div>
              
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-900" style={{ fontFamily: 'Manrope, sans-serif' }}>
                  Password <span className="text-red-500">*</span>
                </label>
                <input
                  type="password"
                  name="password"
                  placeholder="Create a strong password"
                  className="w-full rounded-xl border border-gray-300 bg-gray-50/50 px-4 py-3 text-gray-900 placeholder-gray-500 transition-all focus:border-indigo-500 focus:bg-white focus:outline-none focus:ring-3 focus:ring-indigo-500/20 focus:shadow-sm"
                  value={formData.password}
                  onChange={handleInputChange}
                  required
                  minLength={6}
                />
                {errors.password && (
                  <p className="mt-1 text-sm text-red-600" style={{ fontFamily: 'Sora, sans-serif' }}>
                    {errors.password}
                  </p>
                )}
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium text-gray-900" style={{ fontFamily: 'Manrope, sans-serif' }}>
                  Designation <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  name="designation"
                  placeholder="e.g., Hiring Manager, HR Director"
                  className="w-full rounded-xl border border-gray-300 bg-gray-50/50 px-4 py-3 text-gray-900 placeholder-gray-500 transition-all focus:border-indigo-500 focus:bg-white focus:outline-none focus:ring-3 focus:ring-indigo-500/20 focus:shadow-sm"
                  value={formData.designation}
                  onChange={handleInputChange}
                  required
                />
                {errors.designation && (
                  <p className="mt-1 text-sm text-red-600" style={{ fontFamily: 'Sora, sans-serif' }}>
                    {errors.designation}
                  </p>
                )}
              </div>
              {/* Organization Section */}
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-900" style={{ fontFamily: 'Manrope, sans-serif' }}>
                  Organization <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  name="organization"
                  placeholder="e.g., Acme Corp, Tech Solutions"
                  className="w-full rounded-xl border border-gray-300 bg-gray-50/50 px-4 py-3 text-gray-900 placeholder-gray-500 transition-all focus:border-indigo-500 focus:bg-white focus:outline-none focus:ring-3 focus:ring-indigo-500/20 focus:shadow-sm"
                  value={formData.organization}
                  onChange={handleInputChange}
                  required
                />
                {errors.organization && (
                  <p className="mt-1 text-sm text-red-600" style={{ fontFamily: 'Sora, sans-serif' }}>
                    {errors.organization}
                  </p>
                )}
              </div>
            </div>

            {/* Contact Information Section */}
            <div className="space-y-4">
              <div className="flex items-center gap-2 pb-2 mb-3">
                <div className="w-6 h-6 bg-indigo-50 rounded-full flex items-center justify-center">
                  <span className="text-indigo-600 font-medium text-xs">2</span>
                </div>
                <h3 className="text-sm font-medium text-gray-700 uppercase tracking-wide" style={{ fontFamily: 'Manrope, sans-serif' }}>Contact Information</h3>
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium text-gray-900" style={{ fontFamily: 'Manrope, sans-serif' }}>
                  Mobile number <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <div className={`flex rounded-xl border bg-gray-50/50 transition-all focus-within:bg-white focus-within:ring-3 focus-within:shadow-sm ${
                    phoneCheckLoading
                      ? "border-indigo-300 focus-within:border-indigo-500 focus-within:ring-indigo-500/20"
                      : phoneAvailable === null
                      ? "border-gray-300 focus-within:border-indigo-500 focus-within:ring-indigo-500/20"
                      : phoneAvailable
                      ? "border-green-400 focus-within:border-green-500 focus-within:ring-green-500/20"
                      : "border-red-400 focus-within:border-red-500 focus-within:ring-red-500/20"
                  }`}>
                    <span className="inline-flex items-center px-4 text-gray-600 text-sm font-medium border-r border-gray-300">
                      +91
                    </span>
                    <input
                      type="tel"
                      name="mobile"
                      maxLength="10"
                      inputMode="numeric"
                      pattern="[0-9]*"
                      placeholder="Enter your mobile number"
                      className="w-full rounded-r-xl px-4 py-3 text-gray-900 placeholder-gray-500 bg-transparent focus:outline-none"
                      value={formData.mobile}
                      onChange={handlePhoneNumberChange}
                      required
                    />
                  </div>
                  {phoneAvailable && (
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                      <Check className="h-4 w-4 text-green-500" />
                    </div>
                  )}
                </div>
                {/* Phone validation feedback */}
                {phoneCheckMsg && (
                  <div className={`mt-1 flex items-center gap-1 text-sm ${
                    phoneCheckLoading
                      ? "text-indigo-600"
                      : phoneAvailable
                      ? "text-green-600"
                      : "text-red-600"
                  }`} style={{ fontFamily: 'Sora, sans-serif' }}>
                    {phoneCheckLoading ? (
                      <div className="h-3 w-3 animate-spin rounded-full border-2 border-indigo-600 border-t-transparent"></div>
                    ) : phoneAvailable ? (
                      <Check className="h-3 w-3" />
                    ) : (
                      <X className="h-3 w-3" />
                    )}
                    {phoneCheckMsg}
                  </div>
                )}
                {errors.mobile && (
                  <p className="mt-1 text-sm text-red-600" style={{ fontFamily: 'Sora, sans-serif' }}>
                    {errors.mobile}
                  </p>
                )}
                
                {showSendOTPButton && !otpVerified && phoneAvailable && (
                  <button
                    onClick={handleSendOTPClick}
                    className="mt-3 inline-flex items-center gap-2 rounded-xl bg-indigo-600 px-4 py-2.5 text-sm font-medium text-white shadow-sm transition-all hover:bg-indigo-700 hover:shadow-md focus:outline-none focus:ring-3 focus:ring-indigo-500/30"
                    style={{ fontFamily: 'Sora, sans-serif' }}
                  >
                    Send verification code
                  </button>
                )}
                
                {otpVerified && (
                  <div className="mt-2 inline-flex items-center gap-2 text-sm text-green-600" style={{ fontFamily: 'Sora, sans-serif' }}>
                    <Check className="h-4 w-4" />
                    Phone number verified!
                  </div>
                )}
              </div>
            </div>

            {/* Final Section */}
            <div className="space-y-4 pt-2">
              <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
                <input
                  type="checkbox"
                  className="mt-0.5 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 focus:ring-offset-0"
                  defaultChecked
                />
                <div>
                  <p className="text-sm text-gray-800 font-medium mb-1" style={{ fontFamily: 'Manrope, sans-serif' }}>
                    Stay updated with opportunities
                  </p>
                  <p className="text-xs text-gray-600" style={{ fontFamily: 'Sora, sans-serif' }}>
                    Get candidate applications and hiring insights via email and SMS
                  </p>
                </div>
              </div>

              <p className="text-xs text-gray-600 text-center" style={{ fontFamily: 'Sora, sans-serif' }}>
                By creating an account, you agree to our{" "}
                <a href="#" className="text-indigo-600 hover:text-indigo-700 underline">
                  Terms of Service
                </a>{" "}
                and{" "}
                <a href="#" className="text-indigo-600 hover:text-indigo-700 underline">
                  Privacy Policy
                </a>
              </p>

              <button
                type="submit"
                className="w-full rounded-xl bg-gradient-to-r from-indigo-600 to-indigo-700 py-3.5 px-6 text-base font-semibold text-white shadow-lg transition-all hover:from-indigo-700 hover:to-indigo-800 hover:shadow-xl hover:scale-[1.01] focus:outline-none focus:ring-3 focus:ring-indigo-500/30"
                style={{ fontFamily: 'Manrope, sans-serif' }}
              >
                Create your Visume profile
              </button>
            </div>
          </form>
        </div>

        {/* Sidebar */}
        <div className="lg:sticky lg:top-8 h-fit">
          <div className="rounded-2xl border border-indigo-200 bg-white p-6 shadow-sm">
            <div className="mb-6 text-center">
              <img
                src={videoRes}
                alt="Employer hiring illustration"
                className="w-full max-w-xs mx-auto"
              />
            </div>
            
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2 text-center" style={{ fontFamily: 'Manrope, sans-serif' }}>
                Why choose Visume for hiring?
              </h3>
              <p className="text-gray-600 text-center text-sm" style={{ fontFamily: 'Sora, sans-serif' }}>
                Join thousands of companies revolutionizing their hiring process
              </p>
            </div>
            
            <div className="space-y-5">
              {[
                {
                  icon: Target,
                  color: "bg-indigo-100/80 text-indigo-700",
                  title: "50% faster hiring decisions",
                  description: "Screen candidates 10x faster with video resumes"
                },
                {
                  icon: Users,
                  color: "bg-green-100/80 text-green-700", 
                  title: "Access 10,000+ candidates",
                  description: "Tap into our growing pool of video-verified talent"
                },
                {
                  icon: Zap,
                  color: "bg-amber-100/80 text-amber-700",
                  title: "AI-powered matching",
                  description: "Smart algorithms find the perfect culture fit"
                },
                {
                  icon: Crown,
                  color: "bg-purple-100/80 text-purple-700",
                  title: "Premium hiring tools",
                  description: "Advanced analytics and candidate insights"
                }
              ].map((feature, index) => (
                <div key={index} className="flex items-start gap-4">
                  <div className={`flex-shrink-0 rounded-xl p-3 shadow-sm ${feature.color}`}>
                    <feature.icon className="h-5 w-5" strokeWidth={2.5} />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1 text-sm" style={{ fontFamily: 'Manrope, sans-serif' }}>
                      {feature.title}
                    </h4>
                    <p className="text-sm text-gray-600 leading-relaxed" style={{ fontFamily: 'Sora, sans-serif' }}>
                      {feature.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>

    {/* OTP Modal */}
    {showOTPModal && (
      <div className="fixed inset-0 z-50 flex items-center justify-center backdrop-blur-sm">
        <div className="absolute inset-0 bg-black/60"></div>
        <div className="relative z-50 w-full max-w-md mx-4 rounded-3xl border border-white/20 bg-white/95 backdrop-blur-md p-8 shadow-2xl">
          <button
            onClick={() => setShowOTPModal(false)}
            className="absolute right-4 top-4 rounded-full p-2 text-gray-400 transition-colors hover:bg-gray-100 hover:text-gray-600"
          >
            <X className="h-5 w-5" />
          </button>
          
          <div className="text-center mb-8">
            <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-indigo-100">
              <svg className="h-8 w-8 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </div>
            <h3 className="text-xl font-bold text-gray-800 mb-2" style={{ fontFamily: 'Manrope, sans-serif' }}>
              Verify your number
            </h3>
            <p className="text-gray-600 text-sm" style={{ fontFamily: 'Sora, sans-serif' }}>
              Enter the 4-digit code sent to your mobile number
            </p>
          </div>
          
          <div className="mb-6 flex justify-center gap-3">
            {otp.map((digit, index) => (
              <input
                key={index}
                id={`otp-${index}`}
                type="text"
                inputMode="numeric"
                maxLength={1}
                value={digit}
                onChange={(e) => handleOTPChange(index, e.target.value)}
                className="h-14 w-14 rounded-xl border-2 border-gray-200 text-center text-xl font-semibold text-gray-800 transition-all focus:border-indigo-500 focus:outline-none focus:ring-4 focus:ring-indigo-500/10"
                aria-label={`OTP digit ${index + 1}`}
              />
            ))}
          </div>
          
          {otpError && (
            <div className="mb-6 rounded-xl bg-red-50 p-3 text-center">
              <p className="text-sm text-red-600 flex items-center justify-center gap-1" style={{ fontFamily: 'Sora, sans-serif' }}>
                <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                {otpError}
              </p>
            </div>
          )}
          
          <div className="space-y-3">
            <button
              onClick={handleVerifyOTP}
              className="w-full rounded-xl bg-indigo-600 py-3.5 text-lg font-semibold text-white shadow-sm transition-colors hover:bg-indigo-700 focus:outline-none focus:ring-4 focus:ring-indigo-500/20"
              style={{ fontFamily: 'Manrope, sans-serif' }}
            >
              Verify OTP
            </button>
            <button
              onClick={handleSendOTPClick}
              className="w-full text-sm text-indigo-600 font-medium hover:text-indigo-700 transition-colors"
              style={{ fontFamily: 'Sora, sans-serif' }}
            >
              Didn't receive the code? Resend OTP
            </button>
          </div>
        </div>
      </div>
    )}
  </div>
);

const CreateAccount = () => {
  const [isLoading, setIsLoading] = useState(false);

  // Get Google profile data from navigation state
  const location = useLocation();

  // Profile picture state
  const [profilePic, setProfilePic] = useState(null);
  const [profilePicPreview, setProfilePicPreview] = useState(null);
  const [profilePicError, setProfilePicError] = useState("");
  const profilePicInputRef = useRef(null);
  const [imageError, setImageError] = useState(false);

  // Prefill form if Google data is present
  useEffect(() => {
    if (location.state) {
      setFormData((prev) => ({
        ...prev,
        fullName: location.state.name || "",
        email: location.state.email || "",
      }));
      if (location.state.picture) {
        setProfilePicPreview(location.state.picture);
      }
    }
  }, [location.state]);

  const jsregcookie = (data) => {
    const allCookies = Cookies.get(); // Get all cookies
    for (const cookieName in allCookies) {
      Cookies.remove(cookieName); // Remove each cookie
    }
    localStorage.clear();
    Cookies.set("empId", data.emp_id, { expires: 7 });
    Cookies.set("employerId", data.employerId, { expires: 7 });
    Cookies.set("jstoken", data.token, { expires: 7 });
    Cookies.set("role", data.role, { expires: 7 });
  };

  // Remove profile picture handler
  const handleRemoveProfilePic = () => {
    setProfilePic(null);
    setProfilePicPreview(null);
    setProfilePicError("");
    if (profilePicInputRef.current) {
      profilePicInputRef.current.value = "";
    }
  };

  // Profile picture handler
  const handleProfilePicChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      const validTypes = ["image/jpeg", "image/png", "image/jpg"];
      if (!validTypes.includes(file.type)) {
        setProfilePicError("Only JPG, JPEG, or PNG files are allowed.");
        setProfilePic(null);
        setProfilePicPreview(null);
        return;
      }
      if (file.size > 5 * 1024 * 1024) {
        setProfilePicError("File size must be less than 5MB.");
        setProfilePic(null);
        setProfilePicPreview(null);
        return;
      }
      setProfilePic(file);
      setProfilePicPreview(URL.createObjectURL(file));
      setProfilePicError("");
    }
  };

  // Email validation state
  const [emailValidation, setEmailValidation] = useState({
    isValid: null,
    isChecking: false,
    message: ""
  });

  const validateEmail = async (email) => {
    if (!email || !email.includes("@")) {
      setEmailValidation({ isValid: null, isChecking: false, message: "" });
      return;
    }
    
    setEmailValidation({ isValid: null, isChecking: true, message: "Checking email..." });
    
    try {
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/check-employer-email`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ email }),
        }
      );
      const data = await response.json();
      
      if (response.ok && data.available) {
        setEmailValidation({ isValid: true, isChecking: false, message: "Email is available" });
      } else if (response.status === 409) {
        setEmailValidation({ 
          isValid: false, 
          isChecking: false, 
          message: data.message || "Email already exists. Please log in." 
        });
      } else {
        setEmailValidation({ 
          isValid: false, 
          isChecking: false, 
          message: data.message || "Error checking email." 
        });
      }
    } catch (err) {
      setEmailValidation({ 
        isValid: false, 
        isChecking: false, 
        message: "Error checking email." 
      });
    }
  };

  // Phone validation states
  const [phoneAvailable, setPhoneAvailable] = useState(null);
  const [phoneCheckLoading, setPhoneCheckLoading] = useState(false);
  const [phoneCheckMsg, setPhoneCheckMsg] = useState("");

  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    password: "",
    mobile: "",
    designation: "",
    organization: ""
  });

  // Debounced email validation
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      validateEmail(formData.email);
    }, 500);
    return () => clearTimeout(timeoutId);
  }, [formData.email]);

  // Phone validation useEffect
  useEffect(() => {
    const checkPhone = async () => {
      if (!formData.mobile || formData.mobile.length !== 10) {
        setPhoneAvailable(null);
        setPhoneCheckMsg("");
        return;
      }
      setPhoneCheckLoading(true);
      try {
        const response = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/check-employer-phone`,
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ phone: formData.mobile }),
          }
        );
        const data = await response.json();
        if (response.ok && data.available) {
          setPhoneAvailable(true);
          setPhoneCheckMsg("");
        } else if (response.status === 409) {
          setPhoneAvailable(false);
          setPhoneCheckMsg(
            data.message || "Phone number already exists. Please use a different number."
          );
        } else {
          setPhoneAvailable(null);
          setPhoneCheckMsg(data.message || "Error checking phone number.");
        }
      } catch (err) {
        setPhoneAvailable(null);
        setPhoneCheckMsg("Error checking phone number.");
      } finally {
        setPhoneCheckLoading(false);
      }
    };
    checkPhone();
  }, [formData.mobile]);

  const [errors, setErrors] = useState({});
  const navigate = useNavigate();
  const [showSendOTPButton, setShowSendOTPButton] = useState(false);
  const [showOTPModal, setShowOTPModal] = useState(false);
  const [otp, setOTP] = useState(["", "", "", ""]);
  const [generatedOTP, setGeneratedOTP] = useState("");
  const [otpVerified, setOtpVerified] = useState(false);
  const [otpError, setOtpError] = useState("");

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
    setErrors({ ...errors, [name]: "" });
  };

  const handlePhoneNumberChange = (e) => {
    const value = e.target.value.replace(/\D/g, "").slice(0, 10);
    setFormData({ ...formData, mobile: value });
    setShowSendOTPButton(value.length === 10);
    if (value.length !== 10) {
      setOtpVerified(false);
      setOtpError("");
    }
    setErrors({ ...errors, mobile: "" });
  };

  const handleSendOTPClick = (e) => {
    e.preventDefault();
    const newOTP = Math.floor(1000 + Math.random() * 9000).toString();
    setGeneratedOTP(newOTP);
    alert(`Your OTP is: ${newOTP}`); // In a real app, this would be sent via SMS
    setShowOTPModal(true);
    setOTP(["", "", "", ""]);
    setOtpVerified(false);
    setOtpError("");
  };

  const handleOTPChange = (index, value) => {
    const newOTP = [...otp];
    newOTP[index] = value.replace(/\D/g, "").slice(0, 1);
    setOTP(newOTP);

    if (value && index < 3) {
      const nextInput = document.getElementById(`otp-${index + 1}`);
      if (nextInput) nextInput.focus();
    }
  };

  const handleVerifyOTP = () => {
    const enteredOTP = otp.join("");
    if (enteredOTP === generatedOTP) {
      setOtpVerified(true);
      setOtpError("");
      setShowOTPModal(false);
    } else {
      setOtpVerified(false);
      setOtpError("Wrong OTP entered. Please try again.");
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const newErrors = {};

    // Validate required fields
    if (!formData.fullName) newErrors.fullName = "Full name is required";
    if (!formData.email) newErrors.email = "Email is required";
    if (!formData.password) newErrors.password = "Password is required";
    if (!formData.mobile) newErrors.mobile = "Mobile number is required";
    if (!formData.designation)
      newErrors.designation = "Designation is required";

    // Profile picture is optional, but validate if present
    if (!formData.organization)
      newErrors.organization = "Organization is required";
    if (profilePic) {
      const validTypes = ["image/jpeg", "image/png", "image/jpg"];
      if (!validTypes.includes(profilePic.type)) {
        newErrors.profilePic = "Only JPG, JPEG, or PNG files are allowed.";
      }
      if (profilePic.size > 5 * 1024 * 1024) {
        newErrors.profilePic = "File size must be less than 5MB.";
      }
    }

    // Validate phone number availability and verification
    if (phoneAvailable === false) {
      newErrors.mobile = "Phone number already exists. Please use a different number.";
    } else if (!otpVerified) {
      newErrors.mobile = "Please verify your phone number";
    }

    // Validate email
    if (emailValidation.isChecking) {
      newErrors.email = "Please wait while we check email availability";
    } else if (emailValidation.isValid === false) {
      newErrors.email = emailValidation.message || "Email validation failed";
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
    } else {
      // Form is valid, proceed with submission
      setIsLoading(true); // Set loading state immediately when form is valid
      const finalFormData = {
        emp_email: formData.email,
        password: formData.password,
        emp_name: formData.fullName,
        emp_mobile: formData.mobile,
        designation: formData.designation,
        organization: formData.organization,
        profile_picture: profilePic
          ? profilePic
          : profilePicPreview &&
            typeof profilePicPreview === "string" &&
            profilePicPreview.startsWith("http") &&
            profilePicPreview.trim() !== ""
          ? profilePicPreview
          : null,
      };
      registerEmployer(finalFormData);
    }
  };

  async function registerEmployer(finalFormData) {
    try {
      setIsLoading(true);

      // Use FormData for file upload
      const formPayload = new FormData();
      formPayload.append("emp_email", finalFormData.emp_email);
      formPayload.append("email", finalFormData.emp_email); // For user table uniqueness
      formPayload.append("password", finalFormData.password);
      formPayload.append("emp_name", finalFormData.emp_name);
      formPayload.append("emp_mobile", finalFormData.emp_mobile);
      formPayload.append("designation", finalFormData.designation);
      formPayload.append("organization", finalFormData.organization);
      if (
        finalFormData.profile_picture &&
        typeof finalFormData.profile_picture !== "string"
      ) {
        formPayload.append("profile_picture", finalFormData.profile_picture);
      }
      if (
        finalFormData.profile_picture &&
        typeof finalFormData.profile_picture === "string" &&
        finalFormData.profile_picture.startsWith("http")
      ) {
        formPayload.append(
          "profile_picture_url",
          finalFormData.profile_picture
        );
      }

      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/register-employeer`,
        {
          method: "POST",
          body: formPayload,
        }
      );

      const resdata = await response.json();

      if (response.ok) {
        console.log(resdata.message); // "Employer registered successfully"
        jsregcookie(resdata); // Store the token, role, candId in a cookie
        navigate("/employer/dashboard"); // Redirect to the employer dashboard
        toast.success("Registered Successfully");
      } else if (response.status === 400) {
        console.error(resdata.message); // Handle validation or missing field errors
        toast.error(resdata.message);
      } else if (response.status === 409) {
        console.error(resdata.message); // "Email already exists"
        toast.error(resdata.message);
      } else if (response.status === 500) {
        console.error(resdata.error); // Detailed error message
        toast.error(resdata.message);
      } else {
        console.error("An unexpected error occurred");
        toast.error(resdata.message);
      }
    } catch (error) {
      toast.error(error?.response?.data?.message || "Something went wrong");
      console.error("Network error:", error);
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <EmpCreateAccountForm
      isProcessing={isLoading}
      navigate={navigate}
      LogoImage={LogoImage}
      videoRes={videoRes}
      profilePicPreview={profilePicPreview}
      imageError={imageError}
      setImageError={setImageError}
      profilePic={profilePic}
      profilePicError={profilePicError}
      errors={errors}
      handleProfilePicChange={handleProfilePicChange}
      profilePicInputRef={profilePicInputRef}
      handleRemoveProfilePic={handleRemoveProfilePic}
      formData={formData}
      setFormData={setFormData}
      handleInputChange={handleInputChange}
      emailValidation={emailValidation}
      handlePhoneNumberChange={handlePhoneNumberChange}
      showSendOTPButton={showSendOTPButton}
      otpVerified={otpVerified}
      handleSendOTPClick={handleSendOTPClick}
      otp={otp}
      otpError={otpError}
      handleOTPChange={handleOTPChange}
      handleVerifyOTP={handleVerifyOTP}
      showOTPModal={showOTPModal}
      setShowOTPModal={setShowOTPModal}
      handleSubmit={handleSubmit}
      phoneAvailable={phoneAvailable}
      phoneCheckLoading={phoneCheckLoading}
      phoneCheckMsg={phoneCheckMsg}
    />
  );
};

export default CreateAccount;