import React, { useState, useEffect } from "react";
import {
  User,
  Lock,
  Bell,
  Shield,
  Building,
  Upload,
  FileText,
  Camera,
  Check,
  EyeIcon,
  EyeOffIcon,
  Globe,
  Mail,
  Phone,
  MapPin,
  Settings
} from "lucide-react";
import Cookies from "js-cookie";
import toast from "react-hot-toast";
import { useNavigate } from "react-router-dom";

const EmployerSettings = () => {
  const navigate = useNavigate();
  const empId = Cookies.get("empId");

  // Active tab state
  const [activeTab, setActiveTab] = useState("company");

  // Company profile state
  const [companyData, setCompanyData] = useState({
    companyName: "",
    companyUrl: "",
    description: "",
    industry: "",
    companySize: "",
    website: "",
    location: "",
    logo: null
  });

  // Personal profile state
  const [personalData, setPersonalData] = useState({
    name: "",
    email: "",
    phone: "",
    position: "",
    department: "",
    profileImage: null
  });

  // Password state
  const [passwordData, setPasswordData] = useState({
    oldPassword: "",
    newPassword: "",
    confirmPassword: ""
  });

  // Notification state
  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    candidateApplications: true,
    systemUpdates: true,
    marketingEmails: false,
    weeklyReports: true
  });

  // Privacy state
  const [privacySettings, setPrivacySettings] = useState({
    companyVisibility: "public",
    showContactInfo: true,
    allowCandidateMessages: true,
    shareCompanyData: false
  });

  // Loading states
  const [loading, setLoading] = useState({
    company: false,
    personal: false,
    password: false,
    notifications: false,
    privacy: false
  });

  // Other states
  const [showPasswords, setShowPasswords] = useState({
    old: false,
    new: false,
    confirm: false
  });
  const [isEditingCompany, setIsEditingCompany] = useState(false);
  const [isEditingPersonal, setIsEditingPersonal] = useState(false);
  const [uploadedFile, setUploadedFile] = useState(null);
  const [errors, setErrors] = useState({});

  // Fetch employer data
  useEffect(() => {
    const fetchEmployerData = async () => {
      if (!empId) {
        toast.error("No Token Found, Please Login Again");
        navigate("/employer/sign-in");
        return;
      }

      try {
        // Simulate API call
        const mockData = {
          company: {
            companyName: "Tech Innovations Inc.",
            companyUrl: "tech-innovations",
            description: "Leading technology solutions provider",
            industry: "Technology",
            companySize: "51-200",
            website: "https://techinnovations.com",
            location: "San Francisco, CA",
            logo: null
          },
          personal: {
            name: "John Smith",
            email: "<EMAIL>",
            phone: "+****************",
            position: "HR Manager",
            department: "Human Resources",
            profileImage: null
          }
        };
        
        setCompanyData(mockData.company);
        setPersonalData(mockData.personal);
      } catch (error) {
        console.error("Failed to fetch employer data:", error);
        toast.error("Failed to load profile data");
      }
    };

    fetchEmployerData();
  }, [empId, navigate]);

  // Handle password change
  const handlePasswordChange = async () => {
    if (!passwordData.oldPassword || !passwordData.newPassword || !passwordData.confirmPassword) {
      toast.error("Please fill in all password fields.");
      return;
    }

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      toast.error("New passwords do not match.");
      return;
    }

    if (passwordData.newPassword.length < 8) {
      toast.error("Password must be at least 8 characters long.");
      return;
    }

    setLoading(prev => ({ ...prev, password: true }));

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      setPasswordData({ oldPassword: "", newPassword: "", confirmPassword: "" });
      toast.success("Password updated successfully!");
    } catch (error) {
      toast.error("An unexpected error occurred.");
    } finally {
      setLoading(prev => ({ ...prev, password: false }));
    }
  };

  // Handle company update
  const handleCompanyUpdate = async () => {
    setLoading(prev => ({ ...prev, company: true }));
    
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success("Company profile updated successfully!");
      setIsEditingCompany(false);
    } catch (error) {
      toast.error("Failed to update company profile");
    } finally {
      setLoading(prev => ({ ...prev, company: false }));
    }
  };

  // Handle personal update
  const handlePersonalUpdate = async () => {
    setLoading(prev => ({ ...prev, personal: true }));
    
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success("Personal profile updated successfully!");
      setIsEditingPersonal(false);
    } catch (error) {
      toast.error("Failed to update personal profile");
    } finally {
      setLoading(prev => ({ ...prev, personal: false }));
    }
  };

  // Handle logo upload
  const handleLogoUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      if (file.type.startsWith('image/') && file.size <= 2 * 1024 * 1024) {
        const imageUrl = URL.createObjectURL(file);
        setCompanyData(prev => ({ ...prev, logo: imageUrl }));
        toast.success("Company logo updated!");
      } else {
        toast.error("Please upload a valid image file under 2MB.");
      }
    }
  };

  // Handle profile image upload
  const handleProfileImageUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      if (file.type.startsWith('image/') && file.size <= 2 * 1024 * 1024) {
        const imageUrl = URL.createObjectURL(file);
        setPersonalData(prev => ({ ...prev, profileImage: imageUrl }));
        toast.success("Profile image updated!");
      } else {
        toast.error("Please upload a valid image file under 2MB.");
      }
    }
  };

  // Handle file upload
  const handleFileUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      const isPdf = file.type === "application/pdf";
      if (isPdf && file.size <= 5 * 1024 * 1024) {
        setUploadedFile(file);
        setErrors(prev => ({ ...prev, resume: "" }));
        toast.success("Document uploaded successfully!");
      } else {
        setErrors(prev => ({ ...prev, resume: "Please upload a valid PDF file under 5MB." }));
        setUploadedFile(null);
      }
    }
  };

  // Tab configuration
  const tabs = [
    { id: "company", label: "Company", icon: Building },
    { id: "personal", label: "Personal", icon: User },
    { id: "security", label: "Security", icon: Lock },
    { id: "notifications", label: "Notifications", icon: Bell },
    { id: "privacy", label: "Privacy", icon: Shield }
  ];

  return (
    <div className="p-3 max-w-6xl mx-auto">
      {/* Compact Header */}
      <div className="bg-white dark:bg-slate-800 rounded-2xl border border-slate-200 dark:border-slate-700 mb-6">
        <div className="flex items-center justify-between p-5">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-600 rounded-xl">
              <Settings className="h-5 w-5 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-slate-900 dark:text-white font-sora">
                Settings
              </h1>
              <p className="text-sm text-slate-600 dark:text-slate-400 font-manrope">
                Manage your account
              </p>
            </div>
          </div>
          <div className="hidden sm:flex items-center gap-2 bg-slate-50 dark:bg-slate-700 rounded-lg px-3 py-2">
            <Building className="w-4 h-4 text-slate-500" />
            <span className="text-sm text-slate-600 dark:text-slate-400 font-manrope">
              Employer
            </span>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="bg-white dark:bg-slate-800 rounded-2xl border border-slate-200 dark:border-slate-700">
        {/* Compact Tab Navigation */}
        <div className="border-b border-slate-200 dark:border-slate-700 p-4">
          <nav className="flex flex-wrap gap-1">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              const isActive = activeTab === tab.id;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-all font-manrope ${
                    isActive
                      ? 'bg-blue-600 text-white shadow-md'
                      : 'text-slate-600 dark:text-slate-400 hover:bg-slate-100 dark:hover:bg-slate-700'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span className="hidden sm:block">{tab.label}</span>
                </button>
              );
            })}
          </nav>
        </div>
        <div className="p-4">
          {/* Content Area */}
          <div>
            {/* Company Profile Tab */}
            {activeTab === "company" && (
              <div>
                {/* Compact Section Header */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <Building className="w-4 h-4 text-blue-600" />
                    <h2 className="text-lg font-semibold text-slate-900 dark:text-white font-sora">
                      Company Profile
                    </h2>
                  </div>
                  <button
                    onClick={() => isEditingCompany ? handleCompanyUpdate() : setIsEditingCompany(true)}
                    disabled={loading.company}
                    className={`flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-all font-manrope ${
                      loading.company
                        ? 'bg-slate-400 text-white cursor-not-allowed'
                        : isEditingCompany
                        ? 'bg-green-600 hover:bg-green-700 text-white'
                        : 'bg-blue-600 hover:bg-blue-700 text-white'
                    }`}
                  >
                    {loading.company ? (
                      <>
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        Saving...
                      </>
                    ) : isEditingCompany ? (
                      <>
                        <Check className="w-4 h-4" />
                        Save Changes
                      </>
                    ) : (
                      "Edit Company"
                    )}
                  </button>
                </div>

                {/* Company Logo Section */}
                <div className="flex items-center gap-6 mb-6 pb-6 border-b border-slate-200 dark:border-slate-600">
                  <div className="relative">
                    <img
                      src={companyData.logo || "https://images.unsplash.com/photo-1560179707-f14e90ef3623?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&h=256&q=80"}
                      alt="Company Logo"
                      className="w-20 h-20 rounded-xl object-cover border-4 border-white dark:border-slate-800 shadow-lg"
                    />
                    {isEditingCompany && (
                      <label className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded-xl cursor-pointer opacity-0 hover:opacity-100 transition-opacity">
                        <Camera className="w-6 h-6 text-white" />
                        <input
                          type="file"
                          accept="image/*"
                          onChange={handleLogoUpload}
                          className="hidden"
                        />
                      </label>
                    )}
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-slate-900 dark:text-white font-sora">
                      {companyData.companyName || "Company Name"}
                    </h3>
                    <p className="text-sm text-slate-600 dark:text-slate-400 font-manrope mt-1">
                      {companyData.industry || "Industry"}
                    </p>
                    {isEditingCompany && (
                      <p className="text-xs text-slate-500 dark:text-slate-500 font-manrope mt-2">
                        Click on logo to change company logo
                      </p>
                    )}
                  </div>
                </div>

                {/* Company Form */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {[
                    { key: 'companyName', label: 'Company Name', type: 'text' },
                    { key: 'companyUrl', label: 'Company URL', type: 'text' },
                    { key: 'industry', label: 'Industry', type: 'select', options: [
                      { value: '', label: 'Select Industry' },
                      { value: 'Technology', label: 'Technology' },
                      { value: 'Healthcare', label: 'Healthcare' },
                      { value: 'Finance', label: 'Finance' },
                      { value: 'Education', label: 'Education' },
                      { value: 'Manufacturing', label: 'Manufacturing' },
                      { value: 'Retail', label: 'Retail' },
                      { value: 'Other', label: 'Other' }
                    ]},
                    { key: 'companySize', label: 'Company Size', type: 'select', options: [
                      { value: '', label: 'Select Size' },
                      { value: '1-10', label: '1-10 employees' },
                      { value: '11-50', label: '11-50 employees' },
                      { value: '51-200', label: '51-200 employees' },
                      { value: '201-500', label: '201-500 employees' },
                      { value: '501-1000', label: '501-1000 employees' },
                      { value: '1000+', label: '1000+ employees' }
                    ]},
                    { key: 'website', label: 'Website', type: 'url', placeholder: 'https://example.com' },
                    { key: 'location', label: 'Location', type: 'text', placeholder: 'e.g., San Francisco, CA' }
                  ].map((field) => (
                    <div key={field.key}>
                      <label className="block text-sm font-medium text-slate-900 dark:text-white font-manrope mb-2">
                        {field.label}
                      </label>
                      {field.type === 'select' ? (
                        <select
                          value={companyData[field.key]}
                          onChange={(e) => setCompanyData(prev => ({ ...prev, [field.key]: e.target.value }))}
                          disabled={!isEditingCompany}
                          className={`w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-900 dark:text-white font-manrope text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors ${
                            !isEditingCompany ? 'opacity-60 cursor-not-allowed' : ''
                          }`}
                        >
                          {field.options.map(opt => (
                            <option key={opt.value} value={opt.value}>{opt.label}</option>
                          ))}
                        </select>
                      ) : (
                        <input
                          type={field.type}
                          value={companyData[field.key]}
                          onChange={(e) => setCompanyData(prev => ({ ...prev, [field.key]: e.target.value }))}
                          disabled={!isEditingCompany}
                          placeholder={field.placeholder}
                          className={`w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-900 dark:text-white font-manrope text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors ${
                            !isEditingCompany ? 'opacity-60 cursor-not-allowed' : ''
                          }`}
                        />
                      )}
                    </div>
                  ))}

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-slate-900 dark:text-white font-manrope mb-2">
                      Description
                    </label>
                    <textarea
                      value={companyData.description}
                      onChange={(e) => setCompanyData(prev => ({ ...prev, description: e.target.value }))}
                      disabled={!isEditingCompany}
                      rows={4}
                      className={`w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-900 dark:text-white font-manrope text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors resize-vertical ${
                        !isEditingCompany ? 'opacity-60 cursor-not-allowed' : ''
                      }`}
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Personal Profile Tab */}
            {activeTab === "personal" && (
              <div>
                {/* Compact Section Header */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <User className="w-4 h-4 text-blue-600" />
                    <h2 className="text-lg font-semibold text-slate-900 dark:text-white font-sora">
                      Personal Profile
                    </h2>
                  </div>
                  <button
                    onClick={() => isEditingPersonal ? handlePersonalUpdate() : setIsEditingPersonal(true)}
                    disabled={loading.personal}
                    className={`flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-all font-manrope ${
                      loading.personal
                        ? 'bg-slate-400 text-white cursor-not-allowed'
                        : isEditingPersonal
                        ? 'bg-green-600 hover:bg-green-700 text-white'
                        : 'bg-blue-600 hover:bg-blue-700 text-white'
                    }`}
                  >
                    {loading.personal ? (
                      <>
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        Saving...
                      </>
                    ) : isEditingPersonal ? (
                      <>
                        <Check className="w-4 h-4" />
                        Save Changes
                      </>
                    ) : (
                      "Edit Profile"
                    )}
                  </button>
                </div>

                {/* Profile Image Section */}
                <div className="flex items-center gap-6 mb-6 pb-6 border-b border-slate-200 dark:border-slate-600">
                  <div className="relative">
                    <img
                      src={personalData.profileImage || "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"}
                      alt="Profile"
                      className="w-20 h-20 rounded-full object-cover border-4 border-white dark:border-slate-800 shadow-lg"
                    />
                    {isEditingPersonal && (
                      <label className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded-full cursor-pointer opacity-0 hover:opacity-100 transition-opacity">
                        <Camera className="w-6 h-6 text-white" />
                        <input
                          type="file"
                          accept="image/*"
                          onChange={handleProfileImageUpload}
                          className="hidden"
                        />
                      </label>
                    )}
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-slate-900 dark:text-white font-sora">
                      {personalData.name || "Full Name"}
                    </h3>
                    <p className="text-sm text-slate-600 dark:text-slate-400 font-manrope mt-1">
                      {personalData.position || "Position"}
                    </p>
                    {isEditingPersonal && (
                      <p className="text-xs text-slate-500 dark:text-slate-500 font-manrope mt-2">
                        Click on image to change profile photo
                      </p>
                    )}
                  </div>
                </div>

                {/* Personal Form */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {[
                    { key: 'name', label: 'Full Name', type: 'text' },
                    { key: 'email', label: 'Email Address', type: 'email' },
                    { key: 'phone', label: 'Phone Number', type: 'tel' },
                    { key: 'position', label: 'Position', type: 'text' },
                    { key: 'department', label: 'Department', type: 'text', span: 2 }
                  ].map((field) => (
                    <div key={field.key} className={field.span ? 'md:col-span-2' : ''}>
                      <label className="block text-sm font-medium text-slate-900 dark:text-white font-manrope mb-2">
                        {field.label}
                      </label>
                      <input
                        type={field.type}
                        value={personalData[field.key]}
                        onChange={(e) => setPersonalData(prev => ({ ...prev, [field.key]: e.target.value }))}
                        disabled={!isEditingPersonal}
                        className={`w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-900 dark:text-white font-manrope text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors ${
                          !isEditingPersonal ? 'opacity-60 cursor-not-allowed' : ''
                        }`}
                      />
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Security Tab */}
            {activeTab === "security" && (
              <div>
                {/* Compact Section Header */}
                <div className="flex items-center gap-2 mb-4">
                  <Lock className="w-4 h-4 text-red-600" />
                  <h2 className="text-lg font-semibold text-slate-900 dark:text-white font-sora">
                    Security Settings
                  </h2>
                </div>

                <div className="max-w-2xl">
                  {/* Compact Password Change Card */}
                  <div className="bg-slate-50 dark:bg-slate-700/50 border border-slate-200 dark:border-slate-600 rounded-xl p-4">
                    <div className="flex items-center gap-2 mb-4">
                      <Lock className="w-4 h-4 text-blue-600" />
                      <h3 className="font-semibold text-slate-900 dark:text-white font-sora">
                        Change Password
                      </h3>
                    </div>

                    <div className="space-y-4">
                      {[
                        { key: 'oldPassword', label: 'Current Password', placeholder: 'Enter your current password' },
                        { key: 'newPassword', label: 'New Password', placeholder: 'Enter a new password' },
                        { key: 'confirmPassword', label: 'Confirm New Password', placeholder: 'Confirm your new password' }
                      ].map((field) => (
                        <div key={field.key}>
                          <label className="block text-sm font-medium text-slate-900 dark:text-white font-manrope mb-2">
                            {field.label}
                          </label>
                          <div className="relative">
                            <input
                              type={showPasswords[field.key.replace('Password', '')] ? "text" : "password"}
                              value={passwordData[field.key]}
                              onChange={(e) => setPasswordData(prev => ({ ...prev, [field.key]: e.target.value }))}
                              placeholder={field.placeholder}
                              className="w-full px-3 py-2 pr-10 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-900 dark:text-white font-manrope text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                            />
                            <button
                              type="button"
                              onClick={() => setShowPasswords(prev => ({
                                ...prev,
                                [field.key.replace('Password', '')]: !prev[field.key.replace('Password', '')]
                              }))}
                              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600 dark:hover:text-slate-300"
                            >
                              {showPasswords[field.key.replace('Password', '')] ?
                                <EyeOffIcon className="w-4 h-4" /> :
                                <EyeIcon className="w-4 h-4" />
                              }
                            </button>
                          </div>
                        </div>
                      ))}

                      <button
                        onClick={handlePasswordChange}
                        disabled={loading.password}
                        className={`flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-all font-manrope ${
                          loading.password
                            ? 'bg-slate-400 text-white cursor-not-allowed'
                            : 'bg-blue-600 hover:bg-blue-700 text-white'
                        }`}
                      >
                        {loading.password ? (
                          <>
                            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                            Updating...
                          </>
                        ) : (
                          "Update Password"
                        )}
                      </button>
                    </div>

                    {/* Security Tips */}
                    <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                      <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 font-manrope mb-3">
                        Password Security Tips
                      </h4>
                      <ul className="space-y-2">
                        {[
                          'Use at least 8 characters with a mix of letters, numbers, and symbols',
                          'Avoid using personal information like names or birthdays',
                          'Don\'t reuse passwords from other accounts',
                          'Consider using a password manager for better security'
                        ].map((tip, index) => (
                          <li key={index} className="flex items-start gap-2 text-sm text-blue-800 dark:text-blue-200 font-manrope">
                            <Check className="w-4 h-4 mt-0.5 flex-shrink-0 text-green-600" />
                            {tip}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Notifications Tab */}
            {activeTab === "notifications" && (
              <div>
                {/* Compact Section Header */}
                <div className="flex items-center gap-2 mb-4">
                  <Bell className="w-4 h-4 text-yellow-600" />
                  <h2 className="text-lg font-semibold text-slate-900 dark:text-white font-sora">
                    Notification Preferences
                  </h2>
                </div>

                <div className="space-y-4">
                  {[
                    { key: "emailNotifications", label: "Email Notifications", description: "Receive notifications via email", icon: "📧" },
                    { key: "candidateApplications", label: "Candidate Applications", description: "Get notified about new candidate applications", icon: "👤" },
                    { key: "systemUpdates", label: "System Updates", description: "Receive notifications about system updates and maintenance", icon: "🔧" },
                    { key: "weeklyReports", label: "Weekly Reports", description: "Receive weekly analytics and performance reports", icon: "📊" },
                    { key: "marketingEmails", label: "Marketing Emails", description: "Receive promotional emails and newsletters", icon: "📢" }
                  ].map((setting) => (
                    <div
                      key={setting.key}
                      className="bg-slate-50 dark:bg-slate-700/50 border border-slate-200 dark:border-slate-600 rounded-lg p-3 hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="text-lg">{setting.icon}</div>
                          <div>
                            <h3 className="text-sm font-semibold text-slate-900 dark:text-white font-sora">
                              {setting.label}
                            </h3>
                            <p className="text-xs text-slate-600 dark:text-slate-400 font-manrope">
                              {setting.description}
                            </p>
                          </div>
                        </div>
                        <label className="relative inline-flex cursor-pointer items-center">
                          <input
                            type="checkbox"
                            checked={notificationSettings[setting.key]}
                            onChange={(e) => setNotificationSettings(prev => ({ ...prev, [setting.key]: e.target.checked }))}
                            className="sr-only peer"
                          />
                          <div className="relative w-11 h-6 bg-slate-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-slate-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-slate-600 peer-checked:bg-blue-600"></div>
                        </label>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="mt-6">
                  <button
                    onClick={() => {
                      setLoading(prev => ({ ...prev, notifications: true }));
                      setTimeout(() => {
                        setLoading(prev => ({ ...prev, notifications: false }));
                        toast.success("Notification preferences updated!");
                      }, 1000);
                    }}
                    disabled={loading.notifications}
                    className={`flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-all font-manrope ${
                      loading.notifications
                        ? 'bg-slate-400 text-white cursor-not-allowed'
                        : 'bg-blue-600 hover:bg-blue-700 text-white'
                    }`}
                  >
                    {loading.notifications ? (
                      <>
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        Saving...
                      </>
                    ) : (
                      "Save Preferences"
                    )}
                  </button>
                </div>
              </div>
            )}

        {/* Privacy Tab */}
        {activeTab === "privacy" && (
          <div style={cardStyle}>
            <div style={{ marginBottom: 'var(--space-8)' }}>
              <h2 style={{
                fontFamily: 'Sora, sans-serif',
                fontSize: '36px',
                fontWeight: '600',
                lineHeight: '1.3',
                color: 'var(--text-primary)',
                margin: '0 0 var(--space-1) 0'
              }}>Privacy Settings</h2>
              <p style={{
                fontFamily: 'Manrope, sans-serif',
                fontSize: '16px',
                color: 'var(--text-secondary)',
                margin: '0'
              }}>Control who can see your information and how it's used</p>
            </div>

            <div style={{ display: 'flex', flexDirection: 'column', gap: 'var(--space-8)' }}>
              <div>
                <h3 style={{
                  fontFamily: 'Sora, sans-serif',
                  fontSize: '20px',
                  fontWeight: '500',
                  color: 'var(--text-primary)',
                  marginBottom: 'var(--space-4)'
                }}>Company Visibility</h3>
                <div style={{ display: 'flex', flexDirection: 'column', gap: 'var(--space-3)' }}>
                  {[
                    { value: "public", label: "Public", description: "Anyone can view your company profile" },
                    { value: "verified", label: "Verified Users Only", description: "Only verified candidates can view your profile" },
                    { value: "private", label: "Private", description: "Only you can view your company profile" }
                  ].map((option) => (
                    <label key={option.value} style={{
                      display: 'flex',
                      alignItems: 'center',
                      padding: 'var(--space-4)',
                      border: '1px solid var(--bg-tertiary)',
                      borderRadius: '12px',
                      cursor: 'pointer',
                      transition: 'background-color 0.2s ease'
                    }}
                    onMouseEnter={(e) => e.target.style.background = 'var(--hover)'}
                    onMouseLeave={(e) => e.target.style.background = 'transparent'}
                    >
                      <input
                        type="radio"
                        name="companyVisibility"
                        value={option.value}
                        checked={privacySettings.companyVisibility === option.value}
                        onChange={(e) => setPrivacySettings(prev => ({ ...prev, companyVisibility: e.target.value }))}
                        style={{
                          width: '16px',
                          height: '16px',
                          accentColor: 'var(--brand-primary)',
                          marginRight: 'var(--space-3)'
                        }}
                      />
                      <div>
                        <div style={{
                          fontFamily: 'Manrope, sans-serif',
                          fontSize: '16px',
                          fontWeight: '500',
                          color: 'var(--text-primary)',
                          margin: '0 0 var(--space-1) 0'
                        }}>{option.label}</div>
                        <div style={{
                          fontFamily: 'Manrope, sans-serif',
                          fontSize: '14px',
                          color: 'var(--text-secondary)',
                          margin: '0'
                        }}>{option.description}</div>
                      </div>
                    </label>
                  ))}
                </div>
              </div>

              <div style={{ display: 'flex', flexDirection: 'column', gap: 'var(--space-4)' }}>
                {[
                  { key: "showContactInfo", label: "Show Contact Information", description: "Allow candidates to see your contact details" },
                  { key: "allowCandidateMessages", label: "Allow Candidate Messages", description: "Let candidates send you messages directly" },
                  { key: "shareCompanyData", label: "Share Company Analytics", description: "Allow anonymized company data to be used for platform insights" }
                ].map((setting) => (
                  <div key={setting.key} style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    padding: 'var(--space-4)',
                    border: '1px solid var(--bg-tertiary)',
                    borderRadius: '12px'
                  }}>
                    <div>
                      <h3 style={{
                        fontFamily: 'Manrope, sans-serif',
                        fontSize: '16px',
                        fontWeight: '500',
                        color: 'var(--text-primary)',
                        margin: '0 0 var(--space-1) 0'
                      }}>{setting.label}</h3>
                      <p style={{
                        fontFamily: 'Manrope, sans-serif',
                        fontSize: '14px',
                        color: 'var(--text-secondary)',
                        margin: '0'
                      }}>{setting.description}</p>
                    </div>
                    <label style={{
                      position: 'relative',
                      display: 'inline-flex',
                      alignItems: 'center',
                      cursor: 'pointer'
                    }}>
                      <input
                        type="checkbox"
                        checked={privacySettings[setting.key]}
                        onChange={(e) => setPrivacySettings(prev => ({ ...prev, [setting.key]: e.target.checked }))}
                        style={{
                          position: 'absolute',
                          opacity: 0,
                          width: 0,
                          height: 0
                        }}
                      />
                      <div style={{
                        width: '44px',
                        height: '24px',
                        background: privacySettings[setting.key] ? 'var(--brand-primary)' : 'var(--bg-accent)',
                        borderRadius: '12px',
                        position: 'relative',
                        transition: 'background-color 0.2s ease'
                      }}>
                        <div style={{
                          position: 'absolute',
                          top: '2px',
                          left: privacySettings[setting.key] ? '22px' : '2px',
                          width: '20px',
                          height: '20px',
                          background: 'white',
                          borderRadius: '50%',
                          transition: 'left 0.2s ease',
                          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.2)'
                        }} />
                      </div>
                    </label>
                  </div>
                ))}
              </div>

              <div>
                <button
                  onClick={() => {
                    setLoading(prev => ({ ...prev, privacy: true }));
                    setTimeout(() => {
                      setLoading(prev => ({ ...prev, privacy: false }));
                      toast.success("Privacy settings updated!");
                    }, 1000);
                  }}
                  disabled={loading.privacy}
                  style={buttonStyle('primary', loading.privacy)}
                  onMouseEnter={(e) => {
                    if (!loading.privacy) {
                      e.target.style.background = '#2563EB';
                      e.target.style.transform = 'translateY(-1px)';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!loading.privacy) {
                      e.target.style.background = 'var(--brand-primary)';
                      e.target.style.transform = 'translateY(0)';
                    }
                  }}
                >
                  {loading.privacy ? (
                    <>
                      <div style={{
                        width: '16px',
                        height: '16px',
                        border: '2px solid white',
                        borderTop: '2px solid transparent',
                        borderRadius: '50%',
                        animation: 'spin 1s linear infinite'
                      }}></div>
                      Saving...
                    </>
                  ) : (
                    "Save Settings"
                  )}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Add CSS keyframes for animations */}
      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default EmployerSettings;