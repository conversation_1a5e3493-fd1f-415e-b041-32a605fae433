import React, { useState, useEffect } from "react";
import { 
  User, 
  Lock, 
  Bell, 
  Shield, 
  Building, 
  Upload,
  FileText,
  Camera,
  Check,
  EyeIcon,
  EyeOffIcon,
  Globe,
  Mail,
  Phone,
  MapPin
} from "lucide-react";
import Cookies from "js-cookie";
import toast from "react-hot-toast";
import { useNavigate } from "react-router-dom";

const EmployerSettings = () => {
  const navigate = useNavigate();
  const empId = Cookies.get("empId");
  
  // Active tab state
  const [activeTab, setActiveTab] = useState("company");
  
  // Company profile state
  const [companyData, setCompanyData] = useState({
    companyName: "",
    companyUrl: "",
    description: "",
    industry: "",
    companySize: "",
    website: "",
    location: "",
    logo: null
  });
  
  // Personal profile state
  const [personalData, setPersonalData] = useState({
    name: "",
    email: "",
    phone: "",
    position: "",
    department: "",
    profileImage: null
  });
  
  // Password state
  const [passwordData, setPasswordData] = useState({
    oldPassword: "",
    newPassword: "",
    confirmPassword: ""
  });
  
  // Notification state
  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    candidateApplications: true,
    systemUpdates: true,
    marketingEmails: false,
    weeklyReports: true
  });
  
  // Privacy state
  const [privacySettings, setPrivacySettings] = useState({
    companyVisibility: "public",
    showContactInfo: true,
    allowCandidateMessages: true,
    shareCompanyData: false
  });
  
  // Loading states
  const [loading, setLoading] = useState({
    company: false,
    personal: false,
    password: false,
    notifications: false,
    privacy: false
  });
  
  // Other states
  const [showPasswords, setShowPasswords] = useState({
    old: false,
    new: false,
    confirm: false
  });
  const [isEditingCompany, setIsEditingCompany] = useState(false);
  const [isEditingPersonal, setIsEditingPersonal] = useState(false);
  const [uploadedFile, setUploadedFile] = useState(null);
  const [errors, setErrors] = useState({});

  // Fetch employer data
  useEffect(() => {
    const fetchEmployerData = async () => {
      if (!empId) {
        toast.error("No Token Found, Please Login Again");
        navigate("/employer/sign-in");
        return;
      }

      try {
        // Simulate API call
        const mockData = {
          company: {
            companyName: "Tech Innovations Inc.",
            companyUrl: "tech-innovations",
            description: "Leading technology solutions provider",
            industry: "Technology",
            companySize: "51-200",
            website: "https://techinnovations.com",
            location: "San Francisco, CA",
            logo: null
          },
          personal: {
            name: "John Smith",
            email: "<EMAIL>",
            phone: "+****************",
            position: "HR Manager",
            department: "Human Resources",
            profileImage: null
          }
        };
        
        setCompanyData(mockData.company);
        setPersonalData(mockData.personal);
      } catch (error) {
        console.error("Failed to fetch employer data:", error);
        toast.error("Failed to load profile data");
      }
    };

    fetchEmployerData();
  }, [empId, navigate]);

  // Handle password change
  const handlePasswordChange = async () => {
    if (!passwordData.oldPassword || !passwordData.newPassword || !passwordData.confirmPassword) {
      toast.error("Please fill in all password fields.");
      return;
    }

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      toast.error("New passwords do not match.");
      return;
    }

    if (passwordData.newPassword.length < 8) {
      toast.error("Password must be at least 8 characters long.");
      return;
    }

    setLoading(prev => ({ ...prev, password: true }));

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      setPasswordData({ oldPassword: "", newPassword: "", confirmPassword: "" });
      toast.success("Password updated successfully!");
    } catch (error) {
      toast.error("An unexpected error occurred.");
    } finally {
      setLoading(prev => ({ ...prev, password: false }));
    }
  };

  // Handle company update
  const handleCompanyUpdate = async () => {
    setLoading(prev => ({ ...prev, company: true }));
    
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success("Company profile updated successfully!");
      setIsEditingCompany(false);
    } catch (error) {
      toast.error("Failed to update company profile");
    } finally {
      setLoading(prev => ({ ...prev, company: false }));
    }
  };

  // Handle personal update
  const handlePersonalUpdate = async () => {
    setLoading(prev => ({ ...prev, personal: true }));
    
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success("Personal profile updated successfully!");
      setIsEditingPersonal(false);
    } catch (error) {
      toast.error("Failed to update personal profile");
    } finally {
      setLoading(prev => ({ ...prev, personal: false }));
    }
  };

  // Handle logo upload
  const handleLogoUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      if (file.type.startsWith('image/') && file.size <= 2 * 1024 * 1024) {
        const imageUrl = URL.createObjectURL(file);
        setCompanyData(prev => ({ ...prev, logo: imageUrl }));
        toast.success("Company logo updated!");
      } else {
        toast.error("Please upload a valid image file under 2MB.");
      }
    }
  };

  // Handle profile image upload
  const handleProfileImageUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      if (file.type.startsWith('image/') && file.size <= 2 * 1024 * 1024) {
        const imageUrl = URL.createObjectURL(file);
        setPersonalData(prev => ({ ...prev, profileImage: imageUrl }));
        toast.success("Profile image updated!");
      } else {
        toast.error("Please upload a valid image file under 2MB.");
      }
    }
  };

  // Handle file upload
  const handleFileUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      const isPdf = file.type === "application/pdf";
      if (isPdf && file.size <= 5 * 1024 * 1024) {
        setUploadedFile(file);
        setErrors(prev => ({ ...prev, resume: "" }));
        toast.success("Document uploaded successfully!");
      } else {
        setErrors(prev => ({ ...prev, resume: "Please upload a valid PDF file under 5MB." }));
        setUploadedFile(null);
      }
    }
  };

  // Tab configuration
  const tabs = [
    { id: "company", label: "Company Profile", icon: Building },
    { id: "personal", label: "Personal Profile", icon: User },
    { id: "security", label: "Security", icon: Lock },
    { id: "notifications", label: "Notifications", icon: Bell },
    { id: "privacy", label: "Privacy", icon: Shield }
  ];

  // Common styles
  const inputStyle = (disabled = false) => ({
    background: disabled ? 'var(--bg-accent)' : 'var(--bg-tertiary)',
    border: '1px solid var(--bg-accent)',
    borderRadius: '8px',
    padding: 'var(--space-3) var(--space-4)',
    fontFamily: 'Manrope, sans-serif',
    fontSize: '16px',
    color: 'var(--text-primary)',
    width: '100%',
    transition: 'border-color 0.2s ease',
    opacity: disabled ? '0.6' : '1'
  });

  const buttonStyle = (variant = 'primary', disabled = false) => ({
    background: variant === 'primary' ? 
      (disabled ? '#9CA3AF' : 'var(--brand-primary)') : 
      'transparent',
    color: variant === 'primary' ? 'white' : 'var(--text-primary)',
    border: variant === 'primary' ? 'none' : '1px solid var(--bg-tertiary)',
    borderRadius: '12px',
    padding: 'var(--space-3) var(--space-6)',
    fontFamily: 'Manrope, sans-serif',
    fontSize: '16px',
    fontWeight: '500',
    cursor: disabled ? 'not-allowed' : 'pointer',
    transition: 'all 0.2s ease',
    display: 'flex',
    alignItems: 'center',
    gap: 'var(--space-2)'
  });

  const cardStyle = {
    background: 'var(--bg-secondary)',
    borderRadius: '16px',
    padding: 'var(--space-6)',
    border: '1px solid var(--bg-tertiary)',
    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
  };

  return (
    <div style={{
      background: 'var(--bg-primary)',
      padding: 'var(--space-8)',
      minHeight: '100vh',
      fontFamily: 'Manrope, sans-serif'
    }}>
      {/* Tab Navigation */}
      <div style={{
        borderBottom: '1px solid var(--bg-tertiary)',
        marginBottom: 'var(--space-8)'
      }}>
        <nav style={{ display: 'flex', gap: 'var(--space-8)' }}>
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 'var(--space-3)',
                  padding: 'var(--space-3) var(--space-4)',
                  borderRadius: '8px',
                  color: activeTab === tab.id ? 'white' : 'var(--text-secondary)',
                  background: activeTab === tab.id ? 'var(--brand-primary)' : 'transparent',
                  border: 'none',
                  fontFamily: 'Manrope, sans-serif',
                  fontSize: '16px',
                  fontWeight: '500',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease'
                }}
                onMouseEnter={(e) => {
                  if (activeTab !== tab.id) {
                    e.target.style.background = 'var(--hover)';
                    e.target.style.color = 'var(--text-primary)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (activeTab !== tab.id) {
                    e.target.style.background = 'transparent';
                    e.target.style.color = 'var(--text-secondary)';
                  }
                }}
              >
                <Icon style={{ width: '20px', height: '20px' }} />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Content Area */}
      <div>
        {/* Company Profile Tab */}
        {activeTab === "company" && (
          <div style={cardStyle}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              marginBottom: 'var(--space-8)'
            }}>
              <div>
                <h2 style={{
                  fontFamily: 'Sora, sans-serif',
                  fontSize: '36px',
                  fontWeight: '600',
                  lineHeight: '1.3',
                  color: 'var(--text-primary)',
                  margin: '0 0 var(--space-1) 0'
                }}>Company Profile</h2>
                <p style={{
                  fontFamily: 'Manrope, sans-serif',
                  fontSize: '16px',
                  color: 'var(--text-secondary)',
                  margin: '0'
                }}>Manage your company information and branding</p>
              </div>
              <button
                onClick={() => isEditingCompany ? handleCompanyUpdate() : setIsEditingCompany(true)}
                disabled={loading.company}
                style={buttonStyle('primary', loading.company)}
                onMouseEnter={(e) => {
                  if (!loading.company) {
                    e.target.style.background = '#2563EB';
                    e.target.style.transform = 'translateY(-1px)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!loading.company) {
                    e.target.style.background = 'var(--brand-primary)';
                    e.target.style.transform = 'translateY(0)';
                  }
                }}
              >
                {loading.company ? (
                  <>
                    <div style={{
                      width: '16px',
                      height: '16px',
                      border: '2px solid white',
                      borderTop: '2px solid transparent',
                      borderRadius: '50%',
                      animation: 'spin 1s linear infinite'
                    }}></div>
                    Saving...
                  </>
                ) : isEditingCompany ? (
                  <>
                    <Check style={{ width: '16px', height: '16px' }} />
                    Save Changes
                  </>
                ) : (
                  "Edit Company"
                )}
              </button>
            </div>

            {/* Company Logo */}
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: 'var(--space-6)',
              marginBottom: 'var(--space-8)',
              paddingBottom: 'var(--space-8)',
              borderBottom: '1px solid var(--bg-tertiary)'
            }}>
              <div style={{ position: 'relative' }}>
                <img
                  src={companyData.logo || "https://images.unsplash.com/photo-1560179707-f14e90ef3623?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&h=256&q=80"}
                  alt="Company Logo"
                  style={{
                    width: '96px',
                    height: '96px',
                    borderRadius: '16px',
                    objectFit: 'cover',
                    border: '4px solid var(--bg-primary)',
                    boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)'
                  }}
                />
                {isEditingCompany && (
                  <label style={{
                    position: 'absolute',
                    inset: '0',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    background: 'rgba(0, 0, 0, 0.5)',
                    borderRadius: '16px',
                    cursor: 'pointer',
                    opacity: '0',
                    transition: 'opacity 0.2s ease'
                  }}
                  onMouseEnter={(e) => e.target.style.opacity = '1'}
                  onMouseLeave={(e) => e.target.style.opacity = '0'}
                  >
                    <Camera style={{ width: '24px', height: '24px', color: 'white' }} />
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleLogoUpload}
                      style={{ display: 'none' }}
                    />
                  </label>
                )}
              </div>
              <div>
                <h3 style={{
                  fontFamily: 'Sora, sans-serif',
                  fontSize: '20px',
                  fontWeight: '500',
                  lineHeight: '1.5',
                  color: 'var(--text-primary)',
                  margin: '0'
                }}>{companyData.companyName}</h3>
                <p style={{
                  fontFamily: 'Manrope, sans-serif',
                  fontSize: '14px',
                  color: 'var(--text-secondary)',
                  margin: 'var(--space-1) 0 0 0'
                }}>{companyData.industry}</p>
                {isEditingCompany && (
                  <p style={{
                    fontFamily: 'Manrope, sans-serif',
                    fontSize: '12px',
                    color: 'var(--text-muted)',
                    margin: 'var(--space-2) 0 0 0'
                  }}>Click on logo to change company logo</p>
                )}
              </div>
            </div>

            {/* Company Form */}
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(320px, 1fr))',
              gap: 'var(--space-6)'
            }}>
              {[
                { key: 'companyName', label: 'Company Name', type: 'text' },
                { key: 'companyUrl', label: 'Company URL', type: 'text' },
                { key: 'industry', label: 'Industry', type: 'select', options: [
                  { value: '', label: 'Select Industry' },
                  { value: 'Technology', label: 'Technology' },
                  { value: 'Healthcare', label: 'Healthcare' },
                  { value: 'Finance', label: 'Finance' },
                  { value: 'Education', label: 'Education' },
                  { value: 'Manufacturing', label: 'Manufacturing' },
                  { value: 'Retail', label: 'Retail' },
                  { value: 'Other', label: 'Other' }
                ]},
                { key: 'companySize', label: 'Company Size', type: 'select', options: [
                  { value: '', label: 'Select Size' },
                  { value: '1-10', label: '1-10 employees' },
                  { value: '11-50', label: '11-50 employees' },
                  { value: '51-200', label: '51-200 employees' },
                  { value: '201-500', label: '201-500 employees' },
                  { value: '501-1000', label: '501-1000 employees' },
                  { value: '1000+', label: '1000+ employees' }
                ]},
                { key: 'website', label: 'Website', type: 'url', placeholder: 'https://example.com' },
                { key: 'location', label: 'Location', type: 'text', placeholder: 'e.g., San Francisco, CA' }
              ].map((field) => (
                <div key={field.key} style={field.key === 'description' ? { gridColumn: '1 / -1' } : {}}>
                  <label style={{
                    fontFamily: 'Manrope, sans-serif',
                    fontSize: '14px',
                    fontWeight: '500',
                    color: 'var(--text-primary)',
                    marginBottom: 'var(--space-2)',
                    display: 'block'
                  }}>
                    {field.label}
                  </label>
                  {field.type === 'select' ? (
                    <select
                      value={companyData[field.key]}
                      onChange={(e) => setCompanyData(prev => ({ ...prev, [field.key]: e.target.value }))}
                      disabled={!isEditingCompany}
                      style={inputStyle(!isEditingCompany)}
                      onFocus={(e) => {
                        if (isEditingCompany) {
                          e.target.style.outline = 'none';
                          e.target.style.borderColor = 'var(--brand-primary)';
                          e.target.style.boxShadow = '0 0 0 3px var(--focus)';
                        }
                      }}
                      onBlur={(e) => {
                        e.target.style.borderColor = 'var(--bg-accent)';
                        e.target.style.boxShadow = 'none';
                      }}
                    >
                      {field.options.map(opt => (
                        <option key={opt.value} value={opt.value}>{opt.label}</option>
                      ))}
                    </select>
                  ) : (
                    <input
                      type={field.type}
                      value={companyData[field.key]}
                      onChange={(e) => setCompanyData(prev => ({ ...prev, [field.key]: e.target.value }))}
                      disabled={!isEditingCompany}
                      placeholder={field.placeholder}
                      style={inputStyle(!isEditingCompany)}
                      onFocus={(e) => {
                        if (isEditingCompany) {
                          e.target.style.outline = 'none';
                          e.target.style.borderColor = 'var(--brand-primary)';
                          e.target.style.boxShadow = '0 0 0 3px var(--focus)';
                        }
                      }}
                      onBlur={(e) => {
                        e.target.style.borderColor = 'var(--bg-accent)';
                        e.target.style.boxShadow = 'none';
                      }}
                    />
                  )}
                </div>
              ))}
              
              <div style={{ gridColumn: '1 / -1' }}>
                <label style={{
                  fontFamily: 'Manrope, sans-serif',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: 'var(--text-primary)',
                  marginBottom: 'var(--space-2)',
                  display: 'block'
                }}>
                  Description
                </label>
                <textarea
                  value={companyData.description}
                  onChange={(e) => setCompanyData(prev => ({ ...prev, description: e.target.value }))}
                  disabled={!isEditingCompany}
                  rows={4}
                  style={{
                    ...inputStyle(!isEditingCompany),
                    resize: 'vertical',
                    minHeight: '120px'
                  }}
                  onFocus={(e) => {
                    if (isEditingCompany) {
                      e.target.style.outline = 'none';
                      e.target.style.borderColor = 'var(--brand-primary)';
                      e.target.style.boxShadow = '0 0 0 3px var(--focus)';
                    }
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = 'var(--bg-accent)';
                    e.target.style.boxShadow = 'none';
                  }}
                />
              </div>
            </div>
          </div>
        )}

        {/* Personal Profile Tab */}
        {activeTab === "personal" && (
          <div style={cardStyle}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              marginBottom: 'var(--space-8)'
            }}>
              <div>
                <h2 style={{
                  fontFamily: 'Sora, sans-serif',
                  fontSize: '36px',
                  fontWeight: '600',
                  lineHeight: '1.3',
                  color: 'var(--text-primary)',
                  margin: '0 0 var(--space-1) 0'
                }}>Personal Profile</h2>
                <p style={{
                  fontFamily: 'Manrope, sans-serif',
                  fontSize: '16px',
                  color: 'var(--text-secondary)',
                  margin: '0'
                }}>Update your personal information</p>
              </div>
              <button
                onClick={() => isEditingPersonal ? handlePersonalUpdate() : setIsEditingPersonal(true)}
                disabled={loading.personal}
                style={buttonStyle('primary', loading.personal)}
                onMouseEnter={(e) => {
                  if (!loading.personal) {
                    e.target.style.background = '#2563EB';
                    e.target.style.transform = 'translateY(-1px)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!loading.personal) {
                    e.target.style.background = 'var(--brand-primary)';
                    e.target.style.transform = 'translateY(0)';
                  }
                }}
              >
                {loading.personal ? (
                  <>
                    <div style={{
                      width: '16px',
                      height: '16px',
                      border: '2px solid white',
                      borderTop: '2px solid transparent',
                      borderRadius: '50%',
                      animation: 'spin 1s linear infinite'
                    }}></div>
                    Saving...
                  </>
                ) : isEditingPersonal ? (
                  <>
                    <Check style={{ width: '16px', height: '16px' }} />
                    Save Changes
                  </>
                ) : (
                  "Edit Profile"
                )}
              </button>
            </div>

            {/* Profile Image */}
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: 'var(--space-6)',
              marginBottom: 'var(--space-8)',
              paddingBottom: 'var(--space-8)',
              borderBottom: '1px solid var(--bg-tertiary)'
            }}>
              <div style={{ position: 'relative' }}>
                <img
                  src={personalData.profileImage || "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"}
                  alt="Profile"
                  style={{
                    width: '96px',
                    height: '96px',
                    borderRadius: '50%',
                    objectFit: 'cover',
                    border: '4px solid var(--bg-primary)',
                    boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)'
                  }}
                />
                {isEditingPersonal && (
                  <label style={{
                    position: 'absolute',
                    inset: '0',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    background: 'rgba(0, 0, 0, 0.5)',
                    borderRadius: '50%',
                    cursor: 'pointer',
                    opacity: '0',
                    transition: 'opacity 0.2s ease'
                  }}
                  onMouseEnter={(e) => e.target.style.opacity = '1'}
                  onMouseLeave={(e) => e.target.style.opacity = '0'}
                  >
                    <Camera style={{ width: '24px', height: '24px', color: 'white' }} />
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleProfileImageUpload}
                      style={{ display: 'none' }}
                    />
                  </label>
                )}
              </div>
              <div>
                <h3 style={{
                  fontFamily: 'Sora, sans-serif',
                  fontSize: '20px',
                  fontWeight: '500',
                  lineHeight: '1.5',
                  color: 'var(--text-primary)',
                  margin: '0'
                }}>{personalData.name}</h3>
                <p style={{
                  fontFamily: 'Manrope, sans-serif',
                  fontSize: '14px',
                  color: 'var(--text-secondary)',
                  margin: 'var(--space-1) 0 0 0'
                }}>{personalData.position}</p>
                {isEditingPersonal && (
                  <p style={{
                    fontFamily: 'Manrope, sans-serif',
                    fontSize: '12px',
                    color: 'var(--text-muted)',
                    margin: 'var(--space-2) 0 0 0'
                  }}>Click on image to change profile photo</p>
                )}
              </div>
            </div>

            {/* Personal Form */}
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(320px, 1fr))',
              gap: 'var(--space-6)'
            }}>
              {[
                { key: 'name', label: 'Full Name', type: 'text' },
                { key: 'email', label: 'Email Address', type: 'email' },
                { key: 'phone', label: 'Phone Number', type: 'tel' },
                { key: 'position', label: 'Position', type: 'text' },
                { key: 'department', label: 'Department', type: 'text', span: 2 }
              ].map((field) => (
                <div key={field.key} style={field.span ? { gridColumn: '1 / -1' } : {}}>
                  <label style={{
                    fontFamily: 'Manrope, sans-serif',
                    fontSize: '14px',
                    fontWeight: '500',
                    color: 'var(--text-primary)',
                    marginBottom: 'var(--space-2)',
                    display: 'block'
                  }}>
                    {field.label}
                  </label>
                  <input
                    type={field.type}
                    value={personalData[field.key]}
                    onChange={(e) => setPersonalData(prev => ({ ...prev, [field.key]: e.target.value }))}
                    disabled={!isEditingPersonal}
                    style={inputStyle(!isEditingPersonal)}
                    onFocus={(e) => {
                      if (isEditingPersonal) {
                        e.target.style.outline = 'none';
                        e.target.style.borderColor = 'var(--brand-primary)';
                        e.target.style.boxShadow = '0 0 0 3px var(--focus)';
                      }
                    }}
                    onBlur={(e) => {
                      e.target.style.borderColor = 'var(--bg-accent)';
                      e.target.style.boxShadow = 'none';
                    }}
                  />
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Security Tab */}
        {activeTab === "security" && (
          <div style={cardStyle}>
            <div style={{ marginBottom: 'var(--space-8)' }}>
              <h2 style={{
                fontFamily: 'Sora, sans-serif',
                fontSize: '36px',
                fontWeight: '600',
                lineHeight: '1.3',
                color: 'var(--text-primary)',
                margin: '0 0 var(--space-1) 0'
              }}>Security Settings</h2>
              <p style={{
                fontFamily: 'Manrope, sans-serif',
                fontSize: '16px',
                color: 'var(--text-secondary)',
                margin: '0'
              }}>Update your password and security preferences</p>
            </div>

            <div style={{ maxWidth: '600px' }}>
              <h3 style={{
                fontFamily: 'Sora, sans-serif',
                fontSize: '20px',
                fontWeight: '500',
                color: 'var(--text-primary)',
                marginBottom: 'var(--space-6)'
              }}>Change Password</h3>
              
              <div style={{ display: 'flex', flexDirection: 'column', gap: 'var(--space-6)' }}>
                {[
                  { key: 'oldPassword', label: 'Current Password', placeholder: 'Enter your current password' },
                  { key: 'newPassword', label: 'New Password', placeholder: 'Enter a new password' },
                  { key: 'confirmPassword', label: 'Confirm New Password', placeholder: 'Confirm your new password' }
                ].map((field) => (
                  <div key={field.key}>
                    <label style={{
                      fontFamily: 'Manrope, sans-serif',
                      fontSize: '14px',
                      fontWeight: '500',
                      color: 'var(--text-primary)',
                      marginBottom: 'var(--space-2)',
                      display: 'block'
                    }}>
                      {field.label}
                    </label>
                    <div style={{ position: 'relative' }}>
                      <input
                        type={showPasswords[field.key.replace('Password', '')] ? "text" : "password"}
                        value={passwordData[field.key]}
                        onChange={(e) => setPasswordData(prev => ({ ...prev, [field.key]: e.target.value }))}
                        placeholder={field.placeholder}
                        style={{
                          ...inputStyle(),
                          paddingRight: 'var(--space-12)'
                        }}
                        onFocus={(e) => {
                          e.target.style.outline = 'none';
                          e.target.style.borderColor = 'var(--brand-primary)';
                          e.target.style.boxShadow = '0 0 0 3px var(--focus)';
                        }}
                        onBlur={(e) => {
                          e.target.style.borderColor = 'var(--bg-accent)';
                          e.target.style.boxShadow = 'none';
                        }}
                      />
                      <button
                        type="button"
                        onClick={() => setShowPasswords(prev => ({ 
                          ...prev, 
                          [field.key.replace('Password', '')]: !prev[field.key.replace('Password', '')] 
                        }))}
                        style={{
                          position: 'absolute',
                          right: 'var(--space-3)',
                          top: '50%',
                          transform: 'translateY(-50%)',
                          background: 'none',
                          border: 'none',
                          color: 'var(--text-muted)',
                          cursor: 'pointer',
                          padding: 'var(--space-1)'
                        }}
                      >
                        {showPasswords[field.key.replace('Password', '')] ? 
                          <EyeOffIcon style={{ width: '20px', height: '20px' }} /> : 
                          <EyeIcon style={{ width: '20px', height: '20px' }} />
                        }
                      </button>
                    </div>
                  </div>
                ))}

                <button
                  onClick={handlePasswordChange}
                  disabled={loading.password}
                  style={buttonStyle('primary', loading.password)}
                  onMouseEnter={(e) => {
                    if (!loading.password) {
                      e.target.style.background = '#2563EB';
                      e.target.style.transform = 'translateY(-1px)';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!loading.password) {
                      e.target.style.background = 'var(--brand-primary)';
                      e.target.style.transform = 'translateY(0)';
                    }
                  }}
                >
                  {loading.password ? (
                    <>
                      <div style={{
                        width: '16px',
                        height: '16px',
                        border: '2px solid white',
                        borderTop: '2px solid transparent',
                        borderRadius: '50%',
                        animation: 'spin 1s linear infinite'
                      }}></div>
                      Updating...
                    </>
                  ) : (
                    "Update Password"
                  )}
                </button>
              </div>

              {/* Security Tips */}
              <div style={{
                marginTop: 'var(--space-8)',
                padding: 'var(--space-6)',
                background: 'rgba(59, 130, 246, 0.05)',
                border: '1px solid rgba(59, 130, 246, 0.2)',
                borderRadius: '12px'
              }}>
                <h4 style={{
                  fontFamily: 'Manrope, sans-serif',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: 'var(--brand-primary)',
                  marginBottom: 'var(--space-3)',
                  margin: '0 0 var(--space-3) 0'
                }}>Password Security Tips</h4>
                <ul style={{
                  fontFamily: 'Manrope, sans-serif',
                  fontSize: '14px',
                  color: 'var(--text-secondary)',
                  listStyle: 'none',
                  padding: '0',
                  margin: '0',
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 'var(--space-2)'
                }}>
                  {[
                    'Use at least 8 characters with a mix of letters, numbers, and symbols',
                    'Avoid using personal information like names or birthdays',
                    'Don\'t reuse passwords from other accounts',
                    'Consider using a password manager for better security'
                  ].map((tip, index) => (
                    <li key={index} style={{ display: 'flex', alignItems: 'flex-start', gap: 'var(--space-2)' }}>
                      <Check style={{ width: '16px', height: '16px', marginTop: '2px', flexShrink: 0, color: 'var(--success)' }} />
                      {tip}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        )}

        {/* Notifications Tab */}
        {activeTab === "notifications" && (
          <div style={cardStyle}>
            <div style={{ marginBottom: 'var(--space-8)' }}>
              <h2 style={{
                fontFamily: 'Sora, sans-serif',
                fontSize: '36px',
                fontWeight: '600',
                lineHeight: '1.3',
                color: 'var(--text-primary)',
                margin: '0 0 var(--space-1) 0'
              }}>Notification Preferences</h2>
              <p style={{
                fontFamily: 'Manrope, sans-serif',
                fontSize: '16px',
                color: 'var(--text-secondary)',
                margin: '0'
              }}>Manage how you receive notifications and updates</p>
            </div>

            <div style={{ display: 'flex', flexDirection: 'column', gap: 'var(--space-6)' }}>
              {[
                { key: "emailNotifications", label: "Email Notifications", description: "Receive notifications via email" },
                { key: "candidateApplications", label: "Candidate Applications", description: "Get notified about new candidate applications" },
                { key: "systemUpdates", label: "System Updates", description: "Receive notifications about system updates and maintenance" },
                { key: "weeklyReports", label: "Weekly Reports", description: "Receive weekly analytics and performance reports" },
                { key: "marketingEmails", label: "Marketing Emails", description: "Receive promotional emails and newsletters" }
              ].map((setting) => (
                <div key={setting.key} style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  padding: 'var(--space-4)',
                  border: '1px solid var(--bg-tertiary)',
                  borderRadius: '12px'
                }}>
                  <div>
                    <h3 style={{
                      fontFamily: 'Manrope, sans-serif',
                      fontSize: '16px',
                      fontWeight: '500',
                      color: 'var(--text-primary)',
                      margin: '0 0 var(--space-1) 0'
                    }}>{setting.label}</h3>
                    <p style={{
                      fontFamily: 'Manrope, sans-serif',
                      fontSize: '14px',
                      color: 'var(--text-secondary)',
                      margin: '0'
                    }}>{setting.description}</p>
                  </div>
                  <label style={{
                    position: 'relative',
                    display: 'inline-flex',
                    alignItems: 'center',
                    cursor: 'pointer'
                  }}>
                    <input
                      type="checkbox"
                      checked={notificationSettings[setting.key]}
                      onChange={(e) => setNotificationSettings(prev => ({ ...prev, [setting.key]: e.target.checked }))}
                      style={{
                        position: 'absolute',
                        opacity: 0,
                        width: 0,
                        height: 0
                      }}
                    />
                    <div style={{
                      width: '44px',
                      height: '24px',
                      background: notificationSettings[setting.key] ? 'var(--brand-primary)' : 'var(--bg-accent)',
                      borderRadius: '12px',
                      position: 'relative',
                      transition: 'background-color 0.2s ease'
                    }}>
                      <div style={{
                        position: 'absolute',
                        top: '2px',
                        left: notificationSettings[setting.key] ? '22px' : '2px',
                        width: '20px',
                        height: '20px',
                        background: 'white',
                        borderRadius: '50%',
                        transition: 'left 0.2s ease',
                        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.2)'
                      }} />
                    </div>
                  </label>
                </div>
              ))}
            </div>

            <div style={{ marginTop: 'var(--space-8)' }}>
              <button
                onClick={() => {
                  setLoading(prev => ({ ...prev, notifications: true }));
                  setTimeout(() => {
                    setLoading(prev => ({ ...prev, notifications: false }));
                    toast.success("Notification preferences updated!");
                  }, 1000);
                }}
                disabled={loading.notifications}
                style={buttonStyle('primary', loading.notifications)}
                onMouseEnter={(e) => {
                  if (!loading.notifications) {
                    e.target.style.background = '#2563EB';
                    e.target.style.transform = 'translateY(-1px)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!loading.notifications) {
                    e.target.style.background = 'var(--brand-primary)';
                    e.target.style.transform = 'translateY(0)';
                  }
                }}
              >
                {loading.notifications ? (
                  <>
                    <div style={{
                      width: '16px',
                      height: '16px',
                      border: '2px solid white',
                      borderTop: '2px solid transparent',
                      borderRadius: '50%',
                      animation: 'spin 1s linear infinite'
                    }}></div>
                    Saving...
                  </>
                ) : (
                  "Save Preferences"
                )}
              </button>
            </div>
          </div>
        )}

        {/* Privacy Tab */}
        {activeTab === "privacy" && (
          <div style={cardStyle}>
            <div style={{ marginBottom: 'var(--space-8)' }}>
              <h2 style={{
                fontFamily: 'Sora, sans-serif',
                fontSize: '36px',
                fontWeight: '600',
                lineHeight: '1.3',
                color: 'var(--text-primary)',
                margin: '0 0 var(--space-1) 0'
              }}>Privacy Settings</h2>
              <p style={{
                fontFamily: 'Manrope, sans-serif',
                fontSize: '16px',
                color: 'var(--text-secondary)',
                margin: '0'
              }}>Control who can see your information and how it's used</p>
            </div>

            <div style={{ display: 'flex', flexDirection: 'column', gap: 'var(--space-8)' }}>
              <div>
                <h3 style={{
                  fontFamily: 'Sora, sans-serif',
                  fontSize: '20px',
                  fontWeight: '500',
                  color: 'var(--text-primary)',
                  marginBottom: 'var(--space-4)'
                }}>Company Visibility</h3>
                <div style={{ display: 'flex', flexDirection: 'column', gap: 'var(--space-3)' }}>
                  {[
                    { value: "public", label: "Public", description: "Anyone can view your company profile" },
                    { value: "verified", label: "Verified Users Only", description: "Only verified candidates can view your profile" },
                    { value: "private", label: "Private", description: "Only you can view your company profile" }
                  ].map((option) => (
                    <label key={option.value} style={{
                      display: 'flex',
                      alignItems: 'center',
                      padding: 'var(--space-4)',
                      border: '1px solid var(--bg-tertiary)',
                      borderRadius: '12px',
                      cursor: 'pointer',
                      transition: 'background-color 0.2s ease'
                    }}
                    onMouseEnter={(e) => e.target.style.background = 'var(--hover)'}
                    onMouseLeave={(e) => e.target.style.background = 'transparent'}
                    >
                      <input
                        type="radio"
                        name="companyVisibility"
                        value={option.value}
                        checked={privacySettings.companyVisibility === option.value}
                        onChange={(e) => setPrivacySettings(prev => ({ ...prev, companyVisibility: e.target.value }))}
                        style={{
                          width: '16px',
                          height: '16px',
                          accentColor: 'var(--brand-primary)',
                          marginRight: 'var(--space-3)'
                        }}
                      />
                      <div>
                        <div style={{
                          fontFamily: 'Manrope, sans-serif',
                          fontSize: '16px',
                          fontWeight: '500',
                          color: 'var(--text-primary)',
                          margin: '0 0 var(--space-1) 0'
                        }}>{option.label}</div>
                        <div style={{
                          fontFamily: 'Manrope, sans-serif',
                          fontSize: '14px',
                          color: 'var(--text-secondary)',
                          margin: '0'
                        }}>{option.description}</div>
                      </div>
                    </label>
                  ))}
                </div>
              </div>

              <div style={{ display: 'flex', flexDirection: 'column', gap: 'var(--space-4)' }}>
                {[
                  { key: "showContactInfo", label: "Show Contact Information", description: "Allow candidates to see your contact details" },
                  { key: "allowCandidateMessages", label: "Allow Candidate Messages", description: "Let candidates send you messages directly" },
                  { key: "shareCompanyData", label: "Share Company Analytics", description: "Allow anonymized company data to be used for platform insights" }
                ].map((setting) => (
                  <div key={setting.key} style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    padding: 'var(--space-4)',
                    border: '1px solid var(--bg-tertiary)',
                    borderRadius: '12px'
                  }}>
                    <div>
                      <h3 style={{
                        fontFamily: 'Manrope, sans-serif',
                        fontSize: '16px',
                        fontWeight: '500',
                        color: 'var(--text-primary)',
                        margin: '0 0 var(--space-1) 0'
                      }}>{setting.label}</h3>
                      <p style={{
                        fontFamily: 'Manrope, sans-serif',
                        fontSize: '14px',
                        color: 'var(--text-secondary)',
                        margin: '0'
                      }}>{setting.description}</p>
                    </div>
                    <label style={{
                      position: 'relative',
                      display: 'inline-flex',
                      alignItems: 'center',
                      cursor: 'pointer'
                    }}>
                      <input
                        type="checkbox"
                        checked={privacySettings[setting.key]}
                        onChange={(e) => setPrivacySettings(prev => ({ ...prev, [setting.key]: e.target.checked }))}
                        style={{
                          position: 'absolute',
                          opacity: 0,
                          width: 0,
                          height: 0
                        }}
                      />
                      <div style={{
                        width: '44px',
                        height: '24px',
                        background: privacySettings[setting.key] ? 'var(--brand-primary)' : 'var(--bg-accent)',
                        borderRadius: '12px',
                        position: 'relative',
                        transition: 'background-color 0.2s ease'
                      }}>
                        <div style={{
                          position: 'absolute',
                          top: '2px',
                          left: privacySettings[setting.key] ? '22px' : '2px',
                          width: '20px',
                          height: '20px',
                          background: 'white',
                          borderRadius: '50%',
                          transition: 'left 0.2s ease',
                          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.2)'
                        }} />
                      </div>
                    </label>
                  </div>
                ))}
              </div>

              <div>
                <button
                  onClick={() => {
                    setLoading(prev => ({ ...prev, privacy: true }));
                    setTimeout(() => {
                      setLoading(prev => ({ ...prev, privacy: false }));
                      toast.success("Privacy settings updated!");
                    }, 1000);
                  }}
                  disabled={loading.privacy}
                  style={buttonStyle('primary', loading.privacy)}
                  onMouseEnter={(e) => {
                    if (!loading.privacy) {
                      e.target.style.background = '#2563EB';
                      e.target.style.transform = 'translateY(-1px)';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!loading.privacy) {
                      e.target.style.background = 'var(--brand-primary)';
                      e.target.style.transform = 'translateY(0)';
                    }
                  }}
                >
                  {loading.privacy ? (
                    <>
                      <div style={{
                        width: '16px',
                        height: '16px',
                        border: '2px solid white',
                        borderTop: '2px solid transparent',
                        borderRadius: '50%',
                        animation: 'spin 1s linear infinite'
                      }}></div>
                      Saving...
                    </>
                  ) : (
                    "Save Settings"
                  )}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Add CSS keyframes for animations */}
      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default EmployerSettings;