// visume-api/controllers/inviteController.js

const prisma = require('../config/prisma');
/**
 * GET /api/invite/:inviteId
 * Returns invitation context for a given jobId from job_descriptions table
 */
exports.getInviteContext = async (req, res) => {
  const { inviteId } = req.params;
  if (!inviteId) {
    return res.status(400).json({ message: "inviteId is required" });
  }
  try {
    // Fetch from job_descriptions table using id (primary key) with employer details
    const job = await prisma.job_descriptions.findUnique({
      where: { id: Number(inviteId) },
      include: {
        employer: true
      }
    });
    if (!job) {
      return res.status(404).json({ message: "Job description not found" });
    }
    // Parse JobDescription field if it's a stringified JSON
    let jd = job.JobDescription;
    if (typeof jd === "string") {
      try {
        jd = JSON.parse(jd);
      } catch (e) {
        return res.status(500).json({ message: "Invalid JobDescription JSON", error: e.message });
      }
    }
    const {
      role = "",
      skills = [],
      experience = "",
      location = "",
      companyType = "",
    } = jd || {};

    // Strict normalization to schema enum values
    let normalizedCompanyType = "";
    if (typeof companyType === "string") {
      const ct = companyType.trim().toLowerCase();
      if (ct === "mnc") normalizedCompanyType = "mnc";
      else if (ct === "startup") normalizedCompanyType = "startup";
      else if (ct === "mid_range") normalizedCompanyType = "mid_range";
      else normalizedCompanyType = "";
    }

    return res.json({
      jobRole: role,
      skills,
      experience,
      location,
      companyType: normalizedCompanyType,
      empId: job.employer_id,
      jobId: job.id,
      organization: job.employer?.organization || "Company",
      employerName: job.employer?.emp_name || ""
    });
  } catch (err) {
    return res.status(500).json({ message: "Server error", error: err.message });
  }
};