import React from "react";
import { HiOfficeBuilding, HiUserGroup, Hi<PERSON><PERSON>Bulb, HiOutlineSparkles } from "react-icons/hi";
import { CheckCircle } from "lucide-react";

const CompanyType = ({
  inviteDataToUse,
  formData,
  handleInputChange,
  salaryOptions,
}) => (
  <div className="space-y-6">
    <div className="text-center">
      <div className="mx-auto mb-4 flex h-14 w-14 items-center justify-center rounded-lg bg-blue-50 border border-blue-100">
        <HiOfficeBuilding className="h-7 w-7 text-blue-600" />
      </div>
      <h1 className="mb-2 text-2xl font-semibold text-gray-900">
        Company Preference
      </h1>
      <p className="text-gray-600">
        {inviteDataToUse
          ? "Company type from employer invitation"
          : "What type of company environment do you prefer?"}
      </p>
      {inviteDataToUse && (
        <div className="mt-4 rounded-lg border border-blue-200 bg-blue-50 p-4">
          <div className="flex items-center justify-center gap-3">
            <HiOutlineSparkles className="h-5 w-5 text-blue-600" />
            <span className="font-medium text-blue-800">
              Pre-selected: {inviteDataToUse.companyType}
            </span>
          </div>
        </div>
      )}
    </div>

    <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
      {/* MNC Option */}
      <label
        className={`relative cursor-pointer rounded-lg border-2 p-4 transition-all duration-200 ${
          formData.companyType.includes("mnc")
            ? "border-blue-500 bg-blue-50 shadow-sm"
            : "border-gray-200 bg-white hover:border-blue-300 hover:shadow-sm"
        }`}
      >
        <input
          type="checkbox"
          name="companyType"
          value="mnc"
          checked={formData.companyType.includes("mnc")}
          onChange={handleInputChange}
          className="sr-only"
        />
        <div className="text-center">
          <div
            className={`mx-auto mb-3 flex h-10 w-10 items-center justify-center rounded-lg ${
              formData.companyType.includes("mnc")
                ? "bg-blue-100"
                : "bg-gray-100"
            }`}
          >
            <HiOfficeBuilding
              className={`h-6 w-6 ${
                formData.companyType.includes("mnc")
                  ? "text-blue-600"
                  : "text-gray-500"
              }`}
            />
          </div>
          <h3 className="mb-2 font-semibold text-gray-900">
            MNC
          </h3>
          <p className="text-sm text-gray-600">
            Multinational Corporation
          </p>
        </div>
        {formData.companyType.includes("mnc") && (
          <div className="absolute right-3 top-3">
            <CheckCircle className="h-5 w-5 text-blue-600" />
          </div>
        )}
      </label>

      {/* Mid Range Option */}
      <label
        className={`relative cursor-pointer rounded-lg border-2 p-4 transition-all duration-200 ${
          formData.companyType.includes("mid_range")
            ? "border-blue-500 bg-blue-50 shadow-sm"
            : "border-gray-200 bg-white hover:border-blue-300 hover:shadow-sm"
        }`}
      >
        <input
          type="checkbox"
          name="companyType"
          value="mid_range"
          checked={formData.companyType.includes("mid_range")}
          onChange={handleInputChange}
          className="sr-only"
        />
        <div className="text-center">
          <div
            className={`mx-auto mb-3 flex h-10 w-10 items-center justify-center rounded-lg ${
              formData.companyType.includes("mid_range")
                ? "bg-blue-100"
                : "bg-gray-100"
            }`}
          >
            <HiUserGroup
              className={`h-6 w-6 ${
                formData.companyType.includes("mid_range")
                  ? "text-blue-600"
                  : "text-gray-500"
              }`}
            />
          </div>
          <h3 className="mb-2 font-semibold text-gray-900">
            Mid-Size
          </h3>
          <p className="text-sm text-gray-600">
            Growing Company
          </p>
        </div>
        {formData.companyType.includes("mid_range") && (
          <div className="absolute right-3 top-3">
            <CheckCircle className="h-5 w-5 text-blue-600" />
          </div>
        )}
      </label>

      {/* Startup Option */}
      <label
        className={`relative cursor-pointer rounded-lg border-2 p-4 transition-all duration-200 ${
          formData.companyType.includes("startup")
            ? "border-blue-500 bg-blue-50 shadow-sm"
            : "border-gray-200 bg-white hover:border-blue-300 hover:shadow-sm"
        }`}
      >
        <input
          type="checkbox"
          name="companyType"
          value="startup"
          checked={formData.companyType.includes("startup")}
          onChange={handleInputChange}
          className="sr-only"
        />
        <div className="text-center">
          <div
            className={`mx-auto mb-3 flex h-10 w-10 items-center justify-center rounded-lg ${
              formData.companyType.includes("startup")
                ? "bg-blue-100"
                : "bg-gray-100"
            }`}
          >
            <HiLightBulb
              className={`h-6 w-6 ${
                formData.companyType.includes("startup")
                  ? "text-blue-600"
                  : "text-gray-500"
              }`}
            />
          </div>
          <h3 className="mb-2 font-semibold text-gray-900">
            Startup
          </h3>
          <p className="text-sm text-gray-600">
            Early-stage Company
          </p>
        </div>
        {formData.companyType.includes("startup") && (
          <div className="absolute right-3 top-3">
            <CheckCircle className="h-5 w-5 text-blue-600" />
          </div>
        )}
      </label>
    </div>
  </div>
);

export default CompanyType;