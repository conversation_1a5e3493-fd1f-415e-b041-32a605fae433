const axios = require("axios");
const pdf = require("pdf-parse");
const { GoogleGenerativeAI } = require("@google/generative-ai");
const path = require("path");
const fs = require("fs");
const crypto = require("crypto");
const dotenv = require("dotenv");


dotenv.config();

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY);
const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" });


// Generates an 8-character alphanumeric ID
exports.generateRandomId = () => {
  return crypto.randomBytes(4).toString("hex").toUpperCase();
};

// Generates a 10-digit numeric ID
exports.generateVideoRandomId = () => {
  return Array.from({ length: 10 }, (_, i) =>
    i === 0
      ? crypto.randomInt(1, 10) // Ensure the first digit is not zero
      : crypto.randomInt(0, 10)
  ).join("");
};

exports.generateSingleQuestion = async (
  role,
  previousQA,
  skills = [],
  isFirstQuestion = false,
  companyType = '',
  experience = '',
  forcedType = null, // Deprecated: let AI decide type
  currentQuestionNumber = null, // Add question number for termination logic
  projects = [], // Array of candidate projects, each with title/description/technologies
  completeResumeData = null, // Complete resume data for context filtering
  dynamicPrompt = null // Natural language instructions for AI interviewer
) => {
  try {
    if (!role) {
      throw new Error("Role is required for question generation");
    }

    const skillsList = (Array.isArray(skills) ? skills : [skills])
      .filter(skill => skill && typeof skill === 'string')
      .map(skill => skill.trim())
      .filter(skill => skill.length > 0);

    const previousQuestionsSet = new Set(
      previousQA.map(qa => (qa.question || '').trim().toLowerCase())
    );

    const history = previousQA.map(qa => [
      {
        role: "user",
        parts: [{ text: qa.question }]
      },
      {
        role: "model",
        parts: [{ text: qa.answer === null || qa.answer === undefined ? "No response" : qa.answer }]
      }
    ]).flat();
    
    let lastError = null;
    let generated = null;
    let attempts = 0;
    const MAX_ATTEMPTS = 3;

    // Use dynamic prompt instructions only
    let contextSection = "";
    if (dynamicPrompt) {
      contextSection = `
  INTERVIEW GUIDANCE:
  ${dynamicPrompt}
  
  Use these instructions to focus your questions, determine question types (coding vs verbal), and decide when the interview is complete.`;
    }

    while (attempts < MAX_ATTEMPTS) {
      attempts++;

      const chat = model.startChat({
        history,
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 1024,
        },
      });

      const prompt = `
INTERVIEW SYSTEM - DYNAMIC QUESTION GENERATION

STEP 1: INTERVIEW TERMINATION CHECK
${previousQA.length >= 2 ? `
Check the last two answers for termination:
Last Answer: "${previousQA[previousQA.length - 1]?.answer || 'No response'}"
Second-to-Last Answer: "${previousQA[previousQA.length - 2]?.answer || 'No response'}"

IF BOTH answers are poor (empty, <5 words, "I don't know", off-topic): Return {"type": "ai_interview_stop"}
` : ''}

${previousQA.length >= 5 ? `
IF 5+ questions asked AND candidate giving reasonable answers: Return {"type": "ai_interview_stop"}
` : ''}

${previousQA.length >= 8 ? `
IF 8+ questions asked: Return {"type": "ai_interview_stop"} (maximum reached)
` : ''}

STEP 2: QUESTION GENERATION
Context Available:
- Target Role: ${role}
- Skills Focus: ${skillsList.length > 0 ? skillsList.join(', ') : 'General role knowledge'}
- Experience Level: ${experience || 'Not specified'} years
${contextSection}

Previous Q&A:
${previousQA.map((qa, idx) => {
  const answer = (qa.answer === null || qa.answer === undefined) ? 'No response' : qa.answer;
  return `Q${idx + 1}: ${qa.question}\nA${idx + 1}: ${answer}`;
}).join('\n\n')}

QUESTION GENERATION RULES:
1. **Follow the interview guidance above** - it contains specific instructions for this candidate
2. **Question Types**: Choose "coding" for technical implementation questions, "verbal" for all other questions
3. **If no interview guidance provided**: Use your judgment based on the role to determine appropriate question types
4. **Keep interviews efficient**: 5-8 questions total, progress naturally  
5. **Time limits**: 30-60 seconds max per question

RESPONSE FORMAT (JSON only):

For termination:
{"type": "ai_interview_stop"}

For new question:
{
  "question": "Concise, role-relevant question here",
  "type": "coding", // or "verbal" - choose based on interview guidance or role analysis
  "timerDuration": 45 // 30-60 seconds based on complexity
}

Generate the ${isFirstQuestion ? 'first' : 'next'} question now:`;
      
      try {
        const result = await chat.sendMessage(prompt);
        const text = result.response.text().trim();
        
        console.log("=== AI RAW RESPONSE ===");
        console.log(text);

        // Clean and extract JSON
        let jsonText = text
          .replace(/```(?:json)?\s*|\s*```/g, '')
          .replace(/^[^{]*/, '') // Remove any text before first {
          .replace(/[^}]*$/, '') // Remove any text after last }
          .trim();

        // Find the JSON object
        const jsonMatch = jsonText.match(/\{[\s\S]*?\}/);
        if (!jsonMatch) {
          throw new Error('No JSON object found in response');
        }

        jsonText = jsonMatch[0]
          .replace(/\n/g, ' ')
          .replace(/,\s*}/g, '}')
          .replace(/,\s*]/g, ']');

        let parsed = JSON.parse(jsonText);
        
        console.log("=== PARSED JSON ===");
        console.log(parsed);

        // Handle termination
        if (parsed.type === 'ai_interview_stop') {
          console.log('AI requested interview termination');
          return parsed;
        }

        // Validate question response
        if (!parsed.question || !parsed.type) {
          throw new Error('Invalid question format - missing required fields');
        }

        const question = {
          question: String(parsed.question).trim(),
          type: String(parsed.type).toLowerCase(),
          timerDuration: Math.min(90, Math.max(30, parseInt(parsed.timerDuration) || 60))
        };

        // Validate question content
        if (!question.question) {
          throw new Error('Generated question text is empty');
        }

        // Validate type
        if (!['coding', 'verbal'].includes(question.type)) {
          throw new Error('Invalid question type: ' + question.type);
        }

        console.log("Generated Question Output:", question);
        return question;
        
      } catch (error) {
        lastError = error.message;
        console.error("Question generation error:", error);
      }
    }

    throw new Error(lastError || "Failed to generate question after max attempts");
    
  } catch (error) {
    console.error("Critical error in generation:", error);
    throw new Error('Critical error: ' + error.message);
  }
};

exports.generateScores = async (questions) => {
  if (!questions || !Array.isArray(questions) || questions.length === 0) {
    throw new Error("Invalid interview data provided for scoring.");
  }

  if (!process.env.GOOGLE_API_KEY) {
    throw new Error("Google API key not found. Cannot generate scores.");
  }

  // Filter out answers that look like API payloads or objects
  const cleanedInterviewData = questions
    .map(q => ({
      question: String(q.question || '').trim(),
      answer: typeof q.answer === 'string' ? q.answer.trim() : (q.answer === null || q.answer === undefined ? "Not provided" : String(q.answer)),
      type: typeof q.type === 'string' && ['coding', 'verbal'].includes(q.type.trim().toLowerCase()) ? q.type.trim().toLowerCase() : "verbal"
    }))
    .filter(q =>
      !(q.answer.startsWith('Frontend received data from API:') ||
        q.answer.includes('{success:') ||
        q.answer.includes('question:') && q.answer.includes('type:') && q.answer.includes('timerDuration:'))
    );

  const prompt = `You are an expert AI interview evaluator. Evaluate the provided interview responses in detail, providing overall scores and question-wise analysis.

    IMPORTANT: Return ONLY valid JSON in EXACTLY the structure below. Do NOT include any extra text, explanation, markdown, or code block formatting. If you include anything other than pure JSON, your response will be rejected.

    {
      "score": {
        "Skill_Score": (number, 0-10),
        "Communication_Score": (number, 0-10),
        "Overall_Score": (number, 0-10)
      },
      "evaluation": [
        {
          "Question": "Example interview question",
          "Your_Answer": "Candidate's actual response",
          "Expected_Answer": "Key points that should have been covered",
          "Analysis": {
            "Technical_Understanding": "Analysis of technical knowledge",
            "Communication_Quality": "Analysis of how well the answer was explained",
            "Improvement_Areas": ["Specific areas to improve"],
            "Strengths": ["Strong points in the answer"]
          }
        }
      ],
      "Suggestions": "Detailed constructive feedback",
      "Next_Focus_Areas": ["Areas that need more exploration"]
    }

    If any answer is "don't know", "not sure", "no idea", "sorry", "what is that", empty, or off-topic, treat it as "Not provided" and still return the full JSON structure above with constructive feedback and suggestions for improvement.

    Responses to evaluate:
    ${cleanedInterviewData.map((q, idx) =>
      `Question ${idx + 1}: ${q.question}
    Type: ${q.type}
    Expected Keywords: ${(q.keywords && Array.isArray(q.keywords) ? q.keywords.join(", ") : 'none')}
    Answer: ${typeof q.answer === 'string' ? q.answer.trim() :
            q.answer === null || q.answer === undefined || q.answer === "null" ? "Not provided" :
            String(q.answer)}`
    ).join("\n\n")}

    Rules:
    - Return only pure JSON in the structure above.
    - Do NOT include any markdown, code block, or explanation.
    - Scores (Skill_Score, Communication_Score, Overall_Score) must be numbers between 0-10.
    - For each question, provide "Expected_Answer", "Technical_Understanding", "Communication_Quality", "Improvement_Areas", and "Strengths".
    - If an answer is null, undefined, "null", "don't know", "not sure", "no idea", "sorry", "what is that", empty, or off-topic, display "Not provided".
    - Provide specific, actionable feedback in "Suggestions".
    - "Next_Focus_Areas" should suggest topics for further interview rounds.
    - Do NOT return fallback scores; if AI processing fails, an error should be thrown.

    FINAL REMINDER: Return ONLY pure JSON. Any extra text, formatting, or explanation will cause your response to be rejected.`;
 
  try {
    const result = await model.generateContent(prompt);
    const text = result.response.text();

    // Log raw AI response for debugging
    console.log("=== RAW AI SCORE RESPONSE ===");
    console.log(text);

    // Robust cleaning: strip everything before first '{' and after last '}'
    let jsonText = text.replace(/^[^{]*/, '').replace(/[^}]*$/, '').trim();
    let cleaned = cleanJSONResponse(jsonText);
    const data = typeof cleaned === 'string' ? JSON.parse(cleaned) : cleaned;

    if (!data || typeof data !== 'object' || !data.score || !data.evaluation) {
      console.error("=== AI RESPONSE PARSE FAILURE ===");
      console.error("Raw response:", text);
      throw new Error('Invalid response format from AI. Missing score or evaluation.');
    }

    const scores = {
      score: {
        Skill_Score: Math.min(10, Math.max(0, Number(data.score?.Skill_Score) || 0)),
        Communication_Score: Math.min(10, Math.max(0, Number(data.score?.Communication_Score) || 0)),
        Overall_Score: Math.min(10, Math.max(0, Number(data.score?.Overall_Score) || 0))
      },
      evaluation: Array.isArray(data.evaluation) ? data.evaluation.map((e, idx) => {
        const originalAnswer = cleanedInterviewData[idx]?.answer;
        return {
          Question: String(e.Question || ''),
          Your_Answer: (() => {
            return originalAnswer === null ||
                   originalAnswer === undefined ||
                   originalAnswer === "null" ||
                   (typeof originalAnswer === 'string' && originalAnswer.trim() === "")
              ? null
              : originalAnswer;
          })(),
          Expected_Answer: String(e.Expected_Answer || ''),
          Analysis: {
            Technical_Understanding: String(e.Analysis?.Technical_Understanding || ''),
            Communication_Quality: String(e.Analysis?.Communication_Quality || ''),
            Improvement_Areas: Array.isArray(e.Analysis?.Improvement_Areas)
              ? e.Analysis.Improvement_Areas
              : [],
            Strengths: Array.isArray(e.Analysis?.Strengths)
              ? e.Analysis.Strengths
              : []
          }
        };
      }) : [],
      Suggestions: String(data.Suggestions || "No specific feedback available."),
      Next_Focus_Areas: Array.isArray(data.Next_Focus_Areas) ? data.Next_Focus_Areas : []
    };

    // Normalize scores to valid range (0-10)
    scores.score = Object.entries(scores.score).reduce((acc, [key, value]) => {
      acc[key] = Math.min(10, Math.max(0, value));
      return acc;
    }, {});

    return scores;

  } catch (error) {
    console.error("Error in score generation:", error);
    console.error("Score generation error details:", {
      error: error.message,
      lastAnswer: cleanedInterviewData[cleanedInterviewData.length - 1]?.answer,
      answerTypes: cleanedInterviewData.map(d => typeof d.answer)
    });
    // Return a default score/evaluation object instead of throwing
    return {
      score: {
        Skill_Score: 0,
        Communication_Score: 0,
        Overall_Score: 0
      },
      evaluation: cleanedInterviewData.map(q => ({
        Question: q.question,
        Your_Answer: q.answer,
        Expected_Answer: "No valid answer provided.",
        Analysis: {
          Technical_Understanding: "No technical content to evaluate.",
          Communication_Quality: "No communication content to evaluate.",
          Improvement_Areas: ["Provide relevant, detailed answers to interview questions."],
          Strengths: []
        }
      })),
      Suggestions: "Answers were off-topic or insufficient. Please provide relevant responses for proper evaluation.",
      Next_Focus_Areas: []
    };
  }
};

function isValidScoreObject(obj) {
  return (
    obj &&
    typeof obj === 'object' &&
    obj.score &&
    typeof obj.score === 'object' &&
    typeof obj.score.Skill_Score === 'number' &&
    typeof obj.score.Communication_Score === 'number' &&
    typeof obj.score.Overall_Score === 'number' &&
    typeof obj.Suggestions === 'string'
  );
}

function defaultScoreObject(reason) {
  return {
    score: {
      Skill_Score: 5,
      Communication_Score: 5,
      Overall_Score: 5
    },
    Suggestions: `Note: Using default scoring due to processing limitations. Reason: ${reason}`,
    _default: true
  };
}


exports.resumeStripper = async (pdfText) => {
  try {
    console.log('=== Starting enhanced resume parsing ===');
    console.log(`Input PDF text length: ${pdfText?.length || 0} characters`);
    
    if (!pdfText || typeof pdfText !== 'string' || pdfText.trim().length === 0) {
      throw new Error('Invalid or empty PDF text provided');
    }

    // Ensure we have API key
    if (!process.env.GOOGLE_API_KEY) {
      throw new Error('Google API key not found in environment variables');
    }

    const prompt = `You are an expert resume parser. Extract comprehensive information from this resume and return ONLY valid JSON in the exact format below.

CRITICAL: Return ONLY the JSON object, no other text, explanations, or markdown formatting.

For skills extraction, be thorough and identify ALL skills mentioned in the resume:
1. Look for technical skills, programming languages, frameworks, tools
2. Look for soft skills mentioned in descriptions
3. Look for domain-specific skills and certifications
4. Prioritize skills based on frequency and recent usage

{
  "personal_info": {
    "name": "Full Name Here",
    "email": "<EMAIL>", 
    "phone": "phone number",
    "location": "city, state/country"
  },
  "education": [
    {
      "degree": "degree name",
      "institution": "school name",
      "year": "graduation year"
    }
  ],
  "experience": [
    {
      "title": "job title",
      "company": "company name", 
      "duration": "start - end dates",
      "responsibilities": ["responsibility 1", "responsibility 2"]
    }
  ],
  "projects": [
    {
      "title": "project name",
      "description": "brief description",
      "technologies": ["tech1", "tech2"],
      "responsibilities": ["what you did"]
    }
  ],
  "skills": {
    "primary_skills": ["top 5 most important skills"],
    "secondary_skills": ["other relevant skills"],
    "all_skills": ["all skills found in resume"],
    "skill_categories": {
      "technical": ["programming, frameworks, tools"],
      "soft": ["communication, leadership, etc"],
      "domain": ["industry specific skills"]
    }
  },
  "certifications": ["cert1", "cert2"]
}

IMPORTANT INSTRUCTIONS:
- Extract ALL skills mentioned anywhere in the resume
- primary_skills should contain the 5 MOST relevant/important skills
- all_skills should contain EVERY skill you find (minimum 5-10 skills for most resumes)
- If you find fewer than 5 skills total, the resume parsing has failed
- Return only the JSON, no other text

RESUME TEXT TO ANALYZE:
${pdfText.trim()}`;

    // Create a fresh model instance with optimal settings for resume parsing
    const resumeModel = genAI.getGenerativeModel({
      model: 'gemini-2.0-flash',
      generationConfig: {
        temperature: 0.1, // Lower temperature for more consistent extraction
        topP: 0.8,
        topK: 40,
        maxOutputTokens: 3000 // Increased for detailed extraction
      }
    });

    console.log('=== Sending request to AI model ===');

    const result = await resumeModel.generateContent(prompt);
    const response = result.response;
    
    console.log('=== Received response from AI ===');
    let responseText = response.text();
    
    console.log('Raw AI Response (first 500 chars):', responseText.substring(0, 500));
    
    // Enhanced JSON parsing with better error handling
    let parsedResponse;
    try {
      // Remove any markdown formatting or extra text
      responseText = responseText
        .replace(/```json\s*|\s*```/g, '')
        .replace(/```\s*|\s*```/g, '')
        .trim();
      
      // Find JSON object boundaries
      const firstBrace = responseText.indexOf('{');
      const lastBrace = responseText.lastIndexOf('}');
      
      if (firstBrace === -1 || lastBrace === -1 || firstBrace >= lastBrace) {
        throw new Error('No valid JSON object found in AI response');
      }
      
      const jsonText = responseText.substring(firstBrace, lastBrace + 1);
      console.log('Extracted JSON text (first 300 chars):', jsonText.substring(0, 300));
      
      parsedResponse = JSON.parse(jsonText);
      console.log('Successfully parsed JSON response');
      
    } catch (parseError) {
      console.error('JSON parsing failed:', parseError);
      console.error('Response text:', responseText);
      throw new Error(`Failed to parse AI response as JSON: ${parseError.message}`);
    }

    // Validate and enhance the parsed response
    const defaultResponse = {
      personal_info: { name: '', email: '', phone: '', location: '' },
      education: [],
      experience: [],
      projects: [],
      skills: {
        primary_skills: [],
        secondary_skills: [],
        all_skills: [],
        skill_categories: {
          technical: [],
          soft: [],
          domain: []
        }
      },
      certifications: []
    };

    // Deep merge with parsed response
    const enhancedResponse = {
      personal_info: { ...defaultResponse.personal_info, ...(parsedResponse.personal_info || {}) },
      education: Array.isArray(parsedResponse.education) ? parsedResponse.education : [],
      experience: Array.isArray(parsedResponse.experience) ? parsedResponse.experience : [],
      projects: Array.isArray(parsedResponse.projects) ? parsedResponse.projects : [],
      certifications: Array.isArray(parsedResponse.certifications) ? parsedResponse.certifications : [],
      skills: defaultResponse.skills
    };

    // Handle skills with enhanced validation
    if (parsedResponse.skills) {
      // If skills is an array (old format), convert to new format
      if (Array.isArray(parsedResponse.skills)) {
        const skillsArray = parsedResponse.skills.filter(skill => 
          skill && typeof skill === 'string' && skill.trim().length > 0
        );
        enhancedResponse.skills = {
          primary_skills: skillsArray.slice(0, 5),
          secondary_skills: skillsArray.slice(5),
          all_skills: skillsArray,
          skill_categories: defaultResponse.skills.skill_categories
        };
      } else if (typeof parsedResponse.skills === 'object') {
        // New format - validate and clean
        const skills = parsedResponse.skills;
        
        enhancedResponse.skills = {
          primary_skills: Array.isArray(skills.primary_skills) ? 
            skills.primary_skills.filter(s => s && typeof s === 'string').slice(0, 5) : [],
          secondary_skills: Array.isArray(skills.secondary_skills) ? 
            skills.secondary_skills.filter(s => s && typeof s === 'string') : [],
          all_skills: Array.isArray(skills.all_skills) ? 
            skills.all_skills.filter(s => s && typeof s === 'string') : [],
          skill_categories: {
            technical: Array.isArray(skills.skill_categories?.technical) ? 
              skills.skill_categories.technical.filter(s => s && typeof s === 'string') : [],
            soft: Array.isArray(skills.skill_categories?.soft) ? 
              skills.skill_categories.soft.filter(s => s && typeof s === 'string') : [],
            domain: Array.isArray(skills.skill_categories?.domain) ? 
              skills.skill_categories.domain.filter(s => s && typeof s === 'string') : []
          }
        };
      }
    }

    // Ensure all_skills contains all skills if it's empty
    if (enhancedResponse.skills.all_skills.length === 0) {
      enhancedResponse.skills.all_skills = [
        ...enhancedResponse.skills.primary_skills,
        ...enhancedResponse.skills.secondary_skills,
        ...enhancedResponse.skills.skill_categories.technical,
        ...enhancedResponse.skills.skill_categories.soft,
        ...enhancedResponse.skills.skill_categories.domain
      ].filter((skill, index, arr) => skill && arr.indexOf(skill) === index); // Remove duplicates
    }

    // Log the final skills structure for debugging
    const skillsInfo = {
      primary_skills: enhancedResponse.skills.primary_skills?.length || 0,
      secondary_skills: enhancedResponse.skills.secondary_skills?.length || 0,
      total_skills: enhancedResponse.skills.all_skills?.length || 0,
      technical_skills: enhancedResponse.skills.skill_categories?.technical?.length || 0,
      soft_skills: enhancedResponse.skills.skill_categories?.soft?.length || 0,
      domain_skills: enhancedResponse.skills.skill_categories?.domain?.length || 0
    };
    
    console.log('Enhanced skills structure:', skillsInfo);
    
    // Log first few skills for verification
    if (enhancedResponse.skills.all_skills.length > 0) {
      console.log('Sample extracted skills:', enhancedResponse.skills.all_skills.slice(0, 5));
    } else {
      console.warn('WARNING: No skills were extracted from the resume');
    }

    return enhancedResponse;

  } catch (error) {
    console.error("\n=== Error in resumeStripper ===");
    console.error('Error details:', error.message);
    console.error('Error stack:', error.stack);
    
    // Return enhanced default structure on error, but log the failure
    const defaultResult = {
      personal_info: { name: '', email: '', phone: '', location: '' },
      education: [],
      experience: [],
      projects: [],
      skills: {
        primary_skills: [],
        secondary_skills: [],
        all_skills: [],
        skill_categories: {
          technical: [],
          soft: [],
          domain: []
        }
      },
      certifications: [],
      _error: error.message // Include error info for debugging
    };
    
    console.log('Returning default structure due to error');
    return defaultResult;
  }
};

exports.jobDescStripper = async (pdfBuffer) => {
  try {
    const prompt = await fs.promises.readFile(
      "utils/files/jobDescPrompt.txt",
      "utf8"
    );
    if (!prompt) throw new Error("Prompt file is empty");

    let extractedText;
    try {
      extractedText = await pdf(pdfBuffer);
    } catch (pdfErr) {
      console.error("PDF parsing error:", pdfErr);
      return {
        error: "Failed to parse PDF. The file may be corrupted or unsupported.",
        details: pdfErr.message || pdfErr.toString(),
      };
    }
    const cleanPdfText = extractedText.text?.trim?.() || "";
    if (!cleanPdfText) {
      return {
        error: "Extracted text from PDF is empty. Please upload a valid job description PDF.",
      };
    }

    let aiResponse;
    try {
      const result = await model.generateContent(`${prompt}\n\n${cleanPdfText}`);
      aiResponse = result.response;
    } catch (aiErr) {
      console.error("AI model error:", aiErr);
      return {
        error: "Failed to process job description with AI.",
        details: aiErr.message || aiErr.toString(),
      };
    }

    let cleaned;
    try {
      cleaned = cleanJSONResponse(typeof aiResponse.text === "function" ? aiResponse.text() : aiResponse);
      if (typeof cleaned === "object") return cleaned;
      return JSON.parse(cleaned);
    } catch (jsonErr) {
      console.error("AI response parsing error:", jsonErr);
      return {
        error: "AI response was not valid JSON.",
        details: jsonErr.message || jsonErr.toString(),
      };
    }
  } catch (error) {
    console.error("Error processing job description:", error);
    return {
      error: "Failed to process job description due to an unexpected error.",
      details: error.message || error.toString(),
    };
  }
};

function cleanJSONResponse(text) {
  try {
    // If text is already an object, return it directly
    if (typeof text === 'object' && text !== null) {
      return text;
    }
    
    // If text is not a string, try to stringify it first
    if (typeof text !== 'string') {
      text = String(text);
    }

    // First try direct parse
    try {
      return JSON.parse(text);
    } catch (e) {
      // Continue to other methods if direct parse fails
    }

    // Extract JSON object from text
    // Match the first top-level JSON object only (ignore trailing text)
    const jsonMatch = text.match(/\{[\s\S]*?\}/);
    if (!jsonMatch) {
      throw new Error('No JSON object found in response');
    }

    // Clean the text
    let cleanText = jsonMatch[0]
      .replace(/```(?:json)?|```/g, '')  // Remove code blocks
      .replace(/[\n\r]/g, ' ')           // Remove newlines
      .replace(/\s+/g, ' ')              // Normalize spaces
      .replace(/,\s*([}\]])/g, '$1')     // Remove trailing commas
      .replace(/,,+/g, ',')              // Remove double commas
      .replace(/([{\[,])/g, '$1 ')       // Add space after { [ ,
      .replace(/([}\]])/g, ' $1')        // Add space before } ]
      .replace(/\s+/g, ' ')              // Normalize spaces again
      .trim();

    // Remove any trailing non-JSON characters after the last closing brace
    const lastBrace = cleanText.lastIndexOf('}');
    if (lastBrace !== -1 && lastBrace < cleanText.length - 1) {
      cleanText = cleanText.substring(0, lastBrace + 1);
    }

    // Attempt to fix missing closing braces/brackets
    const openBraces = (cleanText.match(/{/g) || []).length;
    const closeBraces = (cleanText.match(/}/g) || []).length;
    if (openBraces > closeBraces) {
      cleanText += '}'.repeat(openBraces - closeBraces);
    }
    const openBrackets = (cleanText.match(/\[/g) || []).length;
    const closeBrackets = (cleanText.match(/]/g) || []).length;
    if (openBrackets > closeBrackets) {
      cleanText += ']'.repeat(openBrackets - closeBrackets);
    }

    // Try parsing cleaned text
    return JSON.parse(cleanText);

  } catch (error) {
    console.error("JSON cleaning error:", error);
    // Return a default object for any error
    return {
      score: {
        Skill_Score: 5,
        Communication_Score: 5,
        Overall_Score: 5
      },
      evaluation: [],
      Suggestions: `Failed to clean response: ${error.message}. Using default scores.`
    };
  }
}

/**
 * Generate natural language dynamic prompt instructions for AI interviewer
 * Creates human-like guidance based on candidate profile and job requirements
 */
exports.generateDynamicPrompt = async (jobRole, skills, companyType, experience, salary, completeResumeData) => {
  try {
    if (!jobRole || typeof jobRole !== 'string' || jobRole.trim().length === 0) {
      throw new Error('Valid job role is required for dynamic prompt generation');
    }

    // Prepare candidate context from resume data
    let candidateContext = '';
    if (completeResumeData) {
      const resumeHighlights = [];
      
      // Extract key experience highlights
      if (completeResumeData.experience && completeResumeData.experience.length > 0) {
        const recentExp = completeResumeData.experience.slice(0, 2);
        resumeHighlights.push(`Recent experience: ${recentExp.map(exp => 
          `${exp.title} at ${exp.company}`).join(', ')}`);
      }
      
      // Extract key skills
      if (completeResumeData.skills) {
        const allSkills = completeResumeData.skills.all_skills || 
                         completeResumeData.skills.primary_skills || 
                         completeResumeData.skills || [];
        if (allSkills.length > 0) {
          resumeHighlights.push(`Key skills from resume: ${allSkills.slice(0, 8).join(', ')}`);
        }
      }
      
      // Extract notable projects
      if (completeResumeData.projects && completeResumeData.projects.length > 0) {
        const projectTitles = completeResumeData.projects.slice(0, 2).map(p => p.title).join(', ');
        resumeHighlights.push(`Notable projects: ${projectTitles}`);
      }
      
      candidateContext = resumeHighlights.join('. ');
    }

    const prompt = `You are an expert HR interviewer creating personalized interview guidance. Analyze this candidate's profile and create strategic interview instructions.

CANDIDATE PROFILE:
- Job Role: ${jobRole.trim()}
- Target Skills: ${Array.isArray(skills) ? skills.join(', ') : skills || 'General role skills'}
- Experience Level: ${experience || 'Not specified'} years
- Company Type: ${companyType || 'Not specified'}
- Expected Salary: ${salary || 'Not specified'}
- Resume Analysis: ${candidateContext || 'Limited resume data available'}

INTERVIEW REQUIREMENTS:
- Duration: Maximum 5 minutes (5-6 questions total)
- Coverage: Must evaluate ALL key competencies before ending
- Assessment: Focus on potential and fit, not just technical perfection
- Termination: Only after comprehensive evaluation, not due to 2-3 poor answers

INSTRUCTIONS:
Analyze the job role and candidate background to determine:
1. Whether this role requires technical implementation skills (coding questions) or primarily business/professional skills (verbal questions only)
2. What key competencies must be verified based on the role requirements and candidate background
3. How to calibrate questions based on the candidate's experience level and resume context
4. What question mix (coding vs verbal) is most appropriate for comprehensive evaluation
5. What completion criteria should be used to ensure all essential areas are covered

Create comprehensive interview guidance that tells the AI interviewer:
- What specific areas to focus on for this role and candidate combination
- What question types to use and in what proportion (determine based on role analysis)
- What experience level to target questions at based on candidate background
- What must be covered before the interview can be considered complete
- How to assess candidate potential even if some answers are weak in certain areas

Write as if briefing a human interviewer. Be specific about this candidate's background and what needs verification. Do not make assumptions about role types - analyze the actual role and determine the appropriate approach.

Return ONLY the comprehensive instruction paragraph, no additional formatting.`;

    console.log(`=== Generating dynamic prompt for role: ${jobRole} ===`);

    const result = await model.generateContent(prompt);
    const response = result.response;
    let dynamicPromptText = response.text().trim();

    console.log('Generated dynamic prompt:', dynamicPromptText.substring(0, 200) + '...');

    // Clean up any unwanted formatting
    dynamicPromptText = dynamicPromptText
      .replace(/```.*```/g, '')
      .replace(/^\*+\s*/, '')
      .replace(/\n+/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();

    if (!dynamicPromptText || dynamicPromptText.length < 50) {
      throw new Error('Generated dynamic prompt is too short or empty');
    }

    console.log(`=== Successfully generated dynamic prompt (${dynamicPromptText.length} chars) ===`);
    return dynamicPromptText;

  } catch (error) {
    console.error('Error generating dynamic prompt:', error);
    
    // Return fallback instruction based on job role
    return getFallbackDynamicPrompt(jobRole, skills, experience, companyType);
  }
};

/**
 * Generate fallback dynamic prompt when AI fails
 */
function getFallbackDynamicPrompt(jobRole, skills, experience, companyType) {
  const skillsList = Array.isArray(skills) ? skills.slice(0, 3).join(', ') : skills || 'relevant skills';
  const expLevel = experience || 'entry-level';

  return `Focus on evaluating this candidate for ${jobRole} position with ${expLevel} years experience. Analyze the role requirements to determine whether this position needs technical implementation skills (coding questions) or primarily business/professional skills (verbal questions only). Cover ALL key competencies for this role including ${skillsList} and relevant experience areas within 5-6 questions maximum. Do NOT terminate early due to 2-3 poor answers - ensure comprehensive evaluation of all essential areas before ending. Assess the candidate's potential and fit for the role, not just technical perfection. Complete only when you've thoroughly evaluated their capabilities, practical experience, and suitability for this specific position within the 5-minute time limit. Focus on understanding their learning ability and potential growth if they show gaps in certain areas.`;
}

/**
 * Generate AI-recommended skills based on job role
 * Returns exactly 5 relevant skills for the given job role
 */
exports.generateSkillRecommendations = async (jobRole) => {
  try {
    if (!jobRole || typeof jobRole !== 'string' || jobRole.trim().length === 0) {
      throw new Error('Valid job role is required for skill recommendations');
    }

    const prompt = `You are an expert career advisor and technical recruiter. Given the job role "${jobRole.trim()}", recommend exactly 5 most relevant and in-demand skills that are essential for this position.

Consider:
1. Core technical skills required for this role
2. Industry-standard tools and technologies
3. Most sought-after skills by employers for this position
4. Skills that would make a candidate competitive in the job market

Return ONLY a JSON array of exactly 5 skill names as strings. No explanations, no additional text.

Example format: ["JavaScript", "React", "Node.js", "SQL", "Git"]

Job Role: ${jobRole.trim()}`;

    if (!model) {
      const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY);
      model = genAI.getGenerativeModel({
        model: 'gemini-2.0-flash',
        generationConfig: {
          temperature: 0.3,
          topP: 0.8,
          topK: 40,
          maxOutputTokens: 200
        }
      });
    }

    console.log(`=== Generating skill recommendations for role: ${jobRole} ===`);

    const result = await model.generateContent(prompt);
    const response = result.response;
    let responseText = response.text();

    console.log('Raw AI response:', responseText);

    // Clean the response to extract JSON
    responseText = responseText.replace(/```json\s*|\s*```/g, '').trim();

    let skills;
    try {
      skills = JSON.parse(responseText);
    } catch (parseError) {
      console.error('Failed to parse AI response as JSON:', parseError);
      // Fallback: try to extract skills from text
      const skillMatches = responseText.match(/"([^"]+)"/g);
      if (skillMatches && skillMatches.length >= 5) {
        skills = skillMatches.slice(0, 5).map(match => match.replace(/"/g, ''));
      } else {
        throw new Error('Failed to extract skills from AI response');
      }
    }

    // Validate response
    if (!Array.isArray(skills) || skills.length !== 5) {
      throw new Error(`Expected exactly 5 skills, got ${skills?.length || 0}`);
    }

    // Ensure all skills are valid strings
    const validSkills = skills
      .filter(skill => skill && typeof skill === 'string' && skill.trim().length > 0)
      .map(skill => skill.trim())
      .slice(0, 5);

    if (validSkills.length !== 5) {
      throw new Error(`Invalid skills format. Expected 5 valid strings, got ${validSkills.length}`);
    }

    console.log(`=== Successfully generated ${validSkills.length} skill recommendations ===`);
    return validSkills;

  } catch (error) {
    console.error('Error generating skill recommendations:', error);

    // Return fallback skills based on common job role patterns
    const fallbackSkills = getFallbackSkills(jobRole);
    console.log(`Using fallback skills for ${jobRole}:`, fallbackSkills);
    return fallbackSkills;
  }
};

/**
 * Get fallback skills for common job roles when AI fails
 */
function getFallbackSkills(jobRole) {
  const role = jobRole.toLowerCase().trim();

  if (role.includes('frontend') || role.includes('front-end') || role.includes('react') || role.includes('angular') || role.includes('vue')) {
    return ['JavaScript', 'HTML', 'CSS', 'React', 'Git'];
  } else if (role.includes('backend') || role.includes('back-end') || role.includes('server') || role.includes('api')) {
    return ['Node.js', 'JavaScript', 'SQL', 'REST API', 'Git'];
  } else if (role.includes('fullstack') || role.includes('full-stack') || role.includes('full stack')) {
    return ['JavaScript', 'React', 'Node.js', 'SQL', 'Git'];
  } else if (role.includes('data') && (role.includes('scientist') || role.includes('analyst') || role.includes('engineer'))) {
    return ['Python', 'SQL', 'Machine Learning', 'Pandas', 'Git'];
  } else if (role.includes('devops') || role.includes('cloud') || role.includes('infrastructure')) {
    return ['AWS', 'Docker', 'Kubernetes', 'Linux', 'Git'];
  } else if (role.includes('mobile') || role.includes('android') || role.includes('ios')) {
    return ['Java', 'Kotlin', 'Swift', 'React Native', 'Git'];
  } else if (role.includes('qa') || role.includes('test') || role.includes('quality')) {
    return ['Selenium', 'Java', 'Test Automation', 'API Testing', 'Git'];
  } else if (role.includes('ui') || role.includes('ux') || role.includes('design')) {
    return ['Figma', 'Adobe XD', 'Prototyping', 'User Research', 'Wireframing'];
  } else {
    // Generic software development skills
    return ['JavaScript', 'Python', 'SQL', 'Git', 'Problem Solving'];
  }
}

