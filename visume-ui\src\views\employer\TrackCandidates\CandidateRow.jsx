import React from "react";
import { useNavigate } from "react-router-dom";
import {
  HiOutlineVideoCamera,
  HiOutlineDocumentText,
  HiOutlineBriefcase,
  HiLockOpen,
  HiLockClosed,
  HiOutlineTrash,
} from "react-icons/hi";
import defaultProfile from "../../../assets/img/default-profile.png";
import ScoreButton from "../components/ScoreButton";

const CandidateRow = ({
  candidate,
  loadingId,
  onStatusChange,
  onToggleUnlock,
  unShortlistCandidate,
  tabName,
}) => {
  const navigate = useNavigate();

  const candidateName =
    candidate.cand_name ||
    (candidate.jobseeker && candidate.jobseeker.cand_name) ||
    "Candidate";

  const profileImg =
    candidate.profile_picture ||
    (candidate.jobseeker && candidate.jobseeker.profile_picture);

  // Use default profile as fallback if profileImg is missing or empty string
  const fallbackAvatar = defaultProfile;
  const profileSrc =
    profileImg && profileImg.trim() !== ""
      ? `${import.meta.env.VITE_APP_HOST}/${profileImg}`
      : fallbackAvatar;

  const scoreData = (() => {
    try {
      const parsed = JSON.parse(candidate?.score || "{}")?.score || {};
      return {
        overallScore: parsed.Overall_Score || 0,
        skillScore: parsed.Skill_Score || 0,
        communicationScore: parsed.Communication_Score || 0,
      };
    } catch {
      return { overallScore: 0, skillScore: 0, communicationScore: 0 };
    }
  })();

  const isUnlocked = tabName.toLowerCase() === "unlocked";

  return (
    <tr className="hover:bg-gray-50 dark:hover:bg-navy-700/50 transition-colors duration-200" style={{fontFamily: 'Manrope, sans-serif'}}>
      <td className="px-4 py-3">
        <div className="flex items-center space-x-3">
          <div className="relative flex-shrink-0">
            <img
              src={profileSrc}
              alt={`${candidateName} avatar`}
              className="h-10 w-10 rounded-full object-cover ring-2 ring-gray-200 dark:ring-navy-600"
              onError={(e) => {
                e.target.onerror = null;
                e.target.src = fallbackAvatar;
              }}
            />
            <div className="absolute -bottom-0.5 -right-0.5 h-3 w-3 rounded-full bg-green-500 ring-2 ring-white dark:ring-navy-800"></div>
          </div>
          <div className="min-w-0 flex-1">
            <h3
              onClick={() =>
                navigate(`/profile/${candidate.video_profile_id}`)
              }
              className="cursor-pointer font-semibold text-sm text-gray-900 hover:text-blue-600 dark:text-white dark:hover:text-blue-400 transition-colors duration-200"
              style={{fontFamily: 'Sora, sans-serif'}}
            >
              {candidateName}
            </h3>
            <div className="flex items-center space-x-2 mt-1">
              <div className="flex items-center space-x-1">
                <HiOutlineBriefcase className="h-3 w-3 text-gray-400" />
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {candidate.preferred_location || "N/A"}
                </span>
              </div>
              <div className="flex space-x-1">
                <HiOutlineVideoCamera className="h-3 w-3 text-blue-500" title="Video Resume" />
                <HiOutlineDocumentText className="h-3 w-3 text-green-500" title="Resume" />
              </div>
            </div>
          </div>
        </div>
      </td>
      <td className="px-4 py-3">
        <span className="inline-flex items-center rounded-full bg-purple-50 px-2.5 py-0.5 text-xs font-medium text-purple-700 ring-1 ring-purple-600/20">
          {candidate.role || "Full Stack Developer"}
        </span>
      </td>
      <td className="px-4 py-3">
        <div className="flex flex-wrap gap-1">
          {candidate.cand_skills?.split(",").slice(0, 3).map((skill, index) => (
            <span
              key={index}
              className="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-blue-600/20"
            >
              {skill.trim()}
            </span>
          ))}
          {candidate.cand_skills && candidate.cand_skills.split(",").length > 3 && (
            <span className="inline-flex items-center rounded-md bg-gray-50 px-2 py-1 text-xs font-medium text-gray-600 ring-1 ring-gray-500/20">
              +{candidate.cand_skills.split(",").length - 3} more
            </span>
          )}
        </div>
      </td>
      <td className="px-4 py-3">
        <ScoreButton {...scoreData} />
      </td>
      <td className="px-4 py-3">
        <div className="flex items-center space-x-2">
          {candidate.unlocked ? (
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              {candidate.cand_email}
            </span>
          ) : (
            <span className="text-sm text-gray-500 dark:text-gray-400 font-mono">
              ***@***.com
            </span>
          )}
          <button
            onClick={() => onToggleUnlock(candidate.id)}
            className={`rounded-md p-1 transition-colors duration-200 ${
              candidate.unlocked
                ? "text-green-600 hover:bg-green-100 dark:hover:bg-green-900/20"
                : "text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700"
            }`}
          >
            {candidate.unlocked ? <HiLockOpen className="h-4 w-4" /> : <HiLockClosed className="h-4 w-4" />}
          </button>
        </div>
      </td>
      <td className="px-4 py-3">
        <select
          value={
            candidate.status.charAt(0).toUpperCase() +
            candidate.status.slice(1)
          }
          onChange={(e) => onStatusChange(candidate.id, e.target.value)}
          className="rounded-md border border-gray-300 bg-white px-3 py-1.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 dark:border-navy-600 dark:bg-navy-700 dark:text-white"
        >
          {["Shortlisted", "Unlocked", "Interviewed", "Offers"]
            .filter((status) => {
              const c = candidate.status;
              if (c === "shortlisted") return status === "Shortlisted" || status === "Unlocked";
              if (c === "unlocked") return status !== "Shortlisted";
              if (c === "interviewed") return status === "Interviewed" || status === "Offers";
              if (c === "offers") return status === "Offers";
              return true;
            })
            .map((status) => (
              <option key={status} value={status}>
                {loadingId === candidate.video_profile_id
                  ? "Loading..."
                  : status}
              </option>
            ))}
        </select>
      </td>
      <td className="px-4 py-3">
        <div className="flex items-center space-x-2">
          <button
            disabled={isUnlocked}
            onClick={() => !isUnlocked && onStatusChange(candidate.id, "Unlocked")}
            className={`inline-flex items-center gap-1.5 rounded-md px-3 py-1.5 text-xs font-medium transition-all duration-200 ${
              !isUnlocked
                ? "bg-blue-50 text-blue-700 hover:bg-blue-100 ring-1 ring-blue-600/20"
                : "bg-gray-50 text-gray-400 cursor-not-allowed ring-1 ring-gray-200"
            }`}
          >
            {!isUnlocked ? (
              <>
                <HiLockOpen className="h-3 w-3" />
                <span>Unlock</span>
              </>
            ) : (
              <>
                <HiLockClosed className="h-3 w-3" />
                <span>Unlocked</span>
              </>
            )}
          </button>

          {!isUnlocked && (
            <button
              onClick={() =>
                unShortlistCandidate(
                  candidate.video_profile_id,
                  candidate.cand_id
                )
              }
              disabled={loadingId === candidate.cand_id}
              className={`inline-flex items-center gap-1.5 rounded-md px-3 py-1.5 text-xs font-medium transition-all duration-200 ${
                loadingId === candidate.cand_id
                  ? "bg-gray-50 text-gray-400 cursor-not-allowed ring-1 ring-gray-200"
                  : "bg-red-50 text-red-700 hover:bg-red-100 ring-1 ring-red-600/20"
              }`}
            >
              <HiOutlineTrash className={`h-3 w-3 ${loadingId === candidate.cand_id ? "text-gray-400" : "text-red-500"}`} />
              {loadingId === candidate.cand_id ? (
                <div className="flex items-center gap-1.5">
                  <svg
                    className="h-3 w-3 animate-spin text-gray-400"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                      fill="none"
                    />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    />
                  </svg>
                  <span>Loading...</span>
                </div>
              ) : (
                <span>Remove</span>
              )}
            </button>
          )}
        </div>
      </td>
    </tr>
  );
};

export default CandidateRow;
