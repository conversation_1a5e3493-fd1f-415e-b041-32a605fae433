import React from 'react'
import { motion } from 'framer-motion';
import CustomNavbar from './components/CustomNavbar';
import { useNavigate } from "react-router-dom";
import { HiHome, HiOutlineSparkles } from 'react-icons/hi';
import { MdBarChart } from 'react-icons/md';
import ProfileSearchUI from './ProfilesUI';

const links = [
    { text: 'Dashboard', url: '/employer/', icon: <HiHome className="h-4 w-4" /> },
    { text: 'Track Candidates', url: '/employer/track-candidates', icon: <MdBarChart className="h-4 w-4" /> },
    {
    text: (
            <span className='flex items-center'>
                Source with AI <span className="bg-orange-400 text-white text-xs font-semibold rounded-full px-2 ml-2">Beta</span>
            </span>
    ),
    url: '/employer',
    icon: <HiOutlineSparkles className="h-4 w-4" />,
    className: 'text-orange-400 font-bold hover:text-orange-500'
},
];


const ProfileSearch = () => {
  return (
    <div className="relative min-h-screen overflow-hidden bg-white dark:bg-gray-900">
      {/* Google Fonts Import */}
      <link href="https://fonts.googleapis.com/css2?family=Sora:wght@400;600;700&family=Manrope:wght@400;500;600;700&display=swap" rel="stylesheet" />
      
      <CustomNavbar links={links} />
      
      {/* Main Content with proper spacing for fixed navbar */}
      <motion.div
        className="relative z-10 pt-28"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.2 }}
      >
        <ProfileSearchUI />
      </motion.div>
    </div>
  )
}

export default ProfileSearch