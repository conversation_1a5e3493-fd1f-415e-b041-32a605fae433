import React, { useState, useRef, useEffect } from "react";

export default function HiringBentoGrid() {
  const [currentSlide, setCurrentSlide] = useState(0);
  const sliderRef = useRef(null);
  const totalCards = 5;
  const [touchStart, setTouchStart] = useState(null);
  const [touchEnd, setTouchEnd] = useState(null);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % totalCards);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + totalCards) % totalCards);
  };

  const goToSlide = (index) => {
    setCurrentSlide(index);
  };

  // Touch handlers for swipe functionality
  const onTouchStart = (e) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const onTouchMove = (e) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return;
    
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe) {
      nextSlide();
    }
    if (isRightSwipe) {
      prevSlide();
    }
  };

  useEffect(() => {
    if (sliderRef.current) {
      sliderRef.current.style.transform = `translateX(-${currentSlide * 20}%)`;
    }
  }, [currentSlide]);

  return (
    <section className="py-6 lg:py-8 bg-white relative">
      <style>{`
        @import url('https://fonts.googleapis.com/css2?family=Manrope:wght@200;300;400;500;600;700;800&family=Sora:wght@100;200;300;400;500;600;700;800&display=swap');
        
        .bento-grid {
          display: grid;
          gap: 1.25rem;
          margin-top: 1.5rem;
          grid-template-columns: 1fr 1fr;
          grid-template-rows: auto auto;
          max-width: 950px;
          margin-left: auto;
          margin-right: auto;
          transform: scale(0.95);
        }
        
        .bento-card {
          background: #ffffff;
          border: 2px solid #93c5fd;
          border-radius: 1.25rem;
          padding: 0;
          display: flex;
          flex-direction: column;
          position: relative;
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          overflow: hidden;
          box-shadow: 0 8px 32px rgba(59, 130, 246, 0.12);
          aspect-ratio: 4/5;
        }
        
        .bento-card::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, rgba(59, 130, 246, 0.08) 0%, rgba(147, 197, 253, 0.04) 100%);
          opacity: 0;
          transition: opacity 0.4s ease;
          z-index: 1;
        }
        
        .bento-card:hover::before {
          opacity: 1;
        }
        
        .bento-card:hover {
          transform: translateY(-12px);
          border-color: #3b82f6;
          box-shadow: 0 24px 48px rgba(59, 130, 246, 0.2);
        }
        
        /* Top row - 2 equal cards */
        .bento-top-1 {
          grid-column: 1;
          grid-row: 1;
          aspect-ratio: 5/4;
        }
        
        .bento-top-2 {
          grid-column: 2;
          grid-row: 1;
          aspect-ratio: 5/4;
        }
        
        /* Adjust text areas for top cards */
        .bento-top-1 .text-area,
        .bento-top-2 .text-area {
          height: 25%;
          padding: 0.75rem 1rem;
        }
        
        .bento-top-1 .visual-area,
        .bento-top-2 .visual-area {
          height: 75%;
        }
        
        /* Bottom row setup for 3 cards */
        .bottom-row {
          grid-column: 1 / 3;
          grid-row: 2;
          display: grid;
          grid-template-columns: 1fr 1fr 1fr;
          gap: 1.25rem;
        }
        
        .bottom-row .bento-card {
          margin: 0;
        }
        
        /* Visual Area (top 70%) */
        .visual-area {
          height: 70%;
          width: 100%;
          background: #ffffff;
          position: relative;
          overflow: hidden;
          border-radius: 1rem 1rem 0 0;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        
        /* Text Area (bottom 30%) */
        .text-area {
          height: 30%;
          width: 100%;
          padding: 1rem 1.25rem;
          display: flex;
          flex-direction: column;
          justify-content: center;
          position: relative;
          z-index: 2;
          background: white;
        }
        
        .text-area h3 {
          margin: 0 0 0.5rem 0;
          font-size: 1.1rem;
          font-weight: 700;
          color: #1f2937;
          line-height: 1.3;
          font-family: 'Manrope', sans-serif;
        }
        
        .text-area p {
          margin: 0;
          font-size: 0.85rem;
          color: #6b7280;
          line-height: 1.5;
          font-weight: 400;
          font-family: 'Sora', sans-serif;
        }
        
        .blue-gradient-text {
          background: linear-gradient(135deg, #3b82f6 0%, #1e40af 50%, #1d4ed8 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }
        
        /* Visual images */
        .visual-image {
          width: 95%;
          height: 95%;
          object-fit: contain;
          border-radius: 0.75rem;
          max-width: 100%;
          max-height: 100%;
        }
        
        /* Mobile Slider Styles */
        .mobile-slider-container {
          display: none;
          position: relative;
          overflow: hidden;
          width: 100%;
          max-width: 350px;
          margin: 0 auto;
        }
        
        .mobile-slider {
          display: flex;
          transition: transform 0.3s ease-in-out;
          width: 500%;
        }
        
        .mobile-slide {
          width: 20%;
          flex-shrink: 0;
          padding: 0 0.5rem;
        }
        
        .mobile-slide .bento-card {
          width: 100%;
          aspect-ratio: 3/4;
          transform: none;
        }
        
        .slider-controls {
          display: flex;
          justify-content: center;
          align-items: center;
          gap: 1rem;
          margin-top: 1.5rem;
        }
        
        .slider-btn {
          background: #3b82f6;
          border: none;
          border-radius: 50%;
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.2s ease;
          color: white;
          font-size: 18px;
        }
        
        .slider-btn:hover {
          background: #2563eb;
          transform: scale(1.1);
        }
        
        .slider-btn:disabled {
          background: #94a3b8;
          cursor: not-allowed;
          transform: none;
        }
        
        .slider-dots {
          display: flex;
          gap: 0.5rem;
        }
        
        .slider-dot {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: #cbd5e1;
          cursor: pointer;
          transition: all 0.2s ease;
        }
        
        .slider-dot.active {
          background: #3b82f6;
          transform: scale(1.25);
        }
        
        @media (max-width: 768px) {
          .bento-grid {
            display: none;
          }
          
          .mobile-slider-container {
            display: block;
          }
          
          .mobile-slide .text-area {
            padding: 1rem;
          }
          
          .mobile-slide .text-area h3 {
            font-size: 1rem;
          }
          
          .mobile-slide .text-area p {
            font-size: 0.85rem;
          }
        }
      `}</style>
      
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <div className="mb-6 inline-flex items-center rounded-full bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200/50 px-6 py-2.5 text-sm font-medium text-blue-700 shadow-sm" style={{ fontFamily: "Sora, sans-serif" }}>
            Platform Features
          </div>
          <h2 className="text-slate-900 mb-6 text-3xl font-bold leading-tight sm:text-4xl lg:text-5xl" style={{ fontFamily: "Manrope, sans-serif" }}>
            Everything you need to{" "}
            <span className="blue-gradient-text">
              hire smarter
            </span>
          </h2>
          <p className="text-slate-600 max-w-3xl mx-auto text-lg leading-relaxed" style={{ fontFamily: "Sora, sans-serif" }}>
            Transform your recruitment process with intelligent matching, video interviews, and data-driven insights.
          </p>
        </div>
        
        {/* Desktop Bento Grid */}
        <div className="bento-grid">
          {/* Top Row - Card 1: AI-Powered Candidate Matching */}
          <div className="bento-card bento-top-1">
            <div className="visual-area">
              <img 
                src="/bentoimg/smatch matchmaking.png" 
                alt="AI-powered candidate matching interface" 
                className="visual-image"
              />
            </div>
            <div className="text-area">
              <h3>
                Smart Candidate <span className="blue-gradient-text">Matching</span>
              </h3>
              <p>Discover the perfect candidates in seconds with our AI-powered matching system that analyzes skills, experience, and cultural fit.</p>
            </div>
          </div>

          {/* Top Row - Card 2: Video Resume Access */}
          <div className="bento-card bento-top-2">
            <div className="visual-area">
              <img 
                src="/bentoimg/prescreenedinterviews.png" 
                alt="Video interview platform interface" 
                className="visual-image"
              />
            </div>
            <div className="text-area">
              <h3>
                Pre-Screened <span className="blue-gradient-text">Video Interviews</span>
              </h3>
              <p>Browse high-quality video interviews from candidates who have been vetted and assessed by our AI.</p>
            </div>
          </div>

          {/* Bottom Row - 3 Equal Cards */}
          <div className="bottom-row">
            {/* Card 3: Real-Time Analytics */}
            <div className="bento-card">
              <div className="visual-area">
                <img 
                  src="/bentoimg/analysis.png" 
                  alt="Advanced hiring analytics dashboard" 
                  className="visual-image"
                />
              </div>
              <div className="text-area">
                <h3>
                  Advanced Hiring <span className="blue-gradient-text">Analytics</span>
                </h3>
                <p>Get detailed insights on candidate performance, hiring trends, and team fit predictions.</p>
              </div>
            </div>

            {/* Card 4: Credit Management */}
            <div className="bento-card">
              <div className="visual-area">
                <img 
                  src="/bentoimg/planandcredits.png" 
                  alt="Plan and credits management interface" 
                  className="visual-image"
                />
              </div>
              <div className="text-area">
                <h3>
                  Plan & <span className="blue-gradient-text">Credits</span>
                </h3>
                <p>Track your usage and manage billing with transparent credit system.</p>
              </div>
            </div>

            {/* Card 5: Candidate Tracking */}
            <div className="bento-card">
              <div className="visual-area">
                <img 
                  src="/bentoimg/trackingprogress.png" 
                  alt="Candidate tracking and progress pipeline" 
                  className="visual-image"
                />
              </div>
              <div className="text-area">
                <h3>
                  Track <span className="blue-gradient-text">Progress</span>
                </h3>
                <p>Monitor candidates through all pipeline stages seamlessly.</p>
              </div>
            </div>
          </div>
        </div>

        {/* Mobile Slider */}
        <div className="mobile-slider-container">
          <div 
            className="mobile-slider" 
            ref={sliderRef}
            onTouchStart={onTouchStart}
            onTouchMove={onTouchMove}
            onTouchEnd={onTouchEnd}
          >
            {/* Slide 1: AI-Powered Candidate Matching */}
            <div className="mobile-slide">
              <div className="bento-card">
                <div className="visual-area">
                  <img 
                    src="/bentoimg/smatch matchmaking.png" 
                    alt="AI-powered candidate matching interface" 
                    className="visual-image"
                  />
                </div>
                <div className="text-area">
                  <h3>
                    Smart Candidate <span className="blue-gradient-text">Matching</span>
                  </h3>
                  <p>Discover the perfect candidates in seconds with our AI-powered matching system that analyzes skills, experience, and cultural fit.</p>
                </div>
              </div>
            </div>

            {/* Slide 2: Video Resume Access */}
            <div className="mobile-slide">
              <div className="bento-card">
                <div className="visual-area">
                  <img 
                    src="/bentoimg/prescreenedinterviews.png" 
                    alt="Video interview platform interface" 
                    className="visual-image"
                  />
                </div>
                <div className="text-area">
                  <h3>
                    Pre-Screened <span className="blue-gradient-text">Video Interviews</span>
                  </h3>
                  <p>Browse high-quality video interviews from candidates who have been vetted and assessed by our AI.</p>
                </div>
              </div>
            </div>

            {/* Slide 3: Real-Time Analytics */}
            <div className="mobile-slide">
              <div className="bento-card">
                <div className="visual-area">
                  <img 
                    src="/bentoimg/analysis.png" 
                    alt="Advanced hiring analytics dashboard" 
                    className="visual-image"
                  />
                </div>
                <div className="text-area">
                  <h3>
                    Advanced Hiring <span className="blue-gradient-text">Analytics</span>
                  </h3>
                  <p>Get detailed insights on candidate performance, hiring trends, and team fit predictions.</p>
                </div>
              </div>
            </div>

            {/* Slide 4: Credit Management */}
            <div className="mobile-slide">
              <div className="bento-card">
                <div className="visual-area">
                  <img 
                    src="/bentoimg/planandcredits.png" 
                    alt="Plan and credits management interface" 
                    className="visual-image"
                  />
                </div>
                <div className="text-area">
                  <h3>
                    Plan & <span className="blue-gradient-text">Credits</span>
                  </h3>
                  <p>Track your usage and manage billing with transparent credit system.</p>
                </div>
              </div>
            </div>

            {/* Slide 5: Candidate Tracking */}
            <div className="mobile-slide">
              <div className="bento-card">
                <div className="visual-area">
                  <img 
                    src="/bentoimg/trackingprogress.png" 
                    alt="Candidate tracking and progress pipeline" 
                    className="visual-image"
                  />
                </div>
                <div className="text-area">
                  <h3>
                    Track <span className="blue-gradient-text">Progress</span>
                  </h3>
                  <p>Monitor candidates through all pipeline stages seamlessly.</p>
                </div>
              </div>
            </div>
          </div>

          {/* Slider Controls */}
          <div className="slider-controls">
            <button className="slider-btn" onClick={prevSlide}>
              ←
            </button>
            <div className="slider-dots">
              {Array.from({ length: totalCards }, (_, index) => (
                <div
                  key={index}
                  className={`slider-dot ${currentSlide === index ? 'active' : ''}`}
                  onClick={() => goToSlide(index)}
                />
              ))}
            </div>
            <button className="slider-btn" onClick={nextSlide}>
              →
            </button>
          </div>
        </div>
      </div>
      
      {/* Enhanced Custom Styles */}
      <style>{`
        @import url('https://fonts.googleapis.com/css2?family=Manrope:wght@200;300;400;500;600;700;800&family=Sora:wght@100;200;300;400;500;600;700;800&display=swap');
      `}</style>
    </section>
  );
}