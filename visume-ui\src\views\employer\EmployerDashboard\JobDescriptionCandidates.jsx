import React, { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";

import { useNavigate } from "react-router-dom";

import ProfileCard from "../ProfilesUI/ProfileCard";
import Cookies from "js-cookie";
import toast from "react-hot-toast";
import { HiOutlineUserGroup, HiOutlineSparkles, HiOutlineShare, HiOutlineLocationMarker, HiOutlineClock, HiOutlineOfficeBuilding, HiOutlineBriefcase } from "react-icons/hi";
const JobDescriptionCandidates = () => {
  const { state } = useLocation();
  const job = state?.job;
  const [candidates, setCandidates] = useState([]);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const [loadingId, setLoadingId] = useState(null);
  const [selectedProfiles, setSelectedProfiles] = useState(new Set());
  const [openTooltipId, setOpenTooltipId] = useState(null);

  const handleShortlist = async (video_profile_id, cand_id) => {
    if (loadingId === video_profile_id) return;
    setLoadingId(video_profile_id);
    try {
      const emp_id = job?.emp_id || Cookies.get("employerId");
      if (!emp_id) {
        alert("You need to be an employer to shortlist profiles");
        return;
      }
      const response = await fetch(
        `${
          import.meta.env.VITE_APP_HOST
        }/api/v1/employer-profiles/shortlist-candidate`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ emp_id, cand_id: video_profile_id }),
        }
      );
      if (!response.ok) {
        const msg = await response.json();
        alert(msg.message);
        throw new Error(`HTTP error! status: ${msg.message}`);
      }
      const data = await response.json();
      toast.success(data.message || "Profile shortlisted!");
      setCandidates((prev) =>
        prev.filter(
          (cand) =>
            (cand.video_profile_id || cand.id || cand._id) !== video_profile_id
        )
      );
      setSelectedProfiles((prev) => {
        const newSelectedProfiles = new Set(prev);
        if (newSelectedProfiles.has(video_profile_id)) {
          newSelectedProfiles.delete(video_profile_id);
        } else {
          newSelectedProfiles.add(video_profile_id);
        }
        return newSelectedProfiles;
      });
    } catch (error) {
      console.error("Error during shortlist operation:", error);
    } finally {
      setLoadingId(null);
    }
  };

  useEffect(() => {
    const fetchCandidates = async () => {
      setLoading(true);
      try {
        if (!job) return;
        // Fetch candidates matching role and skills
        const candRes = await fetch(
          `${
            import.meta.env.VITE_APP_HOST
          }/api/v1/filterCandidate?role=${encodeURIComponent(job.role)}`
        );
        const candData = await candRes.json();

        let arr = [];
        if (Array.isArray(candData.candidates)) arr = candData.candidates;
        else if (Array.isArray(candData.candidateProfiles))
          arr = candData.candidateProfiles;
        else if (Array.isArray(candData)) arr = candData;
        else arr = [];

        // Fetch shortlisted profiles for employer
        const emp_id = job?.emp_id || Cookies.get("employerId");
        let shortListedIds = [];
        if (emp_id) {
          try {
            const res = await fetch(
              `${import.meta.env.VITE_APP_HOST}/api/v1/shortlisted-profiles/${emp_id}`
            );
            const data = await res.json();
            if (data.data && data.data.length) {
              shortListedIds = data.data.map((e) => e.video_profile_id);
            }
          } catch (err) {
            // ignore
          }
        }

        // Filter out already shortlisted candidates
        arr = arr.filter(
          (cand) =>
            !shortListedIds.includes(
              cand.video_profile_id || cand.id || cand._id
            )
        );

        setCandidates(arr);
      } catch (err) {
        setCandidates([]);
      } finally {
        setLoading(false);
      }
    };
    if (job) fetchCandidates();
  }, [job]);

  if (loading) return <div>Loading...</div>;
  if (!job) return <div>Job description not found.</div>;

  return (
    <div className="p-6">
      <div className="mb-8 rounded-2xl bg-white border border-gray-200 p-6 shadow-sm dark:bg-navy-900 dark:border-navy-700">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
              {job.role}
            </h1>
            <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
              {Array.isArray(job.location) ? job.location.join(", ") : job.location}
              {job.experience && (
                <>
                  <span>•</span>
                  <span>{job.experience}</span>
                </>
              )}
              {job.companyType && (
                <>
                  <span>•</span>
                  <span>{job.companyType.charAt(0).toUpperCase() + job.companyType.slice(1).replace("_", " ")}</span>
                </>
              )}
            </div>
          </div>
          <button
            onClick={() => navigate('/employer/dashboard')}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors duration-200 text-sm font-medium"
          >
            ← Back to Dashboard
          </button>
        </div>
        
        <div>
          <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            Required Skills
          </h3>
          <div className="flex flex-wrap gap-2">
            {(Array.isArray(job.skills) ? job.skills : [job.skills]).map((skill, idx) => (
              <span
                key={idx}
                className="inline-flex items-center rounded-lg bg-blue-50 border border-blue-200 px-3 py-2 text-sm font-medium text-blue-700 dark:bg-blue-900/20 dark:border-blue-800 dark:text-blue-400"
              >
                {skill}
              </span>
            ))}
          </div>
        </div>
      </div>
      {Array.isArray(candidates) && candidates.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-16 px-6">
          <div className="relative mb-8">
            <div className="w-24 h-24 rounded-full bg-gradient-to-br from-blue-50 to-blue-100 border-2 border-blue-200 flex items-center justify-center shadow-lg">
              <HiOutlineUserGroup className="w-12 h-12 text-blue-500" />
            </div>
            <div className="absolute -top-2 -right-2 w-8 h-8 rounded-full bg-yellow-400 flex items-center justify-center shadow-md">
              <HiOutlineSparkles className="w-4 h-4 text-yellow-800" />
            </div>
          </div>
          
          <div className="text-center max-w-md space-y-4">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
              No Candidates Yet
            </h3>
            <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
              We haven't found any matching candidates for <span className="font-semibold text-blue-600">{job.role}</span> yet. 
              This could be because candidates haven't discovered this opportunity or need more time to apply.
            </p>
            
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6 mt-6">
              <div className="flex items-start gap-3">
                <HiOutlineShare className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                <div className="text-left">
                  <h4 className="font-semibold text-gray-900 mb-2">Increase your reach</h4>
                  <p className="text-sm text-gray-700 mb-4">
                    Share your job posting to attract more qualified candidates and build a stronger talent pipeline.
                  </p>
                  <button
                    onClick={() => navigate('/employer/dashboard')}
                    className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  >
                    <HiOutlineShare className="w-4 h-4" />
                    Share Job Posting
                  </button>
                </div>
              </div>
            </div>
            
            <div className="pt-4">
              <button
                onClick={() => navigate('/employer/dashboard')}
                className="text-blue-600 hover:text-blue-700 font-medium text-sm transition-colors duration-200"
              >
                ← Back to Dashboard
              </button>
            </div>
          </div>
        </div>
      ) : (
        <div className="grid gap-4 md:grid-cols-2">
          {Array.isArray(candidates) &&
            candidates.map((cand) => {
              // Parse candidateDetails
              const details = Array.isArray(cand.candidateDetails)
                ? cand.candidateDetails[0]
                : {};
              // Parse salary
              let salary = {};
              try {
                salary = cand.salary ? JSON.parse(cand.salary) : {};
              } catch {}
              return (
                <ProfileCard
                  key={cand.id || cand._id}
                  {...cand}
                  candidateDetails={
                    Array.isArray(cand.candidateDetails)
                      ? cand.candidateDetails
                      : [cand.candidateDetails]
                  }
                  score={cand.score}
                  openTooltipId={openTooltipId}
                  setOpenTooltipId={setOpenTooltipId}
                  onShortlist={() => handleShortlist(cand.video_profile_id || cand.id || cand._id)}
                  isShortlisted={selectedProfiles.has(cand.video_profile_id || cand.id || cand._id)}
                  isLoading={loadingId === (cand.video_profile_id || cand.id || cand._id)}
                />
              );
            })}
        </div>
      )}
    </div>
  );
};

export default JobDescriptionCandidates;
