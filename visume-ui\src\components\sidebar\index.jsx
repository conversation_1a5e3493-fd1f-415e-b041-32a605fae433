import React, { useEffect, useState } from 'react';
/* eslint-disable */

import { HiCube, HiMenu, HiX } from "react-icons/hi";
import { IoMdNotificationsOutline } from "react-icons/io";
import Links from "./components/Links";
import SidebarCard from "./components/SidebarCard";
import Cookies from "js-cookie";
import { useNavigate } from "react-router-dom";
import avatar from "assets/img/avatars/avatar4.png";

// import SidebarCard from "components/sidebar/componentsrtl/SidebarCard";
import routes from "routes";
import { ArrowLeftToLine, Code, PanelLeftClose, X, Bell, User, Settings, LogOut } from "lucide-react";
import LogoImage from 'assets/img/Visume-logo-icon.png';

const Sidebar = ({ open, onClose }) => {
  const [empData, setEmpData] = useState({
    name: Cookies.get("role") || "User",
    profileImg: avatar,
    email: Cookies.get("email") || ""
  });
  const navigate = useNavigate();

  useEffect(() => {
    const role = Cookies.get("role");
    const empId = Cookies.get("employerId");
    
    const fetchEmployerInfo = async () => {
      try {
        const response = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/employerProfilesData`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              Authorization: empId,
            },
          }
        );
        if (response.ok) {
          const data = await response.json();
          console.log("Employer data received:", data.data); // Debug log
          console.log("Available email fields:", {
            email: data.data?.email,
            emp_email: data.data?.emp_email,
            empEmail: data.data?.empEmail,
            userEmail: data.data?.userEmail,
            emailAddress: data.data?.emailAddress
          });
          setEmpData({
            name: data.data?.emp_name || data.data?.name || "Employer",
            email: data.data?.email || data.data?.emp_email || data.data?.empEmail || data.data?.userEmail || data.data?.emailAddress || Cookies.get("email") || "",
            profileImg: data.data?.company_logo
              ? (data.data.company_logo.startsWith("http")
                  ? data.data.company_logo
                  : `${import.meta.env.VITE_APP_HOST}/${data.data.company_logo}`)
              : data.data?.profile_picture
              ? (data.data.profile_picture.startsWith("http")
                  ? data.data.profile_picture
                  : `${import.meta.env.VITE_APP_HOST}/${data.data.profile_picture}`)
              : avatar,
          });
        }
      } catch (error) {
        console.error("Error fetching employer data:", error);
      }
    };

    if (role === "employer" && empId) {
      fetchEmployerInfo();
    }
  }, []);

  const handleSignOut = () => {
    // Get current role before removing cookies
    const currentRole = Cookies.get("role");
    
    Cookies.remove("jstoken");
    Cookies.remove("role");
    Cookies.remove("candId");
    Cookies.remove("employerId");
    Cookies.remove("formData");
    Cookies.remove("questions");
    Cookies.remove("videoProfileId");
    Cookies.remove("skills");
    Cookies.remove("jobRole");
    
    // Redirect based on current role
    if (currentRole === "employer") {
      navigate("/employer/sign-in");
    } else {
      navigate("/candidate/sign-in");
    }
  };

  // Click-outside-to-close logic
  React.useEffect(() => {
    if (!open) return;
    function handleClickOutside(event) {
      const sidebar = document.getElementById("visume-sidebar");
      if (
        sidebar &&
        !sidebar.contains(event.target) &&
        window.innerWidth <= 768
      ) {
        onClose();
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [open, onClose]);

  return (
    <>
      {/* Google Fonts Import */}
      <link href="https://fonts.googleapis.com/css2?family=Sora:wght@600&display=swap" rel="stylesheet" />
      {/* Mobile Backdrop */}
      {open && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 xl:hidden"
          onClick={onClose}
        />
      )}
      
      {/* Sidebar */}
      <div
        id="visume-sidebar"
        className={`fixed top-0 left-0 h-screen w-64 z-50 flex flex-col transition-transform duration-300 ease-in-out bg-white dark:bg-gray-900 shadow-lg ${
          open ? "translate-x-0" : "-translate-x-full"
        } xl:translate-x-0`}
      >
        {/* Sidebar Content */}
        <div className="relative h-full w-full flex flex-col">
        {/* Close button for mobile */}
        <button
          className="absolute top-4 right-4 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors xl:hidden"
          onClick={onClose}
        >
          <X className="w-4 h-4 text-gray-600 dark:text-gray-400" />
        </button>

        {/* Logo Section */}
        <div className="flex items-center gap-3 px-6 py-6">
          <img src="/visume-logo-new.png" alt="Visume Logo" className="h-8 w-8 object-contain" />
          <span className="text-xl font-semibold text-gray-900 dark:text-white tracking-tight" style={{ fontFamily: "Sora, sans-serif" }}>
            Visume
          </span>
        </div>

        {/* Navigation */}
        <nav className="flex-1 px-4 py-2 space-y-1">
          <Links routes={routes.filter(r => r.sidebar !== false)} onClose={onClose}/>
        </nav>

        {/* Profile Section */}
        <div className="px-4 py-4 border-t border-gray-200/60 dark:border-gray-700/60">
          <div className="bg-blue-50/50 dark:bg-gray-800 rounded-lg p-3 shadow-sm border border-blue-100 dark:border-gray-700">
            <div 
              className="flex items-center space-x-3 cursor-pointer hover:bg-blue-50/80 dark:hover:bg-gray-800/30 rounded-lg p-1 transition-all duration-200 w-full"
              onClick={() => navigate('/employer/settings')}
            >
              <div className="relative flex-shrink-0">
                <img
                  className="h-10 w-10 rounded-full object-cover border-2 border-gray-200 dark:border-gray-600 shadow-sm"
                  src={empData.profileImg}
                  alt={empData.name}
                  onError={(e) => {
                    if (e.target.src !== avatar) {
                      e.target.onerror = null;
                      e.target.src = avatar;
                    }
                  }}
                />
                <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 rounded-full border-2 border-white dark:border-gray-800"></div>
              </div>
              <div className="flex-1 min-w-0 overflow-hidden">
                <h3 className="text-sm font-semibold text-gray-900 dark:text-white" style={{ fontFamily: 'Sora, sans-serif' }}>
                  {empData.name}
                </h3>
                {empData.email && (
                  <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                    {empData.email}
                  </p>
                )}
              </div>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleSignOut();
                }}
                className="p-1.5 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-all duration-200 flex-shrink-0"
                title="Sign out"
              >
                <LogOut className="w-4 h-4 text-gray-400 hover:text-red-500 dark:text-gray-500 dark:hover:text-red-400 transition-colors duration-200 stroke-2" />
              </button>
            </div>
          </div>
        </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
