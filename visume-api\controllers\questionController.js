const { generateSingleQuestion } = require("../utils/helpers");
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

exports.generateNextQuestion = async (req, res) => {
  try {
    const { role, skills, previousQuestions, companyType, experience, videoProfileId, completeResumeData } = req.body;

    // Validate required fields
    if (!role) {
      return res.status(400).json({
        success: false,
        message: "Role is required for question generation"
      });
    }

    // Parse and validate previous questions array
    const previousQA = (previousQuestions || []).map((q) => ({
      question: q.question,
      answer: q.answer || null,
      type: q.type,
      question_number: q.question_number,
      total_questions: q.total_questions
    }));

    const questionCount = (previousQuestions || []).length + 1;
    const isFirstQuestion = !previousQuestions || previousQuestions.length === 0;

    // Fetch dynamic prompt from database if videoProfileId is provided
    let dynamicPrompt = null;
    if (videoProfileId) {
      try {
        const videoProfile = await prisma.videoprofile.findFirst({
          where: { video_profile_id: parseInt(videoProfileId) },
          select: { dynamic_prompt: true }
        });
        dynamicPrompt = videoProfile?.dynamic_prompt || null;
        console.log('Retrieved dynamic prompt for question generation:', dynamicPrompt ? dynamicPrompt.substring(0, 100) + '...' : 'None found');
      } catch (dbError) {
        console.error('Error fetching dynamic prompt:', dbError);
        // Continue without dynamic prompt - fallback to regular context
      }
    }

    // Streamlined single AI call - let AI handle all logic including termination
    const nextQuestion = await generateSingleQuestion(
      role,
      previousQA,
      skills,
      isFirstQuestion,
      companyType,
      experience,
      null, // Let AI decide type
      questionCount,
      null, // No resume data context
      null, // No complete resume data - all context via dynamic prompt only
      dynamicPrompt // Pass dynamic prompt instructions (contains all resume context)
    );

    // Check if AI determined interview should be terminated
    if (nextQuestion.type === 'ai_interview_stop') {
      return res.status(200).json({
        success: false,
        message: "Interview completed by AI assessment",
        completed: true,
        aiTerminated: true,
        totalQuestionsCompleted: questionCount - 1,
        terminationReason: "AI determined sufficient information gathered"
      });
    }

    // Validate and ensure timerDuration is within acceptable range (30-60 seconds for new optimized questions)
    const timerDuration = Math.min(60, Math.max(30, parseInt(nextQuestion.timerDuration) || 45));

    const response = {
      success: true,
      question: nextQuestion.question,
      type: nextQuestion.type,
      timerDuration: timerDuration,
      question_number: questionCount,
      total_questions: 8, // AI will handle termination dynamically
      completed: false,
      aiTerminated: false,
      _fallback: nextQuestion._fallback || false
    };

    res.status(200).json(response);

  } catch (error) {
    console.error("Question generation error:", error);
    
    const status = error.message === "Interview completed" ? 200 : 500;
    res.status(status).json({
      success: false,
      message: error.message || "Failed to generate question",
      completed: error.message === "Interview completed",
      error_details: error.message
    });
  }
};
