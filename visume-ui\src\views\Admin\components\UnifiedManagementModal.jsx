import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";
import { CreditCard } from "lucide-react";

/**
 * Unified Management Modal for handling both candidate Visume limits and employer credits
 */
const UnifiedManagementModal = ({
  isOpen,
  onClose,
  onSave,
  onTopUp, // New prop for top-up functionality
  selectedEntity,
  entityType, // "candidate" or "employer"
  value,
  setValue,
  isUpdating,
  StatusBadge, // Pass StatusBadge component as prop
  mode = "replace", // "replace" or "topup" - determines which action to show
}) => {
  // Determine modal configuration based on entity type
  const config = {
    candidate: {
      replace: {
        title: `Set Visume Limit for ${selectedEntity?.cand_name || selectedEntity?.name || ""}`,
        fieldLabel: "Visume Limit",
        fieldPlaceholder: "Enter visume limit (minimum 1)",
        helpText: [
          "• Minimum visume limit: 1 Visume (usage history will be reset)",
          "• Set the maximum number of Visumes this candidate can create",
          "• Usage counter will be reset to zero when limit is updated",
        ],
        quickOptions: [
          { value: "1", label: "1 Visume", color: "blue" },
          { value: "3", label: "3 Visumes", color: "green" },
          { value: "5", label: "5 Visumes", color: "purple" },
          { value: "10", label: "10 Visumes", color: "orange" },
        ],
        saveButtonText: isUpdating ? "Setting Limit..." : "Set Visume Limit",
        minValue: 1,
        validationMessage: "Visume limit must be at least 1 Visume",
        warningTitle: "Usage History Will Be Reset",
        warningText: "Setting a new Visume limit will reset the candidate's usage history to zero and provide the full limit amount.",
      },
      topup: {
        title: `Top Up Visume Limit for ${selectedEntity?.cand_name || selectedEntity?.name || ""}`,
        fieldLabel: "Visumes to Add",
        fieldPlaceholder: "Enter visumes to add (minimum 1)",
        helpText: [
          "• Minimum top-up: 1 Visume (usage history will NOT be reset)",
          "• Visumes will be added to current limit without affecting usage",
          `• Current: ${selectedEntity?.visumesUsed || 0} used / ${selectedEntity?.allowedVisumes || selectedEntity?.visumeLimit || 1} total`,
        ],
        quickOptions: [
          { value: "1", label: "+1 Visume", color: "blue" },
          { value: "2", label: "+2 Visumes", color: "green" },
          { value: "5", label: "+5 Visumes", color: "purple" },
        ],
        saveButtonText: isUpdating ? "Adding Visumes..." : "Top Up Visumes",
        minValue: 1,
        validationMessage: "Top-up amount must be at least 1 Visume",
        warningTitle: "Visumes Will Be Added",
        warningText: "Visumes will be added to the current limit. Usage history will remain unchanged.",
      },
    },
    employer: {
      replace: {
        title: `Set Credit Limit for ${selectedEntity?.name || ""}`,
        fieldLabel: "Custom Credit Limit",
        fieldPlaceholder: "Enter credit limit (minimum 10)",
        helpText: [
          "• Minimum credit limit: 10 credits (usage history will be reset)",
          "• Limit = 10: Free Employer Plan | Limit > 10: Custom Plan",
        ],
        quickOptions: [
          { value: "15", label: "15 Credits", color: "blue" },
          { value: "20", label: "20 Credits", color: "green" },
          { value: "25", label: "25 Credits", color: "purple" },
        ],
        saveButtonText: isUpdating ? "Setting Credits..." : "Set Credit Limit",
        minValue: 10,
        validationMessage: "Credit limit must be at least 10 credits",
        warningTitle: "Usage History Will Be Reset",
        warningText: "Setting a new credit limit will reset the employer's usage history to zero and provide the full credit amount.",
      },
      topup: {
        title: `Top Up Credits for ${selectedEntity?.name || ""}`,
        fieldLabel: "Credits to Add",
        fieldPlaceholder: "Enter credits to add (minimum 1)",
        helpText: [
          "• Minimum top-up: 1 credit (usage history will NOT be reset)",
          "• Credits will be added to current limit without affecting usage",
          `• Current: ${selectedEntity?.creditsLeft || 0} available / ${selectedEntity?.totalCredits || selectedEntity?.creditLimit || 0} total`,
        ],
        quickOptions: [
          { value: "5", label: "+5 Credits", color: "blue" },
          { value: "10", label: "+10 Credits", color: "green" },
          { value: "20", label: "+20 Credits", color: "purple" },
        ],
        saveButtonText: isUpdating ? "Adding Credits..." : "Top Up Credits",
        minValue: 1,
        validationMessage: "Top-up amount must be at least 1 credit",
        warningTitle: "Credits Will Be Added",
        warningText: "Credits will be added to the current limit. Usage history will remain unchanged.",
      },
    },
  };

  // Animation state management
  const [isVisible, setIsVisible] = useState(false);
  const [shouldRender, setShouldRender] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setShouldRender(true);
      // Small delay to ensure the DOM is updated before starting the animation
      setTimeout(() => setIsVisible(true), 10);
    } else {
      setIsVisible(false);
      // Wait for the exit animation to complete before unmounting
      const timer = setTimeout(() => setShouldRender(false), 300);
      return () => clearTimeout(timer);
    }
  }, [isOpen]);

  // Get the appropriate configuration based on entity type and mode
  const currentConfig = config[entityType][mode];

  if (!shouldRender || !selectedEntity) return null;

  return (
    <div 
      className={`fixed inset-0 flex items-center justify-center z-50 transition-all duration-300 ${
        isVisible ? 'bg-black bg-opacity-50 backdrop-blur-sm' : 'bg-black bg-opacity-0 backdrop-blur-none'
      }`}
      style={{
        // Fallback for browsers that don't support backdrop-filter
        WebkitBackdropFilter: isVisible ? 'blur(4px)' : 'none',
        backdropFilter: isVisible ? 'blur(4px)' : 'none'
      }}
      onClick={(e) => {
        // Close modal when clicking on the backdrop (not the modal content)
        if (e.target === e.currentTarget && !isUpdating) {
          onClose();
        }
      }}
    >
      <div 
        className={`bg-white dark:bg-gray-800 rounded-xl p-6 w-full max-w-md mx-4 shadow-2xl transform transition-all duration-300 ${
          isVisible ? 'scale-100 opacity-100 ring-1 ring-gray-200 dark:ring-gray-700' : 'scale-95 opacity-0'
        }`}
        style={{
          boxShadow: isVisible ? '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)' : 'none'
        }}
      >
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
          {currentConfig.title}
        </h3>

        {/* Usage Reset Warning */}
        <div className="mb-6 p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg">
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0">
              <svg className="w-5 h-5 text-amber-600 dark:text-amber-400 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div>
              <h4 className="text-sm font-medium text-amber-800 dark:text-amber-200 mb-1">
                {currentConfig.warningTitle}
              </h4>
              <p className="text-sm text-amber-700 dark:text-amber-300">
                {currentConfig.warningText}
              </p>
            </div>
          </div>
        </div>

        {/* Current Status - Only for employers */}
        {entityType === "employer" && selectedEntity && (
          <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Current Credit Limit:</span>
              <span className="text-lg font-bold text-gray-900 dark:text-white">{selectedEntity.totalCredits}</span>
            </div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Credits Remaining:</span>
              <span className="text-lg font-bold text-gray-900 dark:text-white">{selectedEntity.creditsLeft}</span>
            </div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Credits Used:</span>
              <span className="text-lg font-bold text-gray-900 dark:text-white">{(selectedEntity.totalCredits || 10) - (selectedEntity.creditsLeft || 0)}</span>
            </div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Current Plan:</span>
              <span className="text-sm font-semibold text-purple-600 dark:text-purple-400">{selectedEntity.planName}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Status:</span>
              {StatusBadge && <StatusBadge status={selectedEntity.membershipStatus} type="membership" />}
            </div>
          </div>
        )}

        {/* Current Status - Only for candidates */}
        {entityType === "candidate" && selectedEntity && (
          <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Current Visume Limit:</span>
              <span className="text-lg font-bold text-gray-900 dark:text-white">{selectedEntity.allowedVisumes}</span>
            </div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Visumes Created:</span>
              <span className="text-lg font-bold text-gray-900 dark:text-white">{selectedEntity.currentVisumeCount}</span>
            </div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Current Plan:</span>
              <span className="text-sm font-semibold text-purple-600 dark:text-purple-400">{selectedEntity.planName}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Status:</span>
              {StatusBadge && <StatusBadge status={selectedEntity.membershipStatus} type="membership" />}
            </div>
          </div>
        )}

        {/* Quick Options */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            Quick {entityType === "candidate" ? "Visume" : "Credit"} Options
          </label>
          <div className={`grid gap-3 mb-4 ${entityType === "employer" ? "grid-cols-3" : "grid-cols-4"}`}>
            {currentConfig.quickOptions.map((option) => (
              <button
                key={option.value}
                type="button"
                onClick={() => setValue(option.value)}
                className={`px-4 py-3 bg-gradient-to-r from-${option.color}-500 to-${option.color}-600 hover:from-${option.color}-600 hover:to-${option.color}-700 text-white rounded-lg font-medium transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg`}
                disabled={isUpdating}
              >
                {option.label}
              </button>
            ))}
          </div>
        </div>

        {/* Custom Input */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {currentConfig.fieldLabel}
          </label>
          <input
            type="number"
            value={value}
            onChange={(e) => setValue(e.target.value)}
            min={currentConfig.minValue}
            className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
            placeholder={currentConfig.fieldPlaceholder}
            disabled={isUpdating}
          />
          <p className="mt-2 text-xs text-gray-500 dark:text-gray-400">
            {currentConfig.helpText.map((text, index) => (
              <React.Fragment key={index}>
                {text}
                <br />
              </React.Fragment>
            ))}
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3">
          <button
            onClick={mode === 'topup' ? onTopUp : onSave}
            disabled={isUpdating}
            className={`flex-1 ${
              mode === 'topup'
                ? 'bg-green-600 hover:bg-green-700 disabled:bg-green-300'
                : 'bg-blue-600 hover:bg-blue-700 disabled:bg-blue-300'
            } text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center justify-center gap-2`}
          >
            {isUpdating ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                {mode === 'topup' ? 'Adding...' : 'Updating...'}
              </div>
            ) : (
              <>
                {entityType === "employer" && <CreditCard className="w-4 h-4" />}
                {currentConfig.saveButtonText}
              </>
            )}
          </button>
          <button
            onClick={() => !isUpdating && onClose()}
            disabled={isUpdating}
            className="flex-1 bg-gray-100 hover:bg-gray-200 disabled:bg-gray-50 text-gray-700 px-4 py-2 rounded-lg font-medium transition-colors dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-300"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  );
};

UnifiedManagementModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onSave: PropTypes.func.isRequired,
  onTopUp: PropTypes.func, // Function for top-up action
  selectedEntity: PropTypes.object,
  entityType: PropTypes.oneOf(["candidate", "employer"]).isRequired,
  value: PropTypes.string.isRequired,
  setValue: PropTypes.func.isRequired,
  isUpdating: PropTypes.bool,
  StatusBadge: PropTypes.elementType, // Component for rendering status badges
  mode: PropTypes.oneOf(["replace", "topup"]), // Mode for the modal
};

UnifiedManagementModal.defaultProps = {
  isUpdating: false,
  StatusBadge: null,
  mode: "replace",
  onTopUp: null,
};

export default UnifiedManagementModal;