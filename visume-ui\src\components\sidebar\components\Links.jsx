/* eslint-disable */
import React from "react";
import { Link, useLocation } from "react-router-dom";
import DashIcon from "components/icons/DashIcon";

export function SidebarLinks(props) {
  let location = useLocation();
  const layout = `/${location.pathname.split("/")[1]}`;
  let { routes, onClose, open } = props;

  // Verifies if routeName is the one active
  const activeRoute = (routeName) => {
    return location.pathname.includes(routeName);
  };

  const createLinks = (routes) => {
    return routes
      .filter((route) => route.layout === layout && !route.special && route.name)
      .map((route, index) => {
        const linkPath =
          layout === "/employer" && route.path === "profile-search"
            ? "/profile-search"
            : `${route.layout}/${route.path}`;

        const isActive = activeRoute(route.path);

        return (
          <Link
            key={index}
            to={linkPath}
            onClick={() => {
              // Only hide sidebar on smaller screens
              if (window.innerWidth < 768 && open) onClose();
            }}
            className="block"
          >
            <div className={`
              group relative flex items-center px-4 py-3 rounded-lg text-sm font-medium transition-all duration-200 ease-in-out
              ${isActive
                ? "bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 shadow-sm"
                : "text-gray-700 dark:text-gray-300 hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-900/20 dark:hover:text-blue-300 hover:shadow-sm"
              }
            `}>
              {/* Icon */}
              <div className={`
                flex items-center justify-center w-5 h-5 mr-3 transition-colors duration-200
                ${isActive
                  ? "text-blue-700 dark:text-blue-300"
                  : "text-gray-500 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400"
                }
              `}>
                {route.icon ? route.icon : <DashIcon />}
              </div>

              {/* Text */}
              <span className={`
                flex-1 truncate transition-colors duration-200
                ${isActive
                  ? "text-blue-700 dark:text-blue-300 font-semibold"
                  : "text-gray-700 dark:text-gray-300 group-hover:text-blue-700 dark:group-hover:text-blue-300"
                }
              `}>
                {route.name}
              </span>

              {/* Notification badge for specific routes (like Chat) */}
              {route.name === "Chat" && (
                <div className="flex items-center justify-center w-5 h-5 bg-blue-600 text-white text-xs font-bold rounded-full ml-2">
                  9
                </div>
              )}

              {/* Hover effect overlay */}
              <div className={`
                absolute inset-0 rounded-lg opacity-0 transition-opacity duration-200
                ${!isActive ? "group-hover:opacity-100 bg-gradient-to-r from-blue-50/30 via-blue-50/50 to-blue-50/30 dark:from-blue-900/10 dark:via-blue-900/20 dark:to-blue-900/10" : ""}
              `} />

            </div>
          </Link>
        );
      });
  };

  return (
    <div className="space-y-1">
      {createLinks(routes)}
    </div>
  );
}

export default SidebarLinks;
