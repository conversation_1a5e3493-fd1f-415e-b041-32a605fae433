// ProfileCard.jsx
import React, { useState, useRef, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { HiBriefcase, HiHeart, HiLocationMarker, HiOutlineHeart, HiOutlineLocationMarker, HiChevronRight } from "react-icons/hi";
import { Video, Star, MapPin, ChevronRight } from "lucide-react";
import Cookies from "js-cookie";
import toast from "react-hot-toast";
import Tooltip from "../../../components/tooltip"; 

const ProfileCard = ({
  keyValue,
  experience_range,
  shortListedProfiles,
  score,
  video_profile_id,
  candidateDetails,
  role,
  id,
  onShortlist,
  isShortlisted,
  isLoading,
  cand_id,
  skills,
  isSkeleton,
  openTooltipId,
  setOpenTooltipId,
  salary,
  className
}) => {

  const skillsArray = skills ? skills.split(",").map((skill) => skill.trim()) : [];
  const [imageError, setImageError] = useState(false);
  const emp_id = Cookies.get("employerId");
  const [hasViewed, setHasViewed] = useState(false);
  const [hasClicked, setHasClicked] = useState(false);
  // const [isLocationTooltipOpen, setIsLocationTooltipOpen] = useState(false);
  const [openTooltipType, setOpenTooltipType] = useState(null); 
  const locationRef = useRef(null);
  const skillsRef = useRef(null);

  const navigate = useNavigate();

  const isLocationTooltipOpen = openTooltipId === video_profile_id && openTooltipType === 'location';
  const isSkillsTooltipOpen = openTooltipId === video_profile_id && openTooltipType === 'skills';

  const handleToggleTooltip = (e, type) => {
    e.stopPropagation();
    if (openTooltipId === video_profile_id && openTooltipType === type) {
      setOpenTooltipId(null);
      setOpenTooltipType(null);
    } else {
      setOpenTooltipId(video_profile_id);
      setOpenTooltipType(type);
    }
  };

  const profileClick = async () => {
    const currentUrl = window.location.href;
    localStorage.setItem("previousUrl", currentUrl);
    navigate(`/profile/${video_profile_id}`);
    if (!hasClicked) {
      try {
        if (!emp_id) {
          return toast.error("You need to be an employer to shortlist profiles");
        }
        const response = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/candidate/analytics`,
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              employer_id: emp_id,
              profile_id: id,
              interaction_type: "click",
            }),
          }
        );
        if (!response.ok) {
          const msg = await response.json();
          if (
            msg.message ===
            "Duplicate interaction: the same interaction type already exists for this employer and profile."
          ) {
            toast(msg.message);
            return;
          } else {
            toast.error(msg.message);
            throw new Error(`HTTP error! status: ${msg.message}`);
          }
        }
        await response.json();
      } catch (err) {
        console.log(err);
      } finally {
        setHasClicked(true);
      }
    }
  };

  return (
    <div
      key={keyValue}
      className={className || `flex flex-col rounded-3xl ${
        isSkeleton
          ? 'bg-gray-50 dark:bg-gray-700 animate-pulse'
          : 'bg-white dark:bg-gray-800'
      } border-2 border-blue-200 dark:border-blue-700 p-6 m-2 transition-all duration-200 hover:border-blue-300 dark:hover:border-blue-500 h-auto overflow-hidden w-[calc(25%-1rem)]`}
    >
      {/* Header: Profile Image and Name */}
      <div className="flex items-center gap-4 mb-6">
        {/* Large Profile Image */}
        <div
          className={`h-16 w-16 flex-shrink-0 ${!isSkeleton && 'cursor-pointer'} rounded-2xl ${isSkeleton ? 'bg-gray-300' : 'bg-blue-600'} overflow-hidden`}
          onClick={!isSkeleton ? profileClick : undefined}
        >
          {imageError || !candidateDetails[0].profile_picture || candidateDetails[0].profile_picture.trim() === "" ? (
            <div className="flex h-full w-full items-center justify-center rounded-2xl bg-blue-600 text-2xl font-bold text-white">
              {candidateDetails[0].cand_name[0].toUpperCase()}
            </div>
          ) : (
            <img
              className="h-full w-full rounded-2xl object-cover"
              src={`${import.meta.env.VITE_APP_HOST}/${candidateDetails[0].profile_picture}`}
              alt={candidateDetails[0].cand_name}
              onError={(e) => {
                setImageError(true);
              }}
            />
          )}
        </div>
        {/* Name and Role */}
        <div className="flex-1 min-w-0 overflow-hidden">
          <h2 className="text-xl font-bold text-gray-900 dark:text-white truncate mb-1">
            {candidateDetails[0].cand_name}
          </h2>
          <p className="text-sm text-blue-600 dark:text-blue-400 font-semibold truncate">{role}</p>
        </div>
      </div>
      {/* Visume Score */}
      {(() => {
        const scoreValue = Math.round(JSON.parse(score)?.score?.Overall_Score || 0);
        const getScoreColor = (score) => {
          if (score >= 8) return { bg: 'bg-green-200/30', stroke: '#10b981', text: 'text-green-600' };
          if (score >= 5) return { bg: 'bg-yellow-200/30', stroke: '#f59e0b', text: 'text-yellow-600' };
          return { bg: 'bg-red-200/30', stroke: '#ef4444', text: 'text-red-600' };
        };
        const colors = getScoreColor(scoreValue);
        return (
          <div className={`flex items-center justify-between py-3 px-4 ${colors.bg} dark:bg-opacity-20 rounded-2xl mb-6`}>
            <span className={`text-sm font-semibold ${colors.text}`}>Visume Score</span>
            <div className="flex items-center gap-3">
              <div className="relative w-12 h-12">
                {/* Background Circle */}
                <svg className="w-12 h-12 transform -rotate-90" viewBox="0 0 48 48">
                  <circle
                    cx="24"
                    cy="24"
                    r="20"
                    stroke="#e5e7eb"
                    strokeWidth="4"
                    fill="none"
                  />
                  {/* Progress Circle */}
                  <circle
                    cx="24"
                    cy="24"
                    r="20"
                    stroke={colors.stroke}
                    strokeWidth="4"
                    fill="none"
                    strokeLinecap="round"
                    strokeDasharray={`${2 * Math.PI * 20}`}
                    strokeDashoffset={`${2 * Math.PI * 20 * (1 - (scoreValue / 10))}`}
                    className="transition-all duration-500 ease-out"
                  />
                </svg>
                {/* Score Text in Center */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-xs font-bold text-gray-900 dark:text-white">
                    {scoreValue}
                  </span>
                </div>
              </div>
            </div>
          </div>
        );
      })()}

      {/* Experience and Expected Salary */}
      <div className="space-y-4 mb-6 overflow-hidden">
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-500 dark:text-gray-400 font-medium flex-shrink-0">Experience</span>
          <span className="text-sm font-bold text-gray-900 dark:text-white truncate ml-2">{experience_range}</span>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-500 dark:text-gray-400 font-medium flex-shrink-0">Expected Salary</span>
          <span className="text-sm font-bold text-gray-900 dark:text-white truncate ml-2">
            {(() => {
              let salaryObj = {};
              try {
                salaryObj = typeof salary === "string" ? JSON.parse(salary) : salary;
              } catch {}
              if (salaryObj?.expected && salaryObj.expected !== "0") {
                return `₹${salaryObj.expected} LPA`;
              }
              if (salaryObj?.current && salaryObj.current !== "0") {
                return `₹${salaryObj.current} LPA`;
              }
              return "₹12-14 LPA";
            })()}
          </span>
        </div>
      </div>

      {/* Skills and Location Links */}
      <div className="flex items-center justify-between mb-6 overflow-hidden">
        <div className="relative flex-shrink-0">
          <button
            ref={skillsRef}
            onClick={e => handleToggleTooltip(e, 'skills')}
            className="flex items-center gap-1 text-blue-600 dark:text-blue-400 font-semibold hover:text-blue-700 dark:hover:text-blue-300 transition-colors"
            type="button"
          >
            <span>Skills</span>
            <ChevronRight className="w-4 h-4" />
          </button>
          <Tooltip
            content={skillsArray}
            isOpen={isSkillsTooltipOpen}
            onClose={() => { setOpenTooltipId(null); setOpenTooltipType(null); }}
            targetRef={skillsRef}
            title="Skills"
          />
        </div>
        <div className="relative flex items-center gap-1 flex-shrink-0">
          <MapPin className="w-4 h-4 text-blue-600 dark:text-blue-400" />
          <button
            ref={locationRef}
            onClick={e => handleToggleTooltip(e, 'location')}
            className="flex items-center gap-1 text-blue-600 dark:text-blue-400 font-semibold hover:text-blue-700 dark:hover:text-blue-300 transition-colors"
            type="button"
          >
            <span>Location</span>
            <ChevronRight className="w-4 h-4" />
          </button>
          <Tooltip
            content={(
              candidateDetails[0].preferred_location.startsWith("[")
                ? JSON.parse(candidateDetails[0].preferred_location)
                : [candidateDetails[0].preferred_location]
            )}
            isOpen={isLocationTooltipOpen}
            onClose={() => { setOpenTooltipId(null); setOpenTooltipType(null); }}
            targetRef={locationRef}
            title="Preferred Locations"
          />
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex gap-2 mt-auto">
        <button
          onClick={!isSkeleton ? profileClick : undefined}
          className={`flex-1 flex items-center justify-center gap-2 rounded-lg px-4 py-2.5 text-sm font-semibold transition-all duration-200 ${
            isSkeleton
              ? 'bg-gray-100 dark:bg-gray-600 text-gray-400 dark:text-gray-500 cursor-default'
              : 'bg-blue-600 dark:bg-blue-700 text-white hover:bg-blue-700 dark:hover:bg-blue-800'
          }`}
          disabled={isSkeleton}
        >
          <Video className="w-4 h-4 flex-shrink-0" />
          <span className="truncate">Profile</span>
        </button>
        <button
          className={`flex-1 flex items-center justify-center gap-2 rounded-lg border-2 px-4 py-2.5 text-sm font-semibold transition-all duration-200 ${
            isShortlisted || shortListedProfiles?.includes(video_profile_id)
              ? "border-blue-600 dark:border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400"
              : "border-blue-600 dark:border-blue-500 bg-transparent text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20"
          }`}
          onClick={() =>
            !isShortlisted &&
            !shortListedProfiles?.includes(video_profile_id) &&
            onShortlist(id, cand_id)
          }
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <svg
                className="text-current h-4 w-4 animate-spin flex-shrink-0"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                  fill="none"
                />
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                />
              </svg>
              <span className="truncate">Loading</span>
            </>
          ) : isShortlisted ||
            shortListedProfiles?.includes(video_profile_id) ? (
            <>
              <Star className="w-4 h-4 flex-shrink-0 fill-current" />
              <span className="truncate">Shortlist</span>
            </>
          ) : (
            <>
              <Star className="w-4 h-4 flex-shrink-0" />
              <span className="truncate">Shortlist</span>
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default ProfileCard;