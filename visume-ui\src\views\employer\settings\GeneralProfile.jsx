import { useEffect, useState } from "react";
import { Upload, Save, X, Check } from "lucide-react";
import { HiAdjustments } from "react-icons/hi";
import { RiMoonFill, RiSunFill } from "react-icons/ri";
import { useNavigate } from "react-router-dom";
import Cookies from "js-cookie";
import toast from "react-hot-toast";

const GeneralProfile = () => {
  const navigate = useNavigate();
  const emp_id = Cookies.get("employerId");

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [editActive, setEditActive] = useState(false);
  const [empName, setEmpName] = useState("");
  const [empEmail, setEmpEmail] = useState("");
  const [empMobile, setEmpMobile] = useState("");
  const [designation, setDesignation] = useState("");
  const [companyName, setCompanyName] = useState(""); // Keep for display if exists
  const [companyUrl, setCompanyUrl] = useState("");
  const [companyLogo, setCompanyLogo] = useState(null);
  const [companyLogoPreview, setCompanyLogoPreview] = useState(null);
  const [profilePicture, setProfilePicture] = useState(null);
  const [twitterHandle, setTwitterHandle] = useState("");
  const [facebookHandle, setFacebookHandle] = useState("");
  const [linkedinHandle, setLinkedinHandle] = useState("");
  const [includeInReports, setIncludeInReports] = useState(true);
  const [includeInEmails, setIncludeInEmails] = useState(true);
  const [darkMode, setDarkMode] = useState(false);

  // Store original values for cancel functionality
  const [originalValues, setOriginalValues] = useState({
    empName: "",
    empEmail: "",
    empMobile: "",
    designation: "",
    companyName: "",
    companyUrl: "",
    companyLogo: null
  });

  // Handle file upload for company logo
  const handleLogoUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Validate file type and size
      const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/svg+xml'];
      const maxSize = 2 * 1024 * 1024; // 2MB

      if (!validTypes.includes(file.type)) {
        toast.error("Please upload a valid image file (JPG, PNG, GIF, SVG)");
        return;
      }

      if (file.size > maxSize) {
        toast.error("File size must be less than 2MB");
        return;
      }

      setCompanyLogo(file);
      const previewUrl = URL.createObjectURL(file);
      setCompanyLogoPreview(previewUrl);
      toast.success("Logo selected successfully");
    }
  };

  // Save profile changes
  const handleSave = async () => {
    if (!empName.trim()) {
      toast.error("Full name is required");
      return;
    }

    if (!empMobile.trim()) {
      toast.error("Mobile number is required");
      return;
    }

    if (!designation.trim()) {
      toast.error("Designation is required");
      return;
    }

    setSaving(true);
    try {
      const formData = new FormData();
      formData.append("emp_name", empName.trim());
      formData.append("emp_mobile", empMobile.trim());
      formData.append("designation", designation.trim());
      formData.append("company_name", companyName.trim());

      if (companyUrl && companyUrl.trim()) {
        formData.append("company_website", companyUrl.trim());
      }

      if (companyLogo) {
        formData.append("company_logo", companyLogo);
      }

      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/updateEmployerProfile`,
        {
          method: "PUT",
          headers: {
            Authorization: emp_id,
          },
          body: formData,
        }
      );

      const result = await response.json();

      if (response.ok) {
        toast.success("Profile updated successfully!");
        setEditActive(false);
        // Update original values
        setOriginalValues({
          empName,
          empEmail,
          empMobile,
          designation,
          companyName,
          companyUrl,
          companyLogo: companyLogoPreview
        });
        // Refresh profile data
        await getProfileData();

        // Synchronize profile picture with company logo
        if (companyLogo && companyLogoPreview) {
          setProfilePicture(companyLogoPreview);

          // Trigger a custom event to notify other components about the logo update
          window.dispatchEvent(new CustomEvent('companyLogoUpdated', {
            detail: {
              logoUrl: companyLogoPreview,
              timestamp: Date.now()
            }
          }));
        }
      } else {
        toast.error(result.message || "Failed to update profile");
      }
    } catch (error) {
      console.error("Error updating profile:", error);
      toast.error("Failed to update profile. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  // Cancel editing and revert changes
  const handleCancel = () => {
    setEmpName(originalValues.empName);
    setEmpEmail(originalValues.empEmail);
    setEmpMobile(originalValues.empMobile);
    setDesignation(originalValues.designation);
    setCompanyName(originalValues.companyName);
    setCompanyUrl(originalValues.companyUrl);
    setCompanyLogo(null);
    setCompanyLogoPreview(originalValues.companyLogo);
    setEditActive(false);
    toast.success("Changes cancelled");
  };

  // Start editing mode
  const handleEdit = () => {
    setOriginalValues({
      empName,
      empEmail,
      empMobile,
      designation,
      companyName,
      companyUrl,
      companyLogo: companyLogoPreview
    });
    setEditActive(true);
  };

  const getProfileData = async () => {
    setLoading(true);
    try {
      const profileReq = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/getEmployerDetails`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: emp_id,
          },
        }
      );
      const profileJson = await profileReq.json();

      const profileData = profileJson.data || {};
      console.log(profileData);
      if (profileData) {
        setEmpName(profileData.emp_name || "");
        setEmpEmail(profileData.emp_email || "");
        setEmpMobile(profileData.emp_mobile ? profileData.emp_mobile.toString() : "");
        setDesignation(profileData.designation || "");
        setCompanyName(profileData.company_name || "");

        // Set profile picture and company logo (synchronized)
        let imageUrl = null;

        if (profileData.company_logo) {
          imageUrl = profileData.company_logo.startsWith('http')
            ? profileData.company_logo
            : `${import.meta.env.VITE_APP_HOST}/${profileData.company_logo}`;
        } else if (profileData.profile_picture) {
          imageUrl = profileData.profile_picture.startsWith('http')
            ? profileData.profile_picture
            : `${import.meta.env.VITE_APP_HOST}/${profileData.profile_picture}`;
        }

        // Set both profile picture and company logo to the same image
        if (imageUrl) {
          setProfilePicture(imageUrl);
          setCompanyLogoPreview(imageUrl);
        }

        setTwitterHandle(profileData?.company_twitterHandle || "");
        setFacebookHandle(profileData?.company_facebookHandle || "");
        setLinkedinHandle(profileData?.company_linkedinHandle || "");

        // Set original values (use the same synchronized image)
        setOriginalValues({
          empName: profileData.emp_name || "",
          empEmail: profileData.emp_email || "",
          empMobile: profileData.emp_mobile ? profileData.emp_mobile.toString() : "",
          designation: profileData.designation || "",
          companyName: profileData.company_name || "",
          companyUrl: profileData.company_website || "",
          companyLogo: imageUrl
        });
      }
    } catch (err) {
      console.error("Error fetching profile data:", err);
      toast.error("Failed to load profile data. Please refresh the page.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (emp_id) {
      getProfileData();
    } else {
      navigate("/candidate/sign-in");
    }
    
    // Check current theme
    setDarkMode(document.body.classList.contains('dark'));
  }, []);

  const toggleDarkMode = () => {
    if (darkMode) {
      document.body.classList.remove("dark");
      setDarkMode(false);
      toast.success("Switched to light mode");
    } else {
      document.body.classList.add("dark");
      setDarkMode(true);
      toast.success("Switched to dark mode");
    }
  };

  return (
    <div>
      {/* Compact Section Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <HiAdjustments className="w-4 h-4 text-blue-600" />
          <h2 className="text-lg font-semibold text-slate-900 dark:text-white font-sora">
            Profile Information
          </h2>
        </div>
        <button
          onClick={() => editActive ? handleSave() : handleEdit()}
          disabled={saving}
          className={`flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-all font-manrope ${
            saving
              ? 'bg-slate-400 text-white cursor-not-allowed'
              : editActive
              ? 'bg-green-600 hover:bg-green-700 text-white'
              : 'bg-blue-600 hover:bg-blue-700 text-white'
          }`}
        >
          {saving ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              Saving...
            </>
          ) : editActive ? (
            <>
              <Check className="w-4 h-4" />
              Save Changes
            </>
          ) : (
            "Edit Profile"
          )}
        </button>
      </div>

      {/* Hidden file input for image upload */}
      <input
        id="logo-upload"
        type="file"
        accept="image/*"
        onChange={handleLogoUpload}
        className="hidden"
        disabled={!editActive}
      />
      {loading ? (
        <div className="flex items-center justify-center py-16">
          <div className="flex items-center space-x-3">
            <div className="h-6 w-6 animate-spin rounded-full border-2 border-blue-500 border-t-transparent"></div>
            <span className="text-sm text-gray-600 dark:text-gray-400">Loading profile...</span>
          </div>
        </div>
      ) : (
        <div>
          {/* Profile Image Section */}
          <div className="flex items-center gap-6 mb-6 pb-6 border-b border-slate-200 dark:border-slate-600">
            <div className="relative">
              {profilePicture ? (
                <img
                  src={profilePicture}
                  alt="Profile"
                  className="w-20 h-20 rounded-full object-cover border-4 border-white dark:border-slate-800 shadow-lg"
                />
              ) : (
                <div className="w-20 h-20 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center text-white shadow-lg border-4 border-white dark:border-slate-800">
                  <span className="text-lg font-bold">
                    {empName ? empName.slice(0, 2).toUpperCase() : ""}
                  </span>
                </div>
              )}
              {editActive && (
                <label className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded-full cursor-pointer opacity-0 hover:opacity-100 transition-opacity">
                  <Upload className="w-6 h-6 text-white" />
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleLogoUpload}
                    className="hidden"
                  />
                </label>
              )}
            </div>
            <div>
              <h3 className="text-lg font-semibold text-slate-900 dark:text-white font-sora">
                {empName || "Full Name"}
              </h3>
              <p className="text-sm text-slate-600 dark:text-slate-400 font-manrope mt-1">
                {designation || "Position"}
              </p>
              {editActive && (
                <p className="text-xs text-slate-500 dark:text-slate-500 font-manrope mt-2">
                  Click on image to change profile photo
                </p>
              )}
            </div>
          </div>

          {/* Personal Form */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-slate-900 dark:text-white font-manrope mb-2">
                Full Name
              </label>
              <input
                type="text"
                value={empName}
                onChange={(e) => setEmpName(e.target.value)}
                disabled={!editActive}
                className={`w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-900 dark:text-white font-manrope text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors ${
                  !editActive ? 'opacity-60 cursor-not-allowed' : ''
                }`}
                placeholder="Enter your full name"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-900 dark:text-white font-manrope mb-2">
                Email Address
              </label>
              <input
                type="email"
                value={empEmail}
                disabled={true}
                className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-slate-50 dark:bg-slate-800 text-slate-900 dark:text-white font-manrope text-sm opacity-60 cursor-not-allowed"
                placeholder="Email address"
              />
              <p className="mt-1 text-xs text-slate-500 font-manrope">Email cannot be changed</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-900 dark:text-white font-manrope mb-2">
                Mobile Number
              </label>
              <input
                type="tel"
                value={empMobile}
                onChange={(e) => setEmpMobile(e.target.value)}
                disabled={!editActive}
                className={`w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-900 dark:text-white font-manrope text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors ${
                  !editActive ? 'opacity-60 cursor-not-allowed' : ''
                }`}
                placeholder="Enter your mobile number"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-900 dark:text-white font-manrope mb-2">
                Designation
              </label>
              <input
                type="text"
                value={designation}
                onChange={(e) => setDesignation(e.target.value)}
                disabled={!editActive}
                className={`w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-900 dark:text-white font-manrope text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors ${
                  !editActive ? 'opacity-60 cursor-not-allowed' : ''
                }`}
                placeholder="Enter your designation"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-slate-900 dark:text-white font-manrope mb-2">
                Organization
              </label>
              <input
                type="text"
                value={companyName}
                onChange={(e) => setCompanyName(e.target.value)}
                disabled={!editActive}
                className={`w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-900 dark:text-white font-manrope text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors ${
                  !editActive ? 'opacity-60 cursor-not-allowed' : ''
                }`}
                placeholder="Enter your organization"
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GeneralProfile;