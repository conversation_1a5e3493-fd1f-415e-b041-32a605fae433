import { useState } from "react";
import { HiClipboard, HiShare, HiVideoCamera } from "react-icons/hi";
import { useLocation } from "react-router-dom";
import { Share2, Check, Gift } from "lucide-react";

const SidebarCard = () => {
  const [copied, setCopied] = useState(false);
  const location = useLocation();
  const layout = `/${location.pathname.split("/")[1]}`;

  const visumeLink = "https://visume.co.in/referral";

  const handleCopy = () => {
    navigator.clipboard.writeText(visumeLink)
      .then(() => {
        setCopied(true);
        setTimeout(() => {
          setCopied(false);
        }, 2000);
      })
      .catch(err => console.error('Failed to copy: ', err));
  };

  if (layout === "/admin") {
    return null;
  }

  return (
    <div className="w-full">
      <div className="relative bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-800 dark:to-gray-900 rounded-xl p-3 border border-gray-200 dark:border-gray-700 shadow-sm">
        {/* Header with Icon and Title */}
        <div className="flex items-center gap-3 mb-2">
          <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg shadow-sm">
            <Gift className="w-4 h-4 text-white" />
          </div>
          <div>
            <h3 className="text-sm font-semibold text-gray-900 dark:text-white">
              Refer & Earn
            </h3>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              Share and earn rewards
            </p>
          </div>
        </div>

        {/* Copy Button */}
        <button
          onClick={handleCopy}
          className={`
            w-full flex items-center justify-center gap-2 px-3 py-2 rounded-lg text-xs font-medium transition-all duration-200
            ${copied
              ? "bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-400 border border-green-200 dark:border-green-800"
              : "bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-300 dark:hover:border-gray-500"
            }
          `}
        >
          {copied ? (
            <>
              <Check className="w-3.5 h-3.5" />
              <span>Link copied!</span>
            </>
          ) : (
            <>
              <Share2 className="w-3.5 h-3.5" />
              <span>Copy link</span>
            </>
          )}
        </button>

        {/* Decorative elements */}
        <div className="absolute top-2 right-2 w-16 h-16 bg-gradient-to-br from-blue-500/10 to-indigo-600/10 rounded-full blur-xl" />
        <div className="absolute bottom-2 left-2 w-12 h-12 bg-gradient-to-br from-indigo-500/10 to-blue-600/10 rounded-full blur-lg" />
      </div>
    </div>
  );
};

export default SidebarCard;
