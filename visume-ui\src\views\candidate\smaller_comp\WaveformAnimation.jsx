import React from "react";

export default function WaveformAnimation({ amplitudeData = [] }) {
  // Use provided amplitude data or fallback to static animation
  const hasAmplitudeData = amplitudeData.length > 0;
  
  return (
    <div className="flex items-center justify-center gap-1">
      {[...Array(5)].map((_, i) => {
        const amplitude = hasAmplitudeData ? amplitudeData[i] || 0.05 : 0.3;
        // Scale height more dramatically for center bars (index 2 has highest potential)
        const heightMultiplier = i === 2 ? 32 : i === 1 || i === 3 ? 28 : 20;
        const height = Math.max(amplitude * heightMultiplier, 3); // Minimum height for visibility
        
        return (
          <div
            key={i}
            className="bg-red-500 w-1 rounded-full transition-all duration-75 ease-linear"
            style={{
              height: hasAmplitudeData ? `${height}px` : "20px",
              animation: hasAmplitudeData ? "none" : `waveform-${i} 0.8s ease-in-out infinite alternate`,
              animationDelay: hasAmplitudeData ? "0s" : `${i * 0.1}s`,
              transformOrigin: "bottom",
            }}
          />
        );
      })}
      {!hasAmplitudeData && (
        <style jsx>{`
          @keyframes waveform-0 {
            0%, 100% { transform: scaleY(0.3); }
            50% { transform: scaleY(0.8); }
          }
          @keyframes waveform-1 {
            0%, 100% { transform: scaleY(0.5); }
            50% { transform: scaleY(1.2); }
          }
          @keyframes waveform-2 {
            0%, 100% { transform: scaleY(0.8); }
            50% { transform: scaleY(2); }
          }
          @keyframes waveform-3 {
            0%, 100% { transform: scaleY(0.5); }
            50% { transform: scaleY(1.2); }
          }
          @keyframes waveform-4 {
            0%, 100% { transform: scaleY(0.3); }
            50% { transform: scaleY(0.8); }
          }
        `}</style>
      )}
    </div>
  );
}