import React, { useState, useEffect } from "react";
import Editor from "@monaco-editor/react";
import {
  Clock,
  Mic,
  ArrowRight,
  LogOut,
  Code,
  MessageSquare,
  Loader2,
  Play,
} from "lucide-react";
import WaveformAnimation from "./WaveformAnimation";
import { useAudioAnalyzer } from "../../../hooks/useAudioAnalyzer";

export default function AnswerInput({
  question,
  isListening,
  handleSpeechRecognition,
  handleNextQuestion,
  onEndInterview,
  currentIndex,
  isSpeaking,
  remainingTime,
  isProcessing,
  startAnswering,
  updateAnswer,
  isInterviewActive = false,
  // Phase 1: Removed requirement status props to simplify interview flow
}) {
  const [localAnswer, setLocalAnswer] = useState(question?.answer || "");
  
  // Get real-time audio amplitude data for waveform animation
  const amplitudeData = useAudioAnalyzer(isListening);

  // Update local answer when question changes (e.g., next question loads)
  useEffect(() => {
    setLocalAnswer(question?.answer || "");
  }, [question?.id]);

  // Dynamic button control logic based on actual interview length
  const totalQuestions = question?.totalQuestions || 10; // Get total questions from backend
  const currentQuestionNumber = currentIndex + 1;
  const isOnFinalQuestion = currentQuestionNumber === totalQuestions;

  const shouldEnableFinishInterview = () => {
    // Enable finish button ONLY on the final question
    return isOnFinalQuestion;
  };

  const shouldDisableNextQuestion = () => {
    // Disable next button ONLY on the final question
    return isOnFinalQuestion;
  };

  const getNextButtonTooltip = () => {
    if (isOnFinalQuestion) {
      return "This is the final question - please finish the interview";
    }
    return "Continue to next question";
  };

  const getNextButtonText = () => {
    if (isOnFinalQuestion) {
      return "Interview Complete";
    }
    return "Next Question";
  };

  const getFinishButtonTooltip = () => {
    if (isOnFinalQuestion) {
      return "Complete the interview - all questions answered";
    }
    return `Please complete all ${totalQuestions} questions (currently on question ${currentQuestionNumber})`;
  };

  // Function to get question type information for styling
  const getQuestionTypeInfo = () => {
    const type = (question?.type || "").toLowerCase();

    switch (type) {
      case "coding":
      case "technical":
        return {
          label: "Technical Question",
          icon: <Code className="h-5 w-5" />,
          color: "bg-purple-500",
          bgColor:
            "bg-purple-50 dark:bg-purple-900/20",
          borderColor: "border-purple-200 dark:border-purple-800",
        };
      case "behavioral":
      case "verbal":
        return {
          label: "Behavioral Question",
          icon: <MessageSquare className="h-5 w-5" />,
          color: "bg-green-500",
          bgColor:
            "bg-green-50 dark:bg-green-900/20",
          borderColor: "border-green-200 dark:border-green-800",
        };
      default:
        return {
          label: "Interview Question",
          icon: <MessageSquare className="h-5 w-5" />,
          color: "bg-blue-500",
          bgColor:
            "bg-blue-50 dark:bg-blue-900/20",
          borderColor: "border-blue-200 dark:border-blue-800",
        };
    }
  };

  const handleCodeMirrorChange = (value) => {
    if (!isInterviewActive) {
      return;
    }
    if (value && !question?.startTimestamp) {
      startAnswering();
    }
    setLocalAnswer(value);
  };

  const handleNextButtonClick = () => {
    // For technical/other questions, pass the local answer to the parent
    if (
      (question?.type || "").toLowerCase() === "coding" ||
      (question?.type || "").toLowerCase() === "other"
    ) {
      handleNextQuestion(localAnswer);
    } else {
      // For behavioral questions, the handleNextQuestion will manage its own audio transcription
      handleNextQuestion();
    }
  };

  const typeInfo = getQuestionTypeInfo();

  return (
    <div
      className={`border bg-white dark:bg-gray-900 ${typeInfo.borderColor} flex h-full max-h-screen flex-col overflow-hidden rounded-xl shadow-sm`}
    >
      {/* Header */}
      <div
        className={`${typeInfo.bgColor} border-b ${typeInfo.borderColor} flex-shrink-0 px-3 py-2 sm:py-3`}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 sm:gap-3">
            <div
              className={`${typeInfo.color} p-1.5 sm:p-2 rounded-lg`}
            >
              <div className="text-white">{typeInfo.icon}</div>
            </div>
            <div>
              <h3 className="sm:text-md text-sm font-semibold text-gray-900 dark:text-white font-sora">
                {typeInfo.label}
              </h3>
              <p className="text-xs text-gray-600 dark:text-gray-400 font-manrope">
                {question?.type === "behavioral"
                  ? "Share your experience and thoughts"
                  : question?.type === "technical"
                  ? "Write your code solution below"
                  : "Provide your detailed response"}
              </p>
            </div>
          </div>

          {/* Timer */}
          {!isSpeaking && remainingTime > 0 && (
            <div className="flex items-center gap-1 rounded-lg bg-white/80 px-2 py-1 backdrop-blur-sm dark:bg-gray-800/80 sm:gap-2 sm:px-3 sm:py-2">
              <Clock className="h-4 w-4 text-red-500 sm:h-5 sm:w-5" />
              <span className="text-sm font-bold text-red-600 dark:text-red-400 sm:text-lg">
                {remainingTime}s
              </span>
            </div>
          )}
        </div>
      </div>
      {/* Content Area - Responsive with proper height management */}
      <div className="flex-1 overflow-hidden p-3 sm:p-4 lg:p-6" style={{ position: "relative" }}>
        {/* Loader for transitioning/moving to next question */}
        {isProcessing && (
          <div className="flex h-full items-center justify-center bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 rounded-xl shadow-sm overflow-hidden absolute inset-0 z-10">
            <div className="text-center">
              <div className="mb-4">
                <div className="mx-auto h-12 w-12 animate-spin rounded-full border-4 border-blue-200 border-t-blue-600"></div>
              </div>
              <h2 className="mb-2 text-xl font-semibold text-blue-700 dark:text-blue-300 font-sora">
                Loading
              </h2>
              <p className="text-blue-600 dark:text-blue-400 font-manrope">
                Please wait...
              </p>
            </div>
          </div>
        )}
        {(question?.type || "").toLowerCase() === "behavioral" ||
        (question?.type || "").toLowerCase() === "verbal" ? (
          <div className="flex h-full flex-col items-center justify-center">
            {/* Show auto-recording message when AI is speaking */}
            {isSpeaking && (
              <div className="space-y-3 text-center sm:space-y-4">
                <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/50 sm:h-16 sm:w-16">
                  <Play className="h-6 w-6 text-blue-600 dark:text-blue-400 sm:h-8 sm:w-8" />
                </div>
                <div>
                  <p className="mb-1 text-base font-medium text-gray-900 dark:text-white sm:mb-2 sm:text-lg font-sora">
                    AI is narrating the question
                  </p>
                  <p className="text-xs text-gray-600 dark:text-gray-400 sm:text-sm font-manrope">
                    Recording will start automatically when narration ends
                  </p>
                </div>
              </div>
            )}

            {!isSpeaking && !isListening && (
              <div className="space-y-3 text-center sm:space-y-4">
                <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/50 sm:h-16 sm:w-16">
                  <Mic className="h-6 w-6 text-green-600 dark:text-green-400 sm:h-8 sm:w-8" />
                </div>
                {/* Removed Start Recording button as requested */}
              </div>
            )}

            {isListening && (
              <div className="space-y-3 text-center sm:space-y-4">
                <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/50 sm:h-20 sm:w-20">
                  <WaveformAnimation amplitudeData={amplitudeData} />
                </div>
                <div>
                  <p className="mb-1 text-lg font-semibold text-gray-900 dark:text-white sm:mb-2 sm:text-xl font-sora">
                    Listening...
                  </p>
                  <p className="text-xs text-gray-600 dark:text-gray-400 sm:text-sm font-manrope">
                    Speak clearly and take your time
                  </p>
                </div>
              </div>
            )}
          </div>
        ) : (question?.type || "").toLowerCase() === "coding" ||
          (question?.type || "").toLowerCase() === "technical" ||
          (question?.type || "").toLowerCase() === "other" ? (
          <div className="flex h-full min-h-0 flex-col">
            <div
              className="code-editor-mobile max-h-[60vh] min-h-[200px] flex-1 overflow-hidden rounded-lg border border-gray-700 p-1 sm:max-h-[50vh] sm:min-h-[250px] sm:p-2 lg:max-h-none lg:min-h-[300px]"
              style={{ backgroundColor: "#0d1117" }}
            >
              <Editor
                height="100%"
                language="javascript"
                theme="vs-dark"
                value={localAnswer}
                onChange={handleCodeMirrorChange}
                options={{
                  readOnly: !isInterviewActive,
                  lineNumbers: "on",
                  folding: true,
                  minimap: { enabled: false },
                  wordWrap: "on",
                  scrollBeyondLastLine: false,
                  automaticLayout: true,
                  fontSize:
                    typeof window !== "undefined" && window.innerWidth < 640
                      ? 12
                      : 14,
                }}
              />
            </div>
          </div>
        ) : (
          <div className="flex h-full items-center justify-center">
            <div className="space-y-4 text-center">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-gray-100 dark:bg-gray-800">
                <MessageSquare className="h-8 w-8 text-gray-500" />
              </div>
              <p className="text-gray-600 dark:text-gray-400 font-manrope">
                Unsupported question type: {question?.type}
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Footer Actions - Sticky positioning for better accessibility */}
      <div className="answer-input-footer flex-shrink-0 border-t border-gray-200 bg-white px-3 py-3 dark:border-gray-800 dark:bg-gray-900 sm:px-4 sm:py-4 lg:px-6">
        <div className="flex flex-col items-stretch justify-between gap-2 sm:flex-row sm:items-center sm:gap-4">
          <button
            onClick={handleNextButtonClick}
            disabled={
              isProcessing ||
              isSpeaking // Only disable if actually processing or speaking
              ? false
              : false ||
              !isInterviewActive ||
              shouldDisableNextQuestion() // Disable on final question
            }
            className={`flex items-center justify-center gap-1.5 rounded-md px-3 py-2 text-sm font-medium transition-colors duration-200 sm:px-4 sm:py-2.5 ${
              isProcessing ||
              isSpeaking ||
              !isInterviewActive ||
              shouldDisableNextQuestion()
                ? isOnFinalQuestion
                  ? "cursor-not-allowed bg-orange-100 text-orange-600 border-2 border-orange-300 dark:bg-orange-900/20 dark:text-orange-400"
                  : "cursor-not-allowed bg-gray-100 text-gray-400 dark:bg-gray-800"
                : "bg-blue-600 text-white shadow-sm hover:bg-blue-700"
            }`}
            title={getNextButtonTooltip()}
          >
            {isProcessing ? (
              <>
                <Loader2 className="h-3.5 w-3.5 animate-spin" />
                <span>Loading...</span>
              </>
            ) : isSpeaking ? (
              <>
                <span>{getNextButtonText()}</span>
                <ArrowRight className="h-3.5 w-3.5" />
              </>
            ) : isOnFinalQuestion ? (
              // Final question: Show completion indicator
              <>
                <span>{getNextButtonText()}</span>
                <span className="text-xs">🏁</span>
              </>
            ) : (
              // Regular questions: Show next arrow
              <>
                <span>{getNextButtonText()}</span>
                <ArrowRight className="h-3.5 w-3.5" />
              </>
            )}
          </button>

          <button
            onClick={(e) => {
              e.preventDefault();
              console.log("🏁 Finish Interview button clicked", {
                currentIndex,
                currentQuestionNumber,
                totalQuestions,
                isOnFinalQuestion,
                isEnabled: shouldEnableFinishInterview() && isInterviewActive,
                isInterviewActive,
              });
              if (shouldEnableFinishInterview()) {
                // Save the current answer before ending the interview
                if (
                  (question?.type || "").toLowerCase() === "coding" ||
                  (question?.type || "").toLowerCase() === "technical" ||
                  (question?.type || "").toLowerCase() === "other"
                ) {
                  handleNextQuestion(localAnswer);
                }
                onEndInterview();
              }
            }}
            disabled={
              !shouldEnableFinishInterview() ||
              !isInterviewActive ||
              isProcessing ||
              isSpeaking
            }
            className={`flex items-center justify-center gap-1.5 rounded-md px-3 py-2 text-sm font-medium transition-colors duration-200 sm:px-4 sm:py-2.5 ${
              !shouldEnableFinishInterview() ||
              !isInterviewActive ||
              isProcessing ||
              isSpeaking
                ? "cursor-not-allowed bg-gray-100 text-gray-400 dark:bg-gray-800"
                : isOnFinalQuestion
                  ? "bg-green-700 text-white shadow-lg hover:bg-green-800 ring-2 ring-green-300"
                  : "bg-green-600 text-white shadow-sm hover:bg-green-700"
            }`}
            title={getFinishButtonTooltip()}
          >
            <span className="text-center">
              Finish Interview{" "}
              {isOnFinalQuestion
                ? "🎉"
                : ""}
            </span>
            <LogOut className="h-3.5 w-3.5" />
          </button>
        </div>
      </div>
    </div>
  );
}
