import React from "react";
import { HiBriefcase, HiOutlineSparkles } from "react-icons/hi";
import { CheckCircle, X } from "lucide-react";

const JobRoleSkills = ({
  inviteDataToUse,
  jobRoleInput,
  handleJobRoleInput,
  setShowJobRoleSuggestions,
  handleJobRoleBlur,
  isAutoSelectingSkills,
  showJobRoleSuggestions,
  jobRoleSuggestions,
  jobRef,
  handleJobRoleSuggestionClick,
  skillInput,
  handleSkillInput,
  setShowSkillSuggestions,
  showSkillSuggestions,
  skillSuggestions,
  skillRef,
  addSkill,
  formData,
  clearAllSkills,
  skillSelectionMessage,
  removeSkill,
  completeResumeData,
  hasAutoSelected,
}) => (
  <div className="space-y-6">
    <div className="text-center">
      <div className="mx-auto mb-4 flex h-14 w-14 items-center justify-center rounded-lg bg-blue-50 border border-blue-100">
        <HiBriefcase className="h-7 w-7 text-blue-600" />
      </div>
      <h1 className="mb-2 text-2xl font-semibold text-gray-900">
        Job Role & Skills
      </h1>
      <p className="text-gray-600">
        {inviteDataToUse
          ? "Details from employer invitation"
          : "Tell us about your target role and expertise"}
      </p>
      {inviteDataToUse && (
        <div className="mt-4 rounded-lg border border-blue-200 bg-blue-50 p-4">
          <div className="flex items-center justify-center gap-3">
            <HiOutlineSparkles className="h-5 w-5 text-blue-600" />
            <span className="font-medium text-blue-800">
              Pre-filled from employer invitation
            </span>
          </div>
        </div>
      )}
    </div>

    <div className="space-y-6">
      {/* Job Role Input */}
      <div className="relative">
        <label
          htmlFor="jobRole"
          className="mb-2 block text-sm font-semibold text-gray-900"
        >
          Job Role *
        </label>
        <input
          required
          id="jobRole"
          name="jobRole"
          type="text"
          value={jobRoleInput}
          onChange={handleJobRoleInput}
          onFocus={() => setShowJobRoleSuggestions(true)}
          onBlur={handleJobRoleBlur}
          placeholder={
            isAutoSelectingSkills
              ? "Processing..."
              : "e.g., Frontend Developer, Data Scientist"
          }
          disabled={isAutoSelectingSkills}
          className={`w-full rounded-lg border px-4 py-3 transition-all duration-200 focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-100 ${
            isAutoSelectingSkills
              ? "cursor-not-allowed border-gray-200 bg-gray-100 text-gray-400"
              : "border-gray-300 bg-white text-gray-900 placeholder-gray-500"
          }`}
        />
        {showJobRoleSuggestions && jobRoleSuggestions.length > 0 && (
          <ul
            ref={jobRef}
            className="absolute z-10 mt-1 max-h-40 w-full overflow-y-auto rounded-lg border border-gray-200 bg-white shadow-lg"
          >
            {jobRoleSuggestions.map((suggestion, index) => (
              <li
                key={index}
                className="cursor-pointer border-b border-gray-100 px-4 py-3 text-gray-900 transition-colors last:border-b-0 hover:bg-blue-50"
                onClick={() => handleJobRoleSuggestionClick(suggestion)}
              >
                {suggestion}
              </li>
            ))}
          </ul>
        )}
      </div>

      {/* Enhanced Skills Section */}
      <div className="relative">
        <div className="mb-2 flex items-center justify-between">
          <label className="block text-sm font-semibold text-gray-900">
            Skills * ({formData.skills.length}/5 selected)
            {(completeResumeData?.skills?.primary_skills || hasAutoSelected) && (
              <span className="ml-2 text-xs font-medium text-blue-600">
                (AI-suggested skills included)
              </span>
            )}
          </label>
          {formData.skills.length > 0 && !isAutoSelectingSkills && (
            <button
              type="button"
              onClick={clearAllSkills}
              className="flex items-center gap-2 rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm text-gray-600 transition-colors hover:bg-gray-50"
              title="Clear all skills"
            >
              <X size={14} />
              Clear All
            </button>
          )}
        </div>

        {/* Loading indicator for skill selection */}
        {isAutoSelectingSkills && (
          <div className="mb-4 rounded-lg border border-blue-200 bg-blue-50 p-4">
            <div className="flex items-center gap-4">
              <div className="h-6 w-6 animate-spin rounded-full border-2 border-blue-600 border-t-transparent"></div>
              <div>
                <p className="font-medium text-blue-800">
                  {skillSelectionMessage}
                </p>
                <p className="text-sm text-blue-600">
                  Please wait while we process your skills...
                </p>
              </div>
            </div>
          </div>
        )}

        <input
          type="text"
          id="skill-input"
          value={skillInput}
          onChange={handleSkillInput}
          onFocus={() => setShowSkillSuggestions(true)}
          placeholder={
            isAutoSelectingSkills
              ? "Processing skills..."
              : "Type to search skills..."
          }
          disabled={isAutoSelectingSkills}
          className={`w-full rounded-lg border px-4 py-3 transition-all duration-200 focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-100 ${
            isAutoSelectingSkills
              ? "cursor-not-allowed border-gray-200 bg-gray-100 text-gray-400"
              : "border-gray-300 bg-white text-gray-900 placeholder-gray-500"
          }`}
          onKeyDown={(e) => {
            if (e.key === "Enter") {
              e.preventDefault();
              if (skillInput.trim()) {
                addSkill(skillInput.trim());
              }
            }
          }}
        />
        {/* Skills suggestions from database */}
        {showSkillSuggestions && skillSuggestions.length > 0 && (
          <div className="absolute z-10 mt-1 max-h-48 w-full overflow-y-auto rounded-lg border border-gray-200 bg-white shadow-lg">
            <div ref={skillRef}>
              {skillSuggestions.map((suggestion, index) => (
                <div
                  key={index}
                  className="cursor-pointer border-b border-gray-100 px-4 py-3 text-gray-900 transition-colors last:border-b-0 hover:bg-blue-50"
                  onClick={() => addSkill(suggestion)}
                >
                  {suggestion}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Selected Skills Display */}
        {formData.skills.length > 0 && (
          <div className="mt-4 rounded-lg border border-gray-200 bg-gray-50 p-4">
            <p className="mb-3 text-sm font-medium text-gray-700">
              Selected Skills ({formData.skills.length})
            </p>
            <div className="flex max-h-32 flex-wrap gap-2 overflow-y-auto">
              {formData.skills.map((skill, index) => (
                <span
                  key={index}
                  className="inline-flex items-center gap-2 rounded-full bg-blue-100 px-3 py-2 text-sm font-medium text-blue-700"
                >
                  {skill}
                  <button
                    type="button"
                    className="rounded-full p-0.5 transition-colors hover:bg-blue-200"
                    onClick={() => removeSkill(skill)}
                  >
                    <X size={14} />
                  </button>
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Enhanced Help text */}
        {formData.skills.length === 0 && (
          <div className="mt-4 rounded-lg border border-amber-200 bg-amber-50 p-3">
            <p className="text-sm text-amber-800">
              💡 Add at least one skill to continue
            </p>
          </div>
        )}
      </div>
    </div>
  </div>
);

export default JobRoleSkills;